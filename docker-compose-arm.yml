services:
  reverse_proxy:
    image: pickvocab-reverse_proxy:v1.1.0
    platform: linux/arm64
    build:
      context: ./
      target: reverse_proxy
    ports:
      - 443:443
    depends_on:
      - client
    volumes:
      - /home/<USER>/.acme.sh/:/root/.acme.sh/
      - /var/log/nginx/:/var/log/nginx/
      - backend_static:/var/www/api.pickvocab.com/static
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  client:
    image: pickvocab-client:v1.9.15
    platform: linux/arm64
    build:
      context: ./
      target: pickvocab-client
    environment:
      - NUXT_PUBLIC_API_URL=https://api.pickvocab.com
      - NUXT_PUBLIC_GOOGLE_REDIRECT_URL=https://pickvocab.com/auth/google/
      - NUXT_PUBLIC_GOOGLE_CLIENT_ID=1062383996574-bjuibq4tqq1ho90qabvtsh0035l9ncts.apps.googleusercontent.com
      - NODE_ENV=production
    depends_on:
      - backend
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  backend:
    image: pickvocab-backend:v1.8.5
    platform: linux/arm64
    build:
      context: ./pickvocab-server
    container_name: pickvocab-backend
    environment:
      - DB_NAME=pickvocab
      - DB_USER=duy
      - DB_PASSWORD=duy123
      - DB_HOST=db
      - DB_PORT=5432
      - GOOGLE_AUTHORIZED_REDIRECT_URI=https://pickvocab.com/auth/google/
      - EXTENSION_GOOGLE_AUTHORIZED_REDIRECT_URI=https://nfhhjfaahjkjdjbkpacapdblonogknag.chromiumapp.org/
      - PAGE_SIZE=20
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=django-db
      - DEBUG=False
      - DEVELOPMENT=False
    volumes:
      - backend_static:/app/static
    depends_on:
      - db
      - redis
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  celery_worker:
    image: pickvocab-backend:v1.8.5
    platform: linux/arm64
    build:
      context: ./pickvocab-server
    container_name: pickvocab-celery-worker
    command: celery -A pickvocab worker -l INFO
    environment:
      - DB_NAME=pickvocab
      - DB_USER=duy
      - DB_PASSWORD=duy123
      - DB_HOST=db
      - DB_PORT=5432
      - GOOGLE_AUTHORIZED_REDIRECT_URI=https://pickvocab.com/auth/google/
      - EXTENSION_GOOGLE_AUTHORIZED_REDIRECT_URI=https://nfhhjfaahjkjdjbkpacapdblonogknag.chromiumapp.org/
      - PAGE_SIZE=20
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=django-db
      - DEBUG=False
      - DEVELOPMENT=False
    depends_on:
      - db
      - redis
      - backend
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  flower:
    image: pickvocab-backend:v1.8.5
    platform: linux/arm64
    build:
      context: ./pickvocab-server
    container_name: pickvocab-flower
    command: celery -A pickvocab flower --port=5555 --basic-auth=phuongduyphan:Brian78183
    # ports:
    #   - 5555:5555
    environment:
      - DB_NAME=pickvocab
      - DB_USER=duy
      - DB_PASSWORD=duy123
      - DB_HOST=db
      - DB_PORT=5432
      - GOOGLE_AUTHORIZED_REDIRECT_URI=https://pickvocab.com/auth/google/
      - EXTENSION_GOOGLE_AUTHORIZED_REDIRECT_URI=https://nfhhjfaahjkjdjbkpacapdblonogknag.chromiumapp.org/
      - PAGE_SIZE=20
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=django-db
      - DEBUG=False
      - DEVELOPMENT=False
    depends_on:
      - redis
      - celery_worker
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    platform: linux/arm64
    container_name: pickvocab-redis
    volumes:
      - redis_data:/data
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  db:
    image: pgvector/pgvector:pg14
    platform: linux/arm64
    container_name: pickvocab-db
    ports:
      - 5432:5432
    environment:
      - POSTGRES_DB=pickvocab
      - POSTGRES_USER=duy
      - POSTGRES_PASSWORD=duy123
    volumes:
      - pg_data:/var/lib/postgresql/data
      - /backup:/backup
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  pg_data:
  backend_static:
  redis_data:
