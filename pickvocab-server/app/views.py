from app.filters import <PERSON><PERSON>omparison<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GenericCardFilterSet
from app.serializers import WritingRevisionHistorySerializer
from app.models import WritingRevision<PERSON>istory, WritingRevisionHistoryCards, GenericCard

# import ipdb
# from django.db import reset_queries, connection
# from pprint import pprint
from app.services.review_service import pick_cards_to_review
from rest_framework import permissions, viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db.models import Q, F, Value, Case, When, IntegerField, Prefetch
from django.db.models.functions import Greatest, Least
from django_filters import rest_framework as filters
from rest_framework.exceptions import ValidationError
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from slugify import slugify
from django.contrib.contenttypes.prefetch import GenericPrefetch
from django_celery_results.models import TaskResult
from .models import (
    ContextCard,
    Deck2,
    DefinitionCard,
    FeatureFlag,
    <PERSON>ric<PERSON>ard,
    LLMModel,
    Review,
    Review2,
    <PERSON>,
    Card,
    Deck,
    WordComparison,
    WordInContext,
    User<PERSON>mbeddingPreference,
    CustomTone, # Added CustomTone model
    EmbeddingModel,  # Added for embedding status endpoint
    WordDefinition,  # Added for embedding status endpoint
)
from .serializers import (
    ContextCardSerializer,
    Deck2Serializer,
    DeckWithCard2Serializer,
    DeckWithCardSerializer,
    DefinitionCardSerializer,
    FeatureFlagSerializer,
    # DefinitionCardSerializer,
    GenericCardSerializer,
    LLMModelSerializer,
    Review2Serializer,
    ReviewSerializer,
    WordComparisonSerializer,
    WordInContextSerializer,
    WordSerializer,
    CardSerializer,
    DeckSerializer,
    CustomToneSerializer, # Added CustomToneSerializer
)
from .permissions import IsAdminOrReadOnly, IsCreatorOrReadOnly, IsDemoCardReadonly, IsDemoDeck, IsOwner, IsOwnerOrReadOnly
from app.services.card_embedding import calculate_and_store_embedding_for_card
from app.services.card_embedding.tasks import similarity_search_task
import json


# Create your views here.
class WordViewSet(viewsets.ModelViewSet):
    queryset = Word.objects.all().order_by("-is_verified")
    serializer_class = WordSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.DjangoFilterBackend]
    filterset_fields = ["word", "slug"]

    # def get_object(self):
    #     try:
    #         return Word.objects.get(word=self.kwargs['word'])
    #     except Word.DoesNotExist:
    #         word = lookup_word(self.kwargs['word'])
    #         print(self.request.user)
    #         if not self.request.user.is_anonymous:
    #             word.owner = self.request.user
    #         word.save()
    #         return word

    def perform_create(self, serializer):
        if self.request.user.is_authenticated:
            serializer.save(
                creator=self.request.user, slug=slugify(self.request.data["word"])
            )
        else:
            serializer.save()

    @action(detail=False, methods=["get"])
    def search(self, request, *args, **kwargs):
        text = request.query_params.get("text")
        # words = Word.objects.filter(Q(word__istartswith=text) | Q(word__search=text)).order_by('word').distinct('word')[:20]
        words = (
            Word.objects.annotate(
                match_priority=Case(
                    When(word__istartswith=text, then=Value(3)),
                    When(word__search=text, then=Value(2)),
                    When(word__icontains=text, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField(),
                )
            )
            .filter(match_priority__gt=0)
            .order_by("-match_priority", "word")
            .distinct("match_priority", "word")[:20]
        )
        words = [word.word for word in words]
        return Response(words)


class CardViewSet(viewsets.ModelViewSet):
    serializer_class = CardSerializer
    permission_classes = [(permissions.IsAuthenticated & IsOwner) | IsDemoCardReadonly]
    # Card can be view when it belongs to demo deck. Thus, the queryset is not filtered by owner.
    queryset = Card.objects.all().select_related("owner", "word_ref")
    filter_backends = [filters.DjangoFilterBackend]
    filterset_fields = ["word", "owner", "decks__is_demo"]

    def get_queryset(self):
        # In some cases, a card meets both conditions, leading to a queryset with two identical objects.
        # Error: "get() returned more than one Card -- it returned 2!"
        return self.queryset.filter(
            Q(owner=self.request.user) | Q(decks__is_demo=True)
        ).distinct()

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

    @action(detail=False, methods=["get"])
    def search(self, request, *args, **kwargs):
        text = request.query_params.get("text")
        # cards = Card.objects.filter(Q(word__istartswith=text) | Q(word__search=text), owner=self.request.user)
        # if not cards.exists():
        #     cards = Card.objects.filter(Q(word__icontains=text), owner=self.request.user)
        cards = (
            Card.objects.annotate(
                match_priority=Case(
                    When(word__istartswith=text, then=Value(3)),
                    When(word__search=text, then=Value(2)),
                    When(word__icontains=text, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField(),
                )
            )
            .filter(
                match_priority__gt=0,
                owner=self.request.user,  # only return cards owned by the user (not demo cards)
            )
            .order_by("-match_priority")[:20]
        )
        serializer = CardSerializer(cards, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["put"])
    def update_definition(self, request, pk=None):
        card: Card = self.get_object()
        definition = request.data["definition"]
        card.definition = definition
        card.save()
        serializer = CardSerializer(card)
        return Response(serializer.data)


class DeckViewSet(viewsets.ModelViewSet):
    serializer_class = DeckSerializer
    permission_classes = [(permissions.IsAuthenticated & IsOwner) | IsDemoDeck]
    # Deck can be publicly viewed when it's a demo deck. Thus, the queryset is not filtered by owner.
    queryset = Deck.objects.all().select_related("owner")
    filter_backends = [filters.DjangoFilterBackend]
    filterset_fields = ["name", "is_demo", "owner"]

    def get_queryset(self):
        # Same reason as Card queryset
        return self.queryset.filter(
            Q(owner=self.request.user) | Q(is_demo=True)
        ).distinct()

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["pagination_class"] = self.pagination_class
        return context

    def retrieve(self, request, *args, **kwargs):
        # reset_queries()
        deck = self.get_object()
        serializer = DeckWithCardSerializer(deck, context=self.get_serializer_context())
        serializer.data
        # pprint(connection.queries)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def search(self, request, *args, **kwargs):
        text = request.query_params.get("text")
        # decks = Deck.objects.filter(Q(name__istartswith=text) | Q(name__search=text), owner=self.request.user).distinct()
        # if not decks.exists():
        #     decks = Deck.objects.filter(Q(name__icontains=text), owner=self.request.user)
        decks = (
            Deck.objects.annotate(
                match_priority=Case(
                    When(name__istartswith=text, then=Value(3)),
                    When(name__search=text, then=Value(2)),
                    When(name__icontains=text, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField(),
                )
            )
            .filter(
                match_priority__gt=0,
                owner=self.request.user,  # only return decks that the user owns (not demo decks)
            )
            .order_by("-match_priority")[:20]
        )
        serializer = DeckSerializer(decks, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def add_cards(self, request, pk=None):
        deck = self.get_object()
        card_ids = request.data["cards"]
        deck.cards.add(*card_ids)
        serializer = DeckWithCardSerializer(deck, context=self.get_serializer_context())
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def remove_cards(self, request, pk=None):
        deck = self.get_object()
        card_ids = request.data["cards"]
        deck.cards.remove(*card_ids)
        serializer = DeckWithCardSerializer(deck, context=self.get_serializer_context())
        return Response(serializer.data)


class ReviewViewSet(viewsets.ModelViewSet):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwner]

    def get_queryset(self):
        current_user = self.request.user
        return Review.objects.filter(owner=current_user)

    def perform_create(self, serializer):
        cards = pick_cards_to_review(
            self.request.user,
            self.request.data["decks"],
            Card,
            Deck,
            self.request.data.get("is_master", False),
        )
        serializer.save(owner=self.request.user, cards=cards)

    # payload:
    # {
    #   cards: [
    #     {
    #       id: 2,
    #       delta_score: 1
    #     },
    #     {
    #       id: 3,
    #       delta_score: -1
    #     }
    #   ]
    # }
    @action(detail=True, methods=["put"])
    def update_score(self, request, pk=None):
        # reset_queries()
        review = self.get_object()
        review_cards_payload = request.data["review_cards"]

        review_cards = review.reviewcard_set.filter(
            card_id__in=[
                card_payload["card_id"] for card_payload in review_cards_payload
            ]
        ).select_related("card")

        for review_card_payload in review_cards_payload:
            review_card = next(
                c for c in review_cards if c.card_id == review_card_payload["card_id"]
            )
            review_card.delta_score = review_card_payload["delta_score"]

        review_cards.bulk_update(review_cards, ["delta_score"])
        serializer = ReviewSerializer(review)

        cards = [review_card.card for review_card in review_cards]
        for i, card in enumerate(cards):
            card.progress_score = Greatest(
                Least(F("progress_score") + review_cards[i].delta_score, Value(5)),
                Value(0),
            )

        Card.objects.bulk_update(cards, ["progress_score"])

        # pprint(connection.queries)
        return Response(serializer.data)


class LLMModelViewSet(viewsets.ModelViewSet):
    queryset = LLMModel.objects.all().order_by("provider")
    serializer_class = LLMModelSerializer
    permission_classes = [IsAdminOrReadOnly]
    pagination_class = None


class WordComparisonViewSet(viewsets.ModelViewSet):
    queryset = WordComparison.objects.all()
    serializer_class = WordComparisonSerializer
    permission_classes = [IsCreatorOrReadOnly]
    filter_backends = [WordComparisonFilterBackend]
    filterset_fields = ["words"]

    def perform_create(self, serializer):
        words = serializer.validated_data["words"].split(",")
        words.sort()
        wordString = ",".join(words)

        if self.request.user.is_authenticated:
            serializer.save(creator=self.request.user, words=wordString)
        else:
            serializer.save(words=wordString)


class WordInContextViewSet(viewsets.ModelViewSet):
    queryset = WordInContext.objects.all()
    serializer_class = WordInContextSerializer
    permission_classes = [IsCreatorOrReadOnly]
    filter_backends = [filters.DjangoFilterBackend]
    filterset_fields = ["word"]

    def perform_create(self, serializer):
        if self.request.user.is_authenticated:
            serializer.save(creator=self.request.user)
        else:
            serializer.save()

    @action(detail=False, methods=["post"])
    def lookup_pair(self, request, *args, **kwargs):
        word = request.data.get("word")
        context = request.data.get("context")
        results = WordInContext.objects.filter(word=word, context=context)
        serializer = WordInContextSerializer(results, many=True)
        return Response(serializer.data)


class GenericCardViewSet(viewsets.ModelViewSet):
    serializer_class = GenericCardSerializer
    permission_classes = [(permissions.IsAuthenticated & IsOwner) | IsDemoCardReadonly]
    queryset = (
        GenericCard.objects.all()
        .prefetch_related(
            GenericPrefetch(
                "card",
                [
                    DefinitionCard.objects.all().select_related("word_ref"),
                    ContextCard.objects.all().select_related("word_in_context"),
                ],
            ),
        )
        .select_related("card_content_type")
        .order_by("-created_at")
    )
    filter_backends = [filters.DjangoFilterBackend]
    filterset_class = GenericCardFilterSet

    def get_queryset(self):
        return self.queryset.filter(
            Q(owner=self.request.user) | Q(decks__is_demo=True)
        ).distinct()

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

    @action(detail=True, methods=["get"])
    def embedding_status(self, request, pk=None):
        """
        Check if a GenericCard has an embedding calculated for it.
        
        This endpoint considers the user's active embedding preference
        (UserEmbeddingPreference) and falls back to a default model if no preference is set.
        """
        # Get the GenericCard instance
        card = self.get_object()
        
        # Determine the target EmbeddingModel
        try:
            # Try fetching UserEmbeddingPreference for the user
            preference = UserEmbeddingPreference.objects.filter(
                user=request.user,
                is_active=True
            ).first()
            
            if preference and preference.embedding_model:
                target_model = preference.embedding_model
            else:
                # Fall back to default model (Gemini text-embedding-004)
                target_model = EmbeddingModel.objects.get(
                    provider=EmbeddingModel.EmbeddingProvider.Gemini,
                    model_name="models/text-embedding-004"
                )
                
        except EmbeddingModel.DoesNotExist:
            return Response(
                {"detail": "Default embedding model not found in database."},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get the specific card instance (DefinitionCard or ContextCard)
        specific_card = card.card
        
        # Determine the content object type and get the related object
        if isinstance(specific_card, DefinitionCard):
            if not specific_card.word_ref:
                return Response(
                    {"detail": "Definition card has no word reference."},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Get the WordDefinition for this card
            content_object = WordDefinition.objects.filter(
                word=specific_card.word_ref,
                word_def_idx=specific_card.word_def_idx
            ).first()
            
            if not content_object:
                return Response(
                    {"detail": "Word definition not found."},
                    status=status.HTTP_404_NOT_FOUND
                )
                
        elif isinstance(specific_card, ContextCard):
            if not specific_card.word_in_context:
                return Response(
                    {"detail": "Context card has no word in context."},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            content_object = specific_card.word_in_context
        else:
            return Response(
                {"detail": f"Unsupported card type: {type(specific_card)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Get the appropriate field name based on embedding model
        if target_model.provider == EmbeddingModel.EmbeddingProvider.Gemini:
            # both WordDefinition and WordInContext have this field
            vector_field_name = "gemini_text_embedding_004_vector"
        elif target_model.provider == EmbeddingModel.EmbeddingProvider.Mistral:
            # both WordDefinition and WordInContext have this field
            vector_field_name = "mistral_embed_vector"
        else:
            return Response(
                {"detail": f"Unsupported embedding provider: {target_model.provider}"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Access the related manager for the embedding
        related_manager = getattr(content_object, vector_field_name)
        
        # Check if an embedding exists
        has_embedding = related_manager.exists()
        
        # Construct the response payload
        payload = {
            "has_embedding": has_embedding,
            "model_provider": target_model.provider,
            "model_name": target_model.model_name
        }
        
        return Response(payload, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def create_definition_card(self, request, *args, **kwargs):
        definition_serializer = DefinitionCardSerializer(data=request.data)
        if definition_serializer.is_valid(raise_exception=True):
            with transaction.atomic():
                definition_card = definition_serializer.save()
                generic_card_data = {
                    "card_content_type": ContentType.objects.get_for_model(
                        DefinitionCard
                    ).id,
                    "card_object_id": definition_card.id,
                    "owner": self.request.user,
                }
                generic_serializer = GenericCardSerializer(
                    data=generic_card_data, context={"request": request}
                )
                if generic_serializer.is_valid(raise_exception=True):
                    generic_card = generic_serializer.save(owner=self.request.user)

                    if FeatureFlag.is_enabled('revise_with_embedding', request.user.id):
                        # get user's embedding model
                        embedding_model = UserEmbeddingPreference.objects.filter(
                            user=request.user,
                        ).first()
                        if embedding_model:
                            calculate_and_store_embedding_for_card.delay(
                                card_id=generic_card.id,
                                provider=embedding_model.provider,
                                model_name=embedding_model.model_name,
                                embedding_model_id=embedding_model.id,
                            )
                        else:
                            calculate_and_store_embedding_for_card.delay(
                                card_id=generic_card.id,
                            )
                    
                    return Response(
                        GenericCardSerializer(generic_card).data,
                        status=status.HTTP_201_CREATED,
                    )
                else:
                    # If generic_serializer is invalid, the transaction will rollback,
                    # and definition_card will not be committed.
                    raise ValidationError(generic_serializer.errors)
        return Response(
            definition_serializer.errors, status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=["post"])
    def create_context_card(self, request, *args, **kwargs):
        context_serializer = ContextCardSerializer(data=request.data)
        if context_serializer.is_valid(raise_exception=True):
            with transaction.atomic():
                context_card = context_serializer.save()
                generic_card_data = {
                    "card_content_type": ContentType.objects.get_for_model(
                        ContextCard
                    ).id,
                    "card_object_id": context_card.id,
                    "owner": self.request.user,
                }
                generic_serializer = GenericCardSerializer(
                    data=generic_card_data, context={"request": request}
                )
                if generic_serializer.is_valid(raise_exception=True):
                    generic_card = generic_serializer.save(owner=self.request.user)

                    if FeatureFlag.is_enabled('revise_with_embedding', request.user.id):
                        # get user's embedding model
                        embedding_model = UserEmbeddingPreference.objects.filter(
                            user=request.user,
                        ).first()
                        if embedding_model:
                            calculate_and_store_embedding_for_card.delay(
                                card_id=generic_card.id,
                                provider=embedding_model.provider,
                                model_name=embedding_model.model_name,
                                embedding_model_id=embedding_model.id,
                            )
                        else:
                            calculate_and_store_embedding_for_card.delay(
                                card_id=generic_card.id,
                            )

                    return Response(
                        GenericCardSerializer(generic_card).data,
                        status=status.HTTP_201_CREATED,
                    )
                else:
                    # If generic_serializer is invalid, the transaction will rollback,
                    # and context_card will not be committed.
                    raise ValidationError(generic_serializer.errors)
        return Response(context_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["put"])
    def update_definition_card(self, request, pk=None):
        generic_card = self.get_object()

        if not isinstance(generic_card.card, DefinitionCard):
            return Response(
                {"detail": "This GenericCard does not contain a DefinitionCard."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        definition_card = generic_card.card
        definition_serializer = DefinitionCardSerializer(
            definition_card, data=request.data, partial=True
        )
        if definition_serializer.is_valid(raise_exception=True):
            definition_card = definition_serializer.save()
            # You might want to serialize and return the GenericCard here
            generic_card_serializer = GenericCardSerializer(generic_card)
            return Response(generic_card_serializer.data)
        return Response(
            definition_serializer.errors, status=status.HTTP_400_BAD_REQUEST
        )

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        with transaction.atomic():
            # Delete the associated card first
            if instance.card:
                instance.card.delete()

            # Then delete the GenericCard instance
            self.perform_destroy(instance)

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=["get"])
    def search(self, request, *args, **kwargs):
        text = request.query_params.get("text")

        cards = (
            self.get_queryset()
            .annotate(
                match_priority=Case(
                    When(definition_card__word__istartswith=text, then=Value(3)),
                    When(
                        context_card__word_in_context__word__istartswith=text,
                        then=Value(3),
                    ),
                    When(definition_card__word__search=text, then=Value(2)),
                    When(
                        context_card__word_in_context__word__search=text, then=Value(2)
                    ),
                    # When(definition_card__word__icontains=text, then=Value(1)),
                    # When(context_card__word_in_context__word__icontains=text, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField(),
                )
            )
            .filter(match_priority__gt=0, owner=self.request.user)
            .order_by("-match_priority")[:20]
        )
        serializer = GenericCardSerializer(cards, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["post"])
    def start_similarity_search(self, request, *args, **kwargs):
        """
        Start an asynchronous similarity search task.
        
        This endpoint accepts a query string and starts a Celery task to perform
        a similarity search on the user's cards. It returns a task ID that can
        be used to retrieve the results later.

        Request Body:
        {
            "query": "string", # Required: The query to search for
            "limit": 10,       # Optional: Maximum number of results (default: 10)
            "threshold": 0.0   # Optional: Minimum similarity score (default: 0.0)
        }

        Response:
        {
            "task_id": "string",      # The ID of the created task
            "status": "string",       # Current status of the task (PENDING)
            "message": "string"       # A message indicating the task has been started
        }
        """
        query = request.data.get("query")
        limit = int(request.data.get("limit", 10))
        threshold = float(request.data.get("threshold", 0.0))
        
        # Validate the query
        if not query or not isinstance(query, str) or not query.strip():
            return Response(
                {"error": "Query parameter is required and must be a non-empty string"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get user's embedding model if available
        embedding_kwargs = {}
        if FeatureFlag.is_enabled('revise_with_embedding', request.user.id):
            embedding_model = UserEmbeddingPreference.objects.filter(
                user=request.user,
            ).first()
            
            if embedding_model:
                embedding_kwargs = {
                    "provider": embedding_model.provider,
                    "model_name": embedding_model.model_name,
                    "embedding_model_id": embedding_model.id,
                }
        
        # Start the similarity search task
        task = similarity_search_task.delay(
            query=query,
            user_id=request.user.id,
            limit=limit,
            threshold=threshold,
            **embedding_kwargs
        )
        
        return Response({
            "task_id": task.id,
            "status": "PENDING",
            "message": "Similarity search task has been started"
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=False, methods=["get"])
    def get_similarity_search_results(self, request, *args, **kwargs):
        """
        Get the results of a previously started similarity search task.
        
        This endpoint requires a task_id query parameter and returns the results
        of the similarity search if the task has completed successfully.

        Query Parameters:
        - task_id: The ID of the task to retrieve results for

        Response:
        {
            "status": "string",      # Status of the task (PENDING, SUCCESS, FAILURE)
            "results": [],           # List of search results (only if status is SUCCESS)
            "error": "string"        # Error message (only if status is FAILURE)
        }
        """
        task_id = request.query_params.get("task_id")
        
        # Validate the task_id
        if not task_id:
            return Response(
                {"error": "task_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get the task result from the database
            task_result = TaskResult.objects.get(task_id=task_id)
            
            # Check the task state
            if task_result.status == "SUCCESS":
                # If the task was successful, return the results
                # The result is stored as a JSON string, so parse it
                result_data = task_result.result
                if isinstance(result_data, str):
                    result_data = json.loads(result_data)
                
                # Verify the user ID in the result matches the requesting user
                if "user_id" in result_data and result_data["user_id"] != request.user.id:
                    return Response(
                        {"error": "You are not authorized to access these results"},
                        status=status.HTTP_403_FORBIDDEN
                    )
                
                return Response({
                    "status": task_result.status,
                    "results": result_data,
                }, status=status.HTTP_200_OK)
            elif task_result.status == "FAILURE":
                # If the task failed, return the error
                return Response({
                    "status": task_result.status,
                    "error": task_result.result,
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                # If the task is still pending or in progress
                return Response({
                    "status": task_result.status,
                    "message": f"Task is in state: {task_result.status}"
                }, status=status.HTTP_200_OK)
                
        except TaskResult.DoesNotExist:
            return Response(
                {"error": f"No task found with ID: {task_id}"},
                status=status.HTTP_404_NOT_FOUND
            )


class Deck2ViewSet(viewsets.ModelViewSet):
    serializer_class = Deck2Serializer
    permission_classes = [(permissions.IsAuthenticated & IsOwner) | IsDemoDeck]
    # Deck can be publicly viewed when it's a demo deck. Thus, the queryset is not filtered by owner.
    queryset = Deck2.objects.all().select_related("owner").order_by("-created_at")
    filter_backends = [filters.DjangoFilterBackend]
    filterset_fields = ["name", "is_demo", "owner"]

    def get_queryset(self):
        # Same reason as Card queryset
        return self.queryset.filter(
            Q(owner=self.request.user) | Q(is_demo=True)
        ).distinct()

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["pagination_class"] = self.pagination_class
        return context

    def retrieve(self, request, *args, **kwargs):
        # reset_queries()
        deck = self.get_object()
        serializer = DeckWithCard2Serializer(
            deck, context=self.get_serializer_context()
        )
        serializer.data
        # pprint(connection.queries)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def search(self, request, *args, **kwargs):
        text = request.query_params.get("text")
        
        # If no text provided, return empty results
        if not text or not text.strip():
            return Response([])
        
        # Build the queryset with priority-based matching
        queryset = (
            Deck2.objects.annotate(
                match_priority=Case(
                    When(name__istartswith=text, then=Value(3)),
                    When(name__search=text, then=Value(2)),
                    # When(name__icontains=text, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField(),
                )
            )
            .filter(
                match_priority__gt=0,
                owner=self.request.user,  # only return decks that the user owns (not demo decks)
            )
            .order_by("-match_priority")
        )
        
        # Check if pagination is requested via 'page' parameter
        page = request.query_params.get("page")
        if page:
            # Use pagination for infinite scroll
            page_obj = self.paginate_queryset(queryset)
            if page_obj is not None:
                serializer = Deck2Serializer(page_obj, many=True)
                return self.get_paginated_response(serializer.data)
        
        # Fallback to original behavior (limit 20) for backward compatibility
        decks = queryset[:20]
        serializer = Deck2Serializer(decks, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def add_cards(self, request, pk=None):
        deck = self.get_object()
        card_ids = request.data["cards"]
        deck.cards.add(*card_ids)
        serializer = DeckWithCard2Serializer(deck, context=self.get_serializer_context())
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def remove_cards(self, request, pk=None):
        deck = self.get_object()
        card_ids = request.data["cards"]
        deck.cards.remove(*card_ids)
        serializer = DeckWithCard2Serializer(deck, context=self.get_serializer_context())
        return Response(serializer.data)


class Review2ViewSet(viewsets.ModelViewSet):
    serializer_class = Review2Serializer
    permission_classes = [permissions.IsAuthenticated & IsOwner]

    def get_queryset(self):
        current_user = self.request.user
        return Review2.objects.filter(owner=current_user).order_by("-created_at")

    def perform_create(self, serializer):
        cards = pick_cards_to_review(
            self.request.user,
            self.request.data["decks"],
            GenericCard,
            Deck2,
            self.request.data.get("is_master", False),
        )
        print(cards)
        serializer.save(owner=self.request.user, cards=cards)

    # payload:
    # {
    #   cards: [
    #     {
    #       id: 2,
    #       delta_score: 1
    #     },
    #     {
    #       id: 3,
    #       delta_score: -1
    #     }
    #   ]
    # }
    @action(detail=True, methods=["put"])
    def update_score(self, request, pk=None):
        # reset_queries()
        review = self.get_object()
        review_cards_payload = request.data["review_cards"]

        review_cards = review.reviewgenericcard_set.filter(
            card_id__in=[
                card_payload["card_id"] for card_payload in review_cards_payload
            ]
        ).select_related("card")

        for review_card_payload in review_cards_payload:
            review_card = next(
                c for c in review_cards if c.card_id == review_card_payload["card_id"]
            )
            review_card.delta_score = review_card_payload["delta_score"]

        review_cards.bulk_update(review_cards, ["delta_score"])
        serializer = Review2Serializer(review)

        cards = [review_card.card for review_card in review_cards]
        for i, card in enumerate(cards):
            card.progress_score = Greatest(
                Least(F("progress_score") + review_cards[i].delta_score, Value(5)),
                Value(0),
            )

        GenericCard.objects.bulk_update(cards, ["progress_score"])

        # pprint(connection.queries)
        return Response(serializer.data)


class FeatureFlagViewSet(viewsets.ModelViewSet):
    queryset = FeatureFlag.objects.all()
    serializer_class = FeatureFlagSerializer
    permission_classes = [IsAdminOrReadOnly]
    pagination_class = None


class WritingRevisionHistoryViewSet(viewsets.ModelViewSet):
    serializer_class = WritingRevisionHistorySerializer
    permission_classes = [permissions.IsAuthenticated, IsOwner]

    def get_queryset(self):
        return WritingRevisionHistory.objects.filter(owner=self.request.user).order_by("-created_at")

    def perform_create(self, serializer):
        # Always associate with current user
        history = serializer.save(owner=self.request.user)

        # Get the card IDs directly from the request data
        card_ids = self.request.data.get("cardIds", [])
        
        if card_ids:
            # Create WritingRevisionHistoryCards entries
            cards = GenericCard.objects.filter(id__in=card_ids)
            WritingRevisionHistoryCards.objects.bulk_create([
                WritingRevisionHistoryCards(history=history, card=card)
                for card in cards
            ])
        else:
            # Fallback to the old method if cardIds is not provided
            # Extract all vocab card IDs used in any of the 3 revisions
            revisions = history.revisions or []
            card_ids = set()
            for rev in revisions:
                ids = rev.get("user_vocabularies_used", [])
                if ids:
                    card_ids.update(ids)

            # Create WritingRevisionHistoryCards entries
            cards = GenericCard.objects.filter(id__in=card_ids)
            WritingRevisionHistoryCards.objects.bulk_create([
                WritingRevisionHistoryCards(history=history, card=card)
                for card in cards
            ])

    def perform_destroy(self, instance):
        # Delete related card links first
        instance.cards.all().delete()
        # Then delete the history instance
        instance.delete()


# ViewSet for CustomTone model
class CustomToneViewSet(viewsets.ModelViewSet):
    serializer_class = CustomToneSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwner]

    def get_queryset(self):
        """
        This view should return a list of all the custom tones
        for the currently authenticated user.
        """
        user = self.request.user
        return CustomTone.objects.filter(owner=user)

    def perform_create(self, serializer):
        """
        Ensure the owner is set to the current user upon creation.
        """
        serializer.save(owner=self.request.user)