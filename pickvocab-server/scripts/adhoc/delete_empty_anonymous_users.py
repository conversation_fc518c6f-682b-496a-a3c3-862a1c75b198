from django.contrib.auth import get_user_model
from django.db import models
from app.models import (
    Generic<PERSON>ard, Deck2, WritingRevisionHistory, Card, Deck, Review, Review2,
    UserEmbeddingPreference, CustomTone
)

User = get_user_model()


def has_cascade_records(user):
    """Check if user has any records that would prevent deletion (CASCADE relationships)."""
    cascade_checks = [
        GenericCard.objects.filter(owner=user).exists(),
        Deck2.objects.filter(owner=user).exists(),
        WritingRevisionHistory.objects.filter(owner=user).exists(),
        Card.objects.filter(owner=user).exists(),
        Deck.objects.filter(owner=user).exists(),
        Review.objects.filter(owner=user).exists(),
        Review2.objects.filter(owner=user).exists(),
        UserEmbeddingPreference.objects.filter(user=user).exists(),
        CustomTone.objects.filter(owner=user).exists(),
    ]
    
    return any(cascade_checks)


def up():
    """Delete anonymous users that don't have any CASCADE-protected records."""
    anonymous_users = User.objects.filter(is_annonymous=True).filter(
        models.Q(email__isnull=True) | models.Q(email='')
    )
    total_users = anonymous_users.count()
    
    print(f"Found {total_users} anonymous users to analyze")
    
    deleted_count = 0
    skipped_count = 0
    
    for user in anonymous_users:
        print(f"Processing user: {user.username} (ID: {user.id})")
        
        if has_cascade_records(user):
            print("  Skipped: User has CASCADE records")
            skipped_count += 1
        else:
            user.delete()
            print("  Deleted: User had no CASCADE records")
            deleted_count += 1
    
    print(f"Summary: {deleted_count} users deleted, {skipped_count} users skipped")


def down():
    """No rollback possible for user deletion."""
    print("Cannot rollback user deletions")


def run():
    up()