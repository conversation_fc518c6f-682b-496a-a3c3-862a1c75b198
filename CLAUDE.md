# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Pickvocab is an AI-powered vocabulary learning platform with three main components:
- **pickvocab-client**: Nuxt.js web application 
- **pickvocab-server**: Django REST API with PostgreSQL and vector embeddings
- **pickvocab-web-extension**: Cross-browser extension built with WXT framework
- **packages/dictionary**: Shared library for LLM integrations

## Architecture

### Monorepo Structure
- Uses pnpm workspaces for package management
- Individual packages can be developed and built independently
- Shared dictionary package used by both client and extension

### Tech Stack
- **Frontend**: Nuxt.js 3, Vue.js, TailwindCSS, Shadcn/ui, TipTap editor
- **Backend**: Django 5.0, Django REST Framework, PostgreSQL with pgvector, Celery, Redis
- **Extension**: WXT framework (Vue.js-based)
- **AI/LLM**: Multiple providers (OpenAI, Google, Anthropic, Groq, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)

## Development Commands

### Root Level
```bash
# Install all dependencies
pnpm install

# Build all packages
pnpm run build
```

### Client (pickvocab-client/)
```bash
# Development server
pnpm dev

# Production build
pnpm build

# Preview production build
pnpm preview

# Generate static site
pnpm generate
```

### Server (pickvocab-server/)
```bash
# Install dependencies
poetry install

# Run development server
python manage.py runserver

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Start Celery worker (for background tasks)
celery -A pickvocab worker -l INFO

# Start Celery beat (for scheduled tasks)
celery -A pickvocab beat -l INFO

# Start Flower (Celery monitoring)
celery -A pickvocab flower --port=5555
```

### Web Extension (pickvocab-web-extension/)
```bash
# Development (Chrome)
pnpm dev

# Development (Firefox)
pnpm dev:firefox

# Development (Edge)
pnpm dev:edge

# Build for production
pnpm build

# Build for Firefox
pnpm build:firefox

# Create extension package
pnpm zip
```

### Dictionary Package (packages/dictionary/)
```bash
# Build shared library
pnpm build

# Development mode
pnpm dev
```

## Docker Development

### Full Stack Development
```bash
# Start all services
docker compose up

# Build and start services
docker compose up --build

# Start specific service
docker compose up client
docker compose up backend

# View logs
docker compose logs -f [service_name]
```

### ARM Development (for Apple Silicon)
```bash
# Use ARM-specific compose file
docker compose -f docker-compose-arm.yml up
```

## Key Application Features

### Database Architecture
- PostgreSQL with pgvector extension for semantic search
- User management with Google OAuth integration
- Word embeddings for similarity search
- Background task processing with Celery

### AI Integration
- Multiple LLM providers with unified interface
- Contextual word definitions and explanations
- Writing assistance with vocabulary integration
- Vector embeddings for semantic similarity

### Client Features
- Epub reader with integrated dictionary
- Vocabulary notebooks and spaced repetition
- AI-powered writing assistant
- Multi-language support

### Extension Features
- Context-aware word lookup on any webpage
- Seamless vocabulary saving
- Writing assistant integration
- Cross-browser compatibility

## Development Patterns

### API Structure
- Django REST Framework with pagination
- Token-based authentication
- CORS enabled for cross-origin requests
- Background task processing for AI operations

### Frontend Patterns
- Nuxt.js with SSR/SPA capabilities
- Pinia for state management with persistence
- Component-based architecture with Shadcn/ui
- Responsive design with TailwindCSS

### Extension Patterns
- WXT framework for cross-browser development
- Content scripts for page interaction
- Background scripts for API communication
- Popup interface for user settings

## Environment Configuration

### Client Environment Variables
- `NUXT_PUBLIC_API_URL`: Backend API URL
- `NUXT_PUBLIC_GOOGLE_CLIENT_ID`: Google OAuth client ID
- `NUXT_PUBLIC_GOOGLE_REDIRECT_URL`: OAuth redirect URL

### Server Environment Variables
- Database: `DB_NAME`, `DB_USER`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`
- OAuth: `GOOGLE_AUTHORIZED_REDIRECT_URI`
- Celery: `CELERY_BROKER_URL`, `CELERY_RESULT_BACKEND`
- Debug: `DEBUG`, `DEVELOPMENT`

## Deployment

### Production Stack
- Nginx reverse proxy with SSL
- Docker Compose orchestration
- PostgreSQL with pgvector for embeddings
- Redis for caching and task queue
- Celery workers for background processing
- Flower for task monitoring

### Deployment Process
1. SSH to production server
2. Pull latest code from master branch
3. Check environment configuration
4. Run `docker compose build`
5. Run `docker compose up`

## Testing and Quality

### Server Testing
```bash
# Run Django tests
python manage.py test

# Run specific test
python manage.py test app.tests.TestModelName
```

### Code Quality
- Python: Uses ruff for linting (configured in pyproject.toml)
- TypeScript: Configured with strict mode
- Vue: Uses vue-tsc for type checking