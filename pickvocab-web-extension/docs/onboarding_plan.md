## Pickvocab Web Extension: Welcome & Onboarding Plan

This document outlines the design for the welcome and onboarding experience for new users of the Pickvocab web extension.

**Goal:** To quickly familiarize new users with the core functionalities of the extension and guide them towards active use.

**Triggering Logic:**
1.  **Automatic Opening:** Upon fresh installation of the extension, a new tab will automatically open, directing the user to the onboarding page (e.g., `chrome-extension://<EXTENSION_ID>/onboarding.html`).
2.  **Persistence/Re-access:**
    *   A flag (e.g., `localStorage.setItem('pickvocab_onboarding_seen', 'true')`) will be set in the extension's local storage once the user completes or explicitly dismisses the onboarding.
    *   If the onboarding tab is closed prematurely before completion, it will *not* automatically reopen on subsequent browser starts to avoid annoyance.
    *   A permanent link to "View Onboarding" or "Help / Quick Start" will be available in the extension's main popup menu (accessed by clicking the extension icon) and potentially in the extension's options page, allowing users to revisit it.

**Onboarding Page UI Style:**
*   The onboarding will be presented in a dedicated new tab.
*   It will use a multi-step/multi-screen approach, guiding the user through each feature one by one. Each step will occupy the main view, with clear "Next" and "Previous" (optional) navigation. A progress indicator (e.g., step dots) will be visible.

**Coding guidelines**:
* Use tailwind as we have used in the project. Check how we use it in options.html page (`/entrypoints/options`)
* Check how the options page (`/entrypoints/options`) is implemented to serve as a guide for implementing the onboarding page.
* We use Vue, WXT, and Tailwind in this project.
* We use wxt to build the extension. You should check the config file to see how it works: `wxt.config.ts`

---

### Onboarding Flow & UI Mockups/Descriptions

The onboarding will consist of a welcome screen followed by screens dedicated to each of the four key features.

```mermaid
graph TD
    A[Welcome to Pickvocab!] --> B{Feature 1: AI Dictionary};
    B --> C{Feature 2: Contextual Lookup};
    C --> D{Feature 3: Click to Save Words};
    D --> E{Feature 4: Writing Assistant};
    E --> G{Step 5: Set up AI API Keys};
    G --> F[Step 7: Get Started!];
```

---

**Step 1: Welcome**

*   **UI Mockup/Description:**
    *   **Headline:** "Welcome to Pickvocab! Your Smart English Companion."
    *   **Image/Animation:** A friendly, welcoming graphic or a short, looping animation showing the Pickvocab logo and perhaps icons representing its features.
    *   **Brief Intro Text:** "Unlock a smarter way to learn and use English. Pickvocab helps you understand, save, and master new vocabulary effortlessly as you browse the web."
    *   **Button:** "Let's Get Started!" (Proceeds to Step 2)

---

**Step 2: Feature 1 - AI Dictionary (Popup)**

*   **UI Mockup/Description:**
    *   **Headline:** "Meet Your AI-Powered Dictionary"
    *   **Visual:**
        *   A simplified static image or short GIF showing:
            1.  The Pickvocab extension icon in the browser toolbar.
            2.  A mouse cursor clicking it.
            3.  The extension popup appearing with a search bar.
            4.  A word being typed into the search bar.
            5.  A snippet of the definition appearing below.
    *   **Instructional Text (Step-by-Step):**
        1.  "**Click the Pickvocab icon** in your browser toolbar." (Highlight extension icon in visual)
        2.  "Or, press **`Cmd+Shift+K`** (Mac) / **`Ctrl+Shift+K`** (Windows) to open it instantly."
        3.  "**Type any word, phrase, or idiom** you want to understand." (Highlight search bar in visual)
        4.  "Get clear definitions, examples, and more, powered by AI!" (Highlight definition snippet in visual)
    *   **Tip:** "Perfect for quick lookups without leaving your current tab."
    *   **Button:** "Next: Understand in Context" (Proceeds to Step 3)

---

**Step 3: Feature 2 - Contextual Meaning Lookup**

*   **UI Mockup/Description:**
    *   **Headline:** "Understand Words in Their True Context"
    *   **Visual:**
        *   A simplified static image or short GIF showing:
            1.  Text selected on a generic webpage.
            2.  The small Pickvocab floating icon appearing near the selected text.
            3.  Mouse cursor clicking the floating icon.
            4.  A small menu popup appearing with "Lookup" and "Fix writing" options.
            5.  Mouse cursor clicking "Lookup".
            6.  A definition popup appearing with the contextual explanation.
    *   **Instructional Text (Step-by-Step):**
        1.  "**Select any text** on a webpage." (Highlight selected text in visual)
        2.  "**Click the Pickvocab icon** that appears." (Highlight floating icon in visual)
        3.  "Choose **'Lookup'** from the menu." (Highlight "Lookup" option in visual)
        4.  "Instantly get an AI-powered explanation tailored to that specific sentence!" (Highlight definition popup in visual)
    *   **Alternative Trigger Text:** "Alternatively, select text and press **`Cmd+Shift+H`** (Mac) / **`Ctrl+Shift+H`** (Windows) for an even faster lookup."
    *   **Tip:** "No more tab-switching! Understand complex phrases exactly as they're used."
    *   **Button:** "Next: Save Your Discoveries" (Proceeds to Step 4)

---

**Step 4: Feature 3 - Click to Save Words**

*   **UI Mockup/Description:**
    *   **Headline:** "Build Your Personal Vocabulary Notebook"
    *   **Visual:**
        *   A simplified static image or short GIF focusing on the definition popup (from either AI Dictionary or Contextual Lookup context).
            1.  The definition popup is visible.
            2.  A "Save" button/icon within the popup is highlighted.
            3.  Mouse cursor clicks "Save".
            4.  A small confirmation "Saved!" appears, or the button changes state (e.g., to "Saved ✔").
            5.  (Optional, if space allows) A subtle hint towards a notebook icon or the main Pickvocab website.
    *   **Instructional Text (Step-by-Step):**
        1.  "After looking up a word (either via the **popup dictionary** or **contextual lookup**)..."
        2.  "You'll see a **'Save' button**." (Highlight "Save" button in visual)
        3.  "**Click it to add the word**, its definition, and the original sentence (for contextual lookups) to your personal Pickvocab notebook."
    *   **Login Requirement Note:** "You'll need to be signed in to your Pickvocab account to save words. If you're not, we'll guide you through a quick sign-in/sign-up process."
    *   **Tip:** "Saved words are stored with their rich context, making your review sessions super effective!"
    *   **Button:** "Next: Enhance Your Writing" (Proceeds to Step 5)

---

**Step 5: Feature 4 - Writing Assistant**

*   **UI Mockup/Description:**
    *   **Headline:** "Elevate Your Writing with AI Assistance"
    *   **Visual:**
        *   A simplified static image or short GIF showing:
            1.  A paragraph of text selected in an input field (e.g., a social media post box, email composer).
            2.  The small Pickvocab floating icon appearing.
            3.  Mouse cursor clicking the floating icon.
            4.  The menu popup appearing, and "Fix writing" being selected.
            5.  The Writing Assistant modal/popup appearing. Key areas to highlight:
                *   The original text.
                *   Tone selection dropdown.
                *   "Use my vocabulary" toggle.
                *   "Revise" button.
                *   A snippet of the revised text with some changes highlighted.
    *   **Instructional Text (Step-by-Step):**
        1.  "**Select the text you're writing** (e.g., an email, a document, a social media post)." (Highlight selected text in visual)
        2.  "**Click the Pickvocab icon** that appears." (Highlight floating icon)
        3.  "Choose **'Fix writing'**." (Highlight "Fix writing" option)
        4.  "In the Assistant:"
            *   "Adjust the **Tone** (e.g., Formal, Casual)." (Highlight Tone Selector)
            *   "Toggle **'Use my vocabulary'** to get suggestions from words you've saved." (Highlight Vocab Toggle)
            *   "Click **'Revise'**." (Highlight Revise button)
        5.  "Get improved phrasing, grammar corrections, and see your learned vocabulary in action!" (Highlight revised text snippet)
    *   **Tip:** "The Writing Assistant helps you use your new words naturally and write with more confidence."
    *   **Button:** "Next: Unlock More AI Features" (Proceeds to Step 6)

---

**Step 6: Set up AI for Enhanced Features (Optional)**

*   **UI Mockup/Description:**
    *   **Headline:** "Unlock More Powerful AI Features (Optional)"
    *   **Visual:**
        *   A simplified static image or short GIF showing:
            1.  Clicking the extension icon.
            2.  Clicking the "Settings" option.
            3.  The options page opening, specifically highlighting the "API Keys" section.
            4.  (Optional, if feasible) Highlight an API key input field.
    *   **Instructional Text (Step-by-Step):**
        1.  "Some advanced features, like the Writing Assistant and more accurate lookups, work best when you provide your own AI API key."
        2.  "To set this up, **click the Pickvocab icon** in your browser toolbar."
        3.  "Then, click the **'Settings'** option to open the extension options page." (Highlight Settings in visual)
        4.  "In the options page, go to the **'API Keys'** section." (Highlight API Keys section in visual)
        5.  "You can input API keys from various providers here." (Mention where links might be, or that they may need to get a key from the provider's website).
        6.  "**We recommend using a Google Gemini API key** for optimal performance." (Maybe add a link or mention where to find info on getting one).
        7.  "After entering your key, navigate to the **'Models'** tab (or similar section) and enable your preferred Gemini model(s)." (Note: This assumes a 'Models' tab exists or that model selection is handled elsewhere in options).
    *   **Tip:** "Setting up an API key gives you more control and access to powerful AI models for better results."
    *   **Button:** "Next: Finish Onboarding!" (Proceeds to Step 7)
    *   **Optional Button:** "Skip Setup for Now" (Proceeds to Step 7)

---

**Step 7: Get Started!**

*   **UI Mockup/Description:**
    *   **Headline:** "You're All Set!"
    *   **Image/Animation:** A celebratory graphic or animation (e.g., checkmark, confetti).
    *   **Text:** "You're ready to make the most of Pickvocab! Start exploring, learning, and improving your English today."
    *   **Important Note:** "For the extension to work correctly, please **restart your browser** after closing this onboarding tab."
    *   **Optional Links/Actions:**
        *   "Visit Pickvocab.com" (Links to the main website where users can manage notebooks, review history, etc.)
        *   "Explore Extension Options" (If there's a relevant options page for the extension itself).
    *   **Button:** "Close Onboarding & Start Using Pickvocab" (Closes the onboarding tab. Sets `pickvocab_onboarding_seen` to `true`).

---