# Plan to Modify Copy Behavior for Writing Assistant in Web Extension

**Objective:** Modify the "Copy Text" functionality in the Web Extension's Writing Assistant so that it copies the original LLM-generated revision text *before* any vocabulary highlighting is applied, while continuing to *display* the highlighted text to the user. This applies to both the dedicated copy button and the copy-on-click behavior of the revised text display area.

## Proposed Plan:

**Part A: Core Logic for Original Text Storage**

1.  **Modify `RevisionData` Interface:**
    *   **File:** `pickvocab-web-extension/components/write/types.ts`
    *   **Action:** Add a new optional property to the `RevisionData` interface.
        ```typescript
        export interface RevisionData {
          revision: string;
          originalRevision?: string; // New property
          user_vocabularies_used?: string[];
          real_card_ids?: string[];
          feedback: string;
          learning_focus?: string[];
        }
        ```

2.  **Populate `originalRevision` in `useWritingRevisionHandler.ts`:**
    *   **File:** `pickvocab-web-extension/components/write/composables/useWritingRevisionHandler.ts`
    *   **Functions:** `initiateRevisionWithSearch` and `initiateRevisionDirectly`
    *   **Action:** In both `initiateRevisionWithSearch` (around line 256) and `initiateRevisionDirectly` (around line 323), after `revisions` are received and before `allRevisionsResult.value` is set, map `revisions` to include the `originalRevision`.
        ```typescript
        // In initiateRevisionWithSearch (around line 256)
        const revisionsWithOriginal = revisions.map(rev => ({
          ...rev,
          originalRevision: rev.revision
        }));
        allRevisionsResult.value = revisionsWithOriginal;

        // In initiateRevisionDirectly (around line 323)
        const revisionsWithOriginal = revisions.map(rev => ({
          ...rev,
          originalRevision: rev.revision
        }));
        allRevisionsResult.value = revisionsWithOriginal;
        ```

3.  **Add `originalRevisedTextResult` Computed Property to `useWritingRevisionHandler.ts`:**
    *   **File:** `pickvocab-web-extension/components/write/composables/useWritingRevisionHandler.ts`
    *   **Action:** Create a new computed property and ensure it's returned by the composable.
        ```typescript
        const originalRevisedTextResult: ComputedRef<string> = computed(() => {
          return currentRevisionData.value?.originalRevision || currentRevisionData.value?.revision || '';
        });
        ```
    *   Also, ensure `originalRevisedTextResult` is returned from the `useWritingRevisionHandler` composable:
        ```typescript
        return {
          // ... existing properties ...
          originalRevisedTextResult, // Make sure to return this
        }
        ```

**Part B: Updating the Dedicated Copy Button Logic**

4.  **Update Copy Logic in `pickvocab-web-extension/components/write/WritingAssistantView.vue` (for dedicated copy button):**
    *   **File:** `pickvocab-web-extension/components/write/WritingAssistantView.vue`
    *   **Action:** Destructure `originalRevisedTextResult` from `useWritingRevisionHandler` and modify the `handleCopyRevisedText` function to use `revisionHandler.originalRevisedTextResult.value`.
        ```typescript
        async function handleCopyRevisedText() {
          try {
            await navigator.clipboard.writeText(revisionHandler.originalRevisedTextResult.value);
          } catch (e) {
            // TODO: Replace with a toast notification
          }
        }
        ```
    *   Also, pass `originalRevisedTextResult.value` as a new prop named `original-text-for-copy` to the `WritingOutputSection` component:
        ```html
        <WritingOutputSection
          <!-- ... other props ... -->
          :revised-text="revisionHandler.revisedText.value"
          :original-text-for-copy="revisionHandler.originalRevisedTextResult.value" <!-- New prop -->
          <!-- ... other event handlers ... -->
        />
        ```

**Part C: Updating Copy-on-Click for `RevisedTextViewer.vue`**

5.  **Receive and Pass `originalTextForCopy` in `WritingOutputSection.vue`:**
    *   **File:** `pickvocab-web-extension/components/write/components/WritingOutputSection.vue`
    *   **Action:** Define the new `originalTextForCopy` prop and pass it down to the `RevisedTextViewer` component.
        ```typescript
        // Inside defineProps in WritingOutputSection.vue
        const props = defineProps({
          // ... other props ...
          revisedText: { type: String, default: '' },
          originalTextForCopy: { type: String, default: '' }, // New prop
          // ...
        });
        ```
        ```html
        <!-- Inside WritingOutputSection.vue template -->
        <RevisedTextViewer
          :revised-text="props.revisedText"
          :original-text-for-copy="props.originalTextForCopy" <!-- Pass down -->
        />
        ```

6.  **Modify `RevisedTextViewer.vue` to use the new prop for its internal copy-on-click:**
    *   **File:** `pickvocab-web-extension/components/write/components/RevisedTextViewer.vue`
    *   **Action:** Add `originalTextForCopy` to its props definition and update its internal `handleCopy` function to use `props.originalTextForCopy`.
        ```typescript
        // Inside defineProps in RevisedTextViewer.vue
        const props = defineProps<{
          revisedText: string;
          originalTextForCopy: string; // New prop
        }>();
        ```
        ```typescript
        async function handleCopy() {
          if (!props.originalTextForCopy) return; // Use the new prop
          try {
            await navigator.clipboard.writeText(props.originalTextForCopy); // Use the new prop
            copied.value = true;
            setTimeout(() => {
              copied.value = false;
            }, 1200);
          } catch (e) {
            // Handle error
          }
        }
        ```

## Expected Outcome:
- Users will see the highlighted version of the revised text in the UI.
- When users click the dedicated "Copy Text" button, the original, unhighlighted text will be copied.
- When users click directly on the revised text display area (`RevisedTextViewer`), the original, unhighlighted text will also be copied. 