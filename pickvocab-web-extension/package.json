{"name": "Pickvocab: AI-powered Dictionary & Writing Assistant", "description": "Unlock precise meanings and elevate your writing instantly while browsing.", "private": true, "version": "0.0.31", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "dev:edge": "wxt -b edge", "build:edge": "wxt build -b edge", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@floating-ui/dom": "^1.6.13", "@tabler/icons-vue": "^3.17.0", "@tiptap/core": "^2.7.0", "@tiptap/extension-bubble-menu": "^2.11.7", "@tiptap/extension-highlight": "^2.10.3", "@tiptap/extension-placeholder": "^2.10.3", "@tiptap/pm": "^2.7.0", "@tiptap/starter-kit": "^2.10.x", "@tiptap/vue-3": "^2.11.7", "@vueuse/core": "^11.3.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.469.0", "marked": "^13.0.2", "pickvocab-dictionary": "workspace:^0.0.0", "pinia": "^2.1.7", "radix-vue": "^1.9.12", "sentence-splitter": "^5.0.0", "slugify": "^1.6.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tiptap-markdown": "^0.8.10", "vue": "^3.5.12", "vue-router": "^4.5.0", "webext-bridge": "^6.0.1", "yaml": "^2.5.0"}, "devDependencies": {"@types/chrome": "^0.0.280", "@types/lodash-es": "^4.17.12", "@wxt-dev/module-vue": "^1.0.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "5.6.3", "vite-plugin-vue-devtools": "^7.7.0", "vue-tsc": "^2.1.10", "wxt": "^0.19.13"}}