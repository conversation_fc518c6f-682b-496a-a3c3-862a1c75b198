<script setup lang="ts">
import { computed } from 'vue';
import { marked } from 'marked';

const props = withDefaults(defineProps<{
  source: string;
  cssClass?: string;
}>(), {
  cssClass: 'markdown-body'
});

const markdownHtml = computed(() => {
  if (!props.source) return '';
  
  let html = marked(props.source, { breaks: true });
  
  // For inline rendering, strip paragraph tags to prevent line breaks
  if (props.cssClass === 'inline-markdown') {
    html = (html as string).replace(/^<p>/, '').replace(/<\/p>$/, '');
  }
  
  return html;
});
</script>

<template>
  <span v-if="cssClass === 'inline-markdown'" v-html="markdownHtml" :class="cssClass"></span>
  <div v-else :class="cssClass" v-html="markdownHtml"></div>
</template>

<style>
/* GitHub-style markdown CSS for consistent formatting */
.markdown-body {
  color: inherit;
  line-height: 1.6;
}

.markdown-body strong {
  font-weight: 600;
}

.markdown-body em {
  font-style: italic;
}

.markdown-body code {
  background-color: rgba(175, 184, 193, 0.2);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 85%;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

.markdown-body a {
  color: #0969da;
  text-decoration: underline;
}

.markdown-body a:hover {
  text-decoration: none;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.markdown-body li {
  margin: 0.25em 0;
}

.markdown-body blockquote {
  border-left: 4px solid #d0d7de;
  padding-left: 1em;
  margin: 0.5em 0;
  color: #656d76;
}

.inline-markdown {
  display: inline;
}

.inline-markdown p {
  display: inline;
  margin: 0;
}
</style>