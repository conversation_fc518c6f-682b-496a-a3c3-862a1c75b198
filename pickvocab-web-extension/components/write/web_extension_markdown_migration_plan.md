# Updated Web Extension Markdown Migration Plan

## Background
We're migrating the web extension's write functionality from YAML to Markdown parsing, following the same approach successfully implemented in the pickvocab-client using `marked` instead of `unified`/`remark-parse`. This migration will ensure consistency across both codebases and leverage the enhanced vocabulary matching and formatting preservation capabilities.

## Key Changes from Original Plan

**Major Update**: The client has migrated from `unified`/`remark-parse` to `marked` for better performance. The web extension migration plan has been updated to use `marked` directly, following the client's proven implementation.

### **Client's `marked` Implementation Analysis**:
- **Parser**: Uses `marked.lexer()` to get token stream instead of AST
- **Utilities**: Created `markedUtils.ts` with `getTextFromMarkedTokens()` function
- **Token Processing**: Direct token traversal instead of AST node traversal
- **Dependencies**: Only requires `marked` (not `unified`/`remark-parse`)
- **Performance**: Significantly faster token-based processing

## Implementation Steps

### **Step 0: Create Markdown Parser Files**

#### **0.1 Create `markedUtils.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/markedUtils.ts`

**Implementation**: **Copy directly from client implementation**
- **Source**: `pickvocab-client/components/app/write/markedUtils.ts`
- **Action**: Copy the entire file with no changes

```typescript
import { marked } from 'marked';

// Type for individual tokens based on the observed structure
export interface MarkedToken {
  type: string;
  raw?: string;
  text?: string;
  tokens?: MarkedToken[];
  items?: MarkedToken[]; // For list tokens
  [key: string]: any;
}

/**
 * Extract text from marked tokens while preserving markdown formatting
 * This replaces the getNodeText function from the unified/remark-parse implementation
 */
export function getTextFromMarkedTokens(tokens: MarkedToken[]): string {
  return tokens.map(token => getTextFromMarkedToken(token)).join('');
}

/**
 * Extract text from a single marked token while preserving markdown formatting
 */
export function getTextFromMarkedToken(token: MarkedToken): string {
  switch (token.type) {
    case 'text':
      return token.text || '';
    
    case 'strong':
      // Preserve bold formatting using raw if available, otherwise reconstruct
      if (token.raw) {
        return token.raw;
      }
      const strongText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      return `**${strongText}**`;
    
    case 'em':
      // Preserve italic formatting using raw if available, otherwise reconstruct
      if (token.raw) {
        return token.raw;
      }
      const emText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      return `*${emText}*`;
    
    case 'link':
      // For links, we typically want just the text content, not the full markdown
      return token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
    
    case 'code':
      // Inline code
      return `\`${token.text}\``;
    
    case 'br':
      return '\n';
    
    case 'space':
      return ' ';
    
    default:
      // For any other token types that have nested tokens
      if ('tokens' in token && token.tokens) {
        return getTextFromMarkedTokens(token.tokens);
      }
      // Fallback to text property if available
      if ('text' in token && typeof token.text === 'string') {
        return token.text;
      }
      return '';
  }
}
```

#### **0.2 Create `markdownRevisionParser.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/markdownRevisionParser.ts`

**Implementation**: **Copy directly from client implementation**
- **Source**: `pickvocab-client/components/app/write/markdownRevisionParser.ts`
- **Action**: Copy the entire file with minimal import path changes

```typescript
import { marked } from 'marked';
import type { RevisionData } from '../types';
import { getTextFromMarkedTokens, type MarkedToken } from './markedUtils';

// Parse raw markdown and extract revisions
export function parseMarkdownRevisions(markdown: string): RevisionData[] {
  // Copy implementation from client - uses marked.lexer() and token traversal
  const tokens = marked.lexer(markdown);
  
  const revisions: RevisionData[] = [];
  let currentRevision: Partial<RevisionData> | null = null;
  let currentSection: string | null = null;
  
  const traverse = (token: MarkedToken) => {
    // Case 1: Heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      
      // Case 1a: Revision header - starts a new revision
      // Very tolerant regex to match any heading containing 'Revision' and a number
      if (/revision.*\d+/i.test(headingText)) {
        // Save previous revision if exists
        if (currentRevision) {
          const completedRevision = currentRevision as RevisionData;
          completedRevision.revision = completedRevision.revision?.trim() || '';
          completedRevision.feedback = completedRevision.feedback?.trim() || '';
          revisions.push(completedRevision);
        }
        
        // Initialize new revision
        currentRevision = {
          revision: '',
          feedback: '',
          learning_focus: [],
          user_vocabularies_used: []
        };
        currentSection = null;
      }
      // Case 1b: Section header within a revision
      else if (currentRevision) {
        // Determine which section we're entering with error tolerance
        if (/revised.*text/i.test(headingText) || /text/i.test(headingText)) {
          currentSection = 'revision';
        } else if (/vocabulary/i.test(headingText)) {
          currentSection = 'vocabulary';
        } else if (/feedback/i.test(headingText)) {
          currentSection = 'feedback';
        } else if (/learning.*focus/i.test(headingText) || /focus/i.test(headingText)) {
          currentSection = 'learning';
        } else {
          // Unknown heading, reset current section
          currentSection = null;
        }
      }
    }
    
    // Case 2: Paragraph tokens - capture text content
    else if (token.type === 'paragraph' && currentRevision && currentSection) {
      const text = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      
      // Case 2a: Revision text section
      if (currentSection === 'revision') {
        // Append text to revision content
        currentRevision.revision += text + '\n\n';
      }
      // Case 2b: Feedback section
      else if (currentSection === 'feedback') {
        // Append text to feedback content
        currentRevision.feedback += text + '\n\n';
      }
    }
    
    // Case 3: List tokens - vocabulary section
    else if (token.type === 'list' && currentRevision && currentSection === 'vocabulary') {
      // Extract vocabulary IDs from list items
      if (token.items && Array.isArray(token.items)) {
        currentRevision.user_vocabularies_used = token.items
          .map(item => {
            // Use the text property directly from list item, or extract from tokens
            let text = item.text || '';
            
            // If text is empty, try to extract from tokens
            if (!text && item.tokens && Array.isArray(item.tokens)) {
              text = getTextFromMarkedTokens(item.tokens);
            }
            
            // Extract any number from the text with error tolerance
            // Handles formats like: "123", 123, "Word ID 123", "ID: 456", etc.
            // Also handle HTML entities like &quot;
            const cleanText = text.replace(/&quot;/g, '"').trim();
            const match = cleanText.match(/(\d+)/);
            return match ? match[1] : null;
          })
          .filter(Boolean) as string[]; // Filter out null values (non-matches) and cast to string[]
      }
    }
    
    // Case 4: List tokens - learning focus section
    else if (token.type === 'list' && currentRevision && currentSection === 'learning') {
      // Extract learning points from list items
      if (token.items && Array.isArray(token.items)) {
        currentRevision.learning_focus = token.items
          .map(item => {
            // Use the text property directly from list item, or extract from tokens
            let text = item.text || '';
            
            // If text is empty, try to extract from tokens
            if (!text && item.tokens && Array.isArray(item.tokens)) {
              text = getTextFromMarkedTokens(item.tokens);
            }
            
            return text
              .replace(/^- /, '')  // Remove leading dash
              .trim();             // Trim whitespace
          })
          .filter(text => text.length > 0); // Filter out empty strings
      }
    }
    
    // Case 5: Recursive traversal for tokens with nested tokens
    if (token.tokens && Array.isArray(token.tokens)) {
      // Process all nested tokens recursively
      token.tokens.forEach(traverse);
    }

    // Case 6: Handle list items (which have items array instead of tokens)
    if (token.type === 'list' && token.items && Array.isArray(token.items)) {
      token.items.forEach(traverse);
    }
  };
  
  // Start traversal from top-level tokens
  tokens.forEach(traverse);
  
  // Add final revision if exists
  if (currentRevision) {
    // Clean up text fields by trimming extra whitespace
    const finalRevision = currentRevision as RevisionData;
    finalRevision.revision = finalRevision.revision?.trim() || '';
    finalRevision.feedback = finalRevision.feedback?.trim() || '';
    revisions.push(finalRevision);
  }

  return revisions;
}
```

#### **0.3 Create `markdownHighlightParser.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/markdownHighlightParser.ts`

**Implementation**: **Copy directly from client implementation**
- **Source**: `pickvocab-client/components/app/write/markdownHighlightParser.ts`
- **Action**: Copy the entire file with minimal import path changes

```typescript
import { marked } from 'marked';
import type { HighlightedRevisionItem } from '../types';
import { getTextFromMarkedTokens, type MarkedToken } from './markedUtils';

// Parse raw markdown and extract highlighted revisions
export function parseMarkdownHighlights(markdown: string): HighlightedRevisionItem[] {
  // Copy implementation from client - uses marked.lexer() and token traversal
  const tokens = marked.lexer(markdown);

  const highlightedRevisions: HighlightedRevisionItem[] = [];
  let currentHighlight: { highlightedText: string } | undefined;
  let currentSection: string | undefined;

  const traverse = (token: MarkedToken) => {
    // Case 1: Heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';

      // Case 1a: Revision header - starts a new highlighted revision
      // Very tolerant regex to match any heading containing 'Revision' and a number
      if (/revision.*\d+/i.test(headingText)) {
        // Save previous highlight if exists and is valid
        if (currentHighlight !== undefined && currentHighlight.highlightedText.length > 0) {
          highlightedRevisions.push({ 
            highlightedText: currentHighlight.highlightedText.trim() 
          });
        }

        // Initialize new highlight
        currentHighlight = {
          highlightedText: ''
        };
        currentSection = undefined;
      }
      // Case 1b: Section header within a revision
      else if (currentHighlight !== undefined) {
        // Determine which section we're entering with error tolerance
        if (/highlight/i.test(headingText)) {
          currentSection = 'highlighted';
        } else {
          // Unknown heading, reset current section
          currentSection = undefined;
        }
      }
    }

    // Case 2: Paragraph tokens - capture highlighted text content
    else if (token.type === 'paragraph' && currentHighlight !== undefined && currentSection === 'highlighted') {
      const text = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      // Append text to highlighted content
      currentHighlight.highlightedText += text + '\n\n';
    }

    // Case 3: Code block tokens - capture highlighted text content (alternative format)
    else if (token.type === 'code' && currentHighlight !== undefined && currentSection === 'highlighted') {
      const text = token.text || '';
      // Append code block content to highlighted text
      currentHighlight.highlightedText += text + '\n\n';
    }

    // Case 4: Recursive traversal for tokens with nested tokens
    if (token.tokens && Array.isArray(token.tokens)) {
      // Process all nested tokens recursively
      token.tokens.forEach(traverse);
    }

    // Case 5: Handle list items (which have items array instead of tokens)
    if (token.type === 'list' && token.items && Array.isArray(token.items)) {
      token.items.forEach(traverse);
    }
  };

  // Start traversal from top-level tokens
  tokens.forEach(traverse);

  // Add final highlight if exists and is valid
  if (currentHighlight !== undefined && currentHighlight.highlightedText.length > 0) {
    highlightedRevisions.push({ 
      highlightedText: currentHighlight.highlightedText.trim() 
    });
  }

  return highlightedRevisions;
}
```

### **Step 1: Create Markdown Prompt Files**

#### **1.1 Create `generateRevisionMarkdownPrompt.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/generateRevisionMarkdownPrompt.ts`

**Implementation**: **Follow client pattern for simple revision prompts**
- **Reference**: `pickvocab-client/components/app/write/reviseTextSimpleMarkdownPrompt.ts`
- **Action**: Create equivalent function with same structure and format

```typescript
export function generateRevisionMarkdownPrompt(
  selectedToneStyleDescription: string, 
  userText: string
): string {
  // Follow the same pattern as client's reviseTextSimpleMarkdownPrompt
  // Use <instructions>, <output_format>, <input> tags
  // Output 3 revisions in Markdown format
  // Include "Vocabulary Used: None" for consistency
}
```

#### **1.2 Create `useMyVocabularyMarkdownPrompt.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/useMyVocabularyMarkdownPrompt.ts`

**Implementation**: **Copy directly from client implementation**
- **Source**: `pickvocab-client/components/app/write/useMyVocabularyMarkdownPrompt.ts`
- **Action**: Copy the entire file with minimal changes for import paths

#### **1.3 Create `grammarCheckMarkdownPrompt.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/grammarCheckMarkdownPrompt.ts`

**Implementation**: **Follow client pattern for grammar check prompts**
- **Reference**: `pickvocab-client/components/app/write/grammarCheckMarkdownPrompt.ts`
- **Action**: Create equivalent function with same structure and format

#### **1.4 Create `highlightVocabularyMarkdownPrompt.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/highlightVocabularyMarkdownPrompt.ts`

**Implementation**: **Copy from client implementation**
- **Source**: `pickvocab-client/components/app/write/highlightVocabularyMarkdownPrompt.ts`
- **Action**: Copy the entire file with minimal changes for import paths

### **Step 2: Update Type Definitions**

#### **2.1 Remove Index Field from HighlightedRevisionItem**
**Location**: `pickvocab-web-extension/components/write/types.ts`

**Implementation**: **Follow client type definition**
- **Reference**: `pickvocab-client/components/app/write/useRevisionApi.ts`
- **Action**: Update the interface to match the client's structure

```typescript
// Before
export interface HighlightedRevisionItem {
  index: number;         // Remove this field
  highlightedText: string;
}

// After (same as client)
export interface HighlightedRevisionItem {
  highlightedText: string;  // Only keep highlighted text
}
```

### **Step 3: Update Background Handlers**

#### **3.1 Update `revisionHandler.ts`**
**Location**: `pickvocab-web-extension/entrypoints/background/revisionHandler.ts`

**Implementation**: **Follow client parsing approach using marked**

**Changes**:
```typescript
// Remove YAML imports
// import YAML from 'yaml';
// import { extractFromCodeBlock } from '@/components/write/utils/revisionUtils';

// Add Markdown import (same as client)
import { parseMarkdownRevisions } from '@/components/write/utils/markdownRevisionParser';

// Replace YAML parsing pipeline with client's marked-based approach
function extractAndParseMarkdownFromLlmResponse(rawResponse: unknown): RevisionData[] | LlmErrorResult {
  const rawMessage = getRawMessageFromLlmResponse(rawResponse);
  if (typeof rawMessage !== 'string') return rawMessage;
  
  // Parse entire response as Markdown using marked.lexer() (single step) - same as client
  const revisions = parseMarkdownRevisions(rawMessage);
  
  if (!revisions.length) {
    return errorResult('no_valid_revisions', 'No valid revisions found in Markdown response');
  }
  
  // Follow client's validation approach
  return revisions.filter((rev): rev is RevisionData =>
    typeof rev.revision === 'string' && typeof rev.feedback === 'string'
  );
}
```

#### **3.2 Update `highlightHandler.ts`**
**Location**: `pickvocab-web-extension/entrypoints/background/highlightHandler.ts`

**Implementation**: **Follow client highlighting parsing approach using marked**

**Changes**:
```typescript
// Remove YAML imports
// import YAML from 'yaml';
// import { extractFromCodeBlock } from '@/components/write/utils/revisionUtils';

// Add Markdown import (same as client)
import { parseMarkdownHighlights } from '@/components/write/utils/markdownHighlightParser';

// Replace YAML parsing with Markdown parsing using marked.lexer() (same as client)
function extractAndParseHighlightedRevisions(rawResponse: unknown): HighlightedRevisionItem[] | LlmErrorResult {
  const rawMessage = getRawMessageFromLlmResponse(rawResponse);
  if (typeof rawMessage !== 'string') return rawMessage;
  
  // Parse entire response as Markdown using marked.lexer() (single step) - same as client
  const highlightedRevisions = parseMarkdownHighlights(rawMessage);
  
  if (!highlightedRevisions.length) {
    return errorResult('no_valid_highlights', 'No valid highlighted revisions found in Markdown response');
  }
  
  // Follow client's validation approach (no index field)
  return highlightedRevisions.filter((item): item is HighlightedRevisionItem =>
    typeof item.highlightedText === 'string' &&
    item.highlightedText.trim().length > 0
  );
}
```

### **Step 4: Update Frontend Components**

#### **4.1 Update `promptUtils.ts`**
**Location**: `pickvocab-web-extension/components/write/utils/promptUtils.ts`

**Implementation**: **Follow client import pattern**

**Changes**:
```typescript
// Replace exports with new Markdown prompt functions (same pattern as client)
export { generateRevisionMarkdownPrompt as generateRevisionPrompt } from './generateRevisionMarkdownPrompt';
export { useMyVocabularyMarkdownPrompt as useMyVocabularyPrompt } from './useMyVocabularyMarkdownPrompt';
export { grammarCheckMarkdownPrompt as grammarCheckPrompt } from './grammarCheckMarkdownPrompt';
export { highlightVocabularyMarkdownPrompt as highlightVocabularyPrompt } from './highlightVocabularyMarkdownPrompt';
```

#### **4.2 Update `useWritingRevisionHandler.ts`**
**Location**: `pickvocab-web-extension/components/write/composables/useWritingRevisionHandler.ts`

**Implementation**: **Follow client highlighting application logic**

**Key Changes**:
```typescript
// Update highlighting logic to use position mapping (same as client)
function _applyHighlightsToState(
  highlightedItems: HighlightedRevisionItem[],
  revisionsToHighlight: RevisionHighlightInput[]
): void {
  // Follow client's position-based mapping approach
  highlightedItems.forEach((highlightedItem, highlightIndex) => {
    // Map the highlight index to the corresponding revision in revisionsToHighlight
    if (highlightIndex < revisionsToHighlight.length) {
      const targetRevision = revisionsToHighlight[highlightIndex];
      const targetIndex = targetRevision.originalIndex;
      
      // Apply highlight to the correct revision (same logic as client)
      if (targetIndex >= 0 && targetIndex < allRevisionsResult.value.length) {
        // Perform basic safety check (same as client)
        const originalText = targetRevision.revisionText;
        const highlightedTextPlain = highlightedItem.highlightedText.replace(/\*\*/g, '');
        
        if (
          originalText.length > highlightedTextPlain.length * 1.5 || 
          highlightedTextPlain.length > originalText.length * 1.5
        )
           {
          console.warn(`Highlighting result for position ${highlightIndex} is too different in length from original`);
          return;
        }
        
        // Apply the highlight (same as client)
        allRevisionsResult.value[targetIndex] = {
          ...allRevisionsResult.value[targetIndex],
          revision: highlightedItem.highlightedText
        };
      }
    }
  });
}
```

### **Step 5: Add Dependencies**

#### **5.1 Install Required Packages**
```bash
# Navigate to web extension directory
cd pickvocab-web-extension

# Add marked for Markdown token parsing (same as client)
pnpm add marked

# Add TypeScript types
pnpm add -D @types/marked
```

**Note**: Unlike the original plan, we do NOT need:
- `unified` 
- `remark-parse`
- `@types/mdast`

The `marked` library provides everything needed for token-based parsing.

### **Step 6: Testing and Validation**

#### **6.1 Comprehensive Testing**
- Test all revision types (simple, vocabulary-enhanced, grammar check)
- Test highlighting functionality with position mapping
- Verify formatting preservation (`**bold**`, `*italic*`)
- Test error handling for malformed responses

#### **6.2 Performance Verification**
- Compare parsing performance between old YAML and new `marked`-based parsing
- Verify memory usage improvements
- Test with large markdown inputs

### **Step 7: Cleanup**

#### **7.1 Remove Old Dependencies**
Once migration is complete and tested:
```bash
# Remove YAML dependency if no longer used elsewhere
pnpm remove yaml

# Remove old YAML-based prompt files
# Remove old parsing utilities
```

## Key Advantages of `marked` Migration

1. **Performance**: Token-based processing is faster than AST traversal
2. **Simplicity**: Direct token access without complex AST navigation
3. **Consistency**: Same parsing engine as client for unified behavior
4. **Maintenance**: Single parsing library across both codebases
5. **Error Tolerance**: Robust handling of LLM output variations

## Migration Validation Checklist

- [ ] All parser files copied and adapted from client
- [ ] All prompt files created following client patterns
- [ ] Type definitions updated (removed index field)
- [ ] Background handlers updated to use `marked` parsers
- [ ] Frontend components updated for position mapping
- [ ] Dependencies installed and old ones removed
- [ ] End-to-end testing completed
- [ ] Performance improvements verified
