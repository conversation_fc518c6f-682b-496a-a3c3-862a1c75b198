import { marked } from 'marked';

// Type for individual tokens based on the observed structure
export interface MarkedToken {
  type: string;
  raw?: string;
  text?: string;
  tokens?: MarkedToken[];
  items?: MarkedToken[]; // For list tokens
  [key: string]: any;
}

/**
 * Helper function to decode HTML entities (both named and numeric)
 */
export function decodeHtmlEntities(text: string): string {
  return text
    // Handle numeric HTML entities first
    .replace(/&#(\d+);/g, (match, num) => String.fromCharCode(parseInt(num, 10)))
    .replace(/&#x([0-9a-fA-F]+);/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    // Handle named HTML entities
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&'); // This should be last to avoid double-decoding
}

/**
 * Extract text from marked tokens while preserving markdown formatting
 * This replaces the getNodeText function from the unified/remark-parse implementation
 */
export function getTextFromMarkedTokens(tokens: MarkedToken[]): string {
  return tokens.map(token => getTextFromMarkedToken(token)).join('');
}

/**
 * Extract text from a single marked token while preserving markdown formatting
 */
export function getTextFromMarkedToken(token: MarkedToken): string {
  switch (token.type) {
    case 'text':
      return decodeHtmlEntities(token.text || '');
    
    case 'strong':
      // Preserve bold formatting using raw if available, otherwise reconstruct
      if (token.raw) {
        return decodeHtmlEntities(token.raw);
      }
      const strongText = token.tokens ? getTextFromMarkedTokens(token.tokens) : decodeHtmlEntities(token.text || '');
      return `**${strongText}**`;
    
    case 'em':
      // Preserve italic formatting using raw if available, otherwise reconstruct
      if (token.raw) {
        return decodeHtmlEntities(token.raw);
      }
      const emText = token.tokens ? getTextFromMarkedTokens(token.tokens) : decodeHtmlEntities(token.text || '');
      return `*${emText}*`;
    
    case 'link':
      // For links, we typically want just the text content, not the full markdown
      return token.tokens ? getTextFromMarkedTokens(token.tokens) : decodeHtmlEntities(token.text || '');
    
    case 'code':
      // Inline code
      return `\`${decodeHtmlEntities(token.text || '')}\``;
    
    case 'br':
      return '\n';
    
    case 'space':
      return ' ';
    
    default:
      // For any other token types that have nested tokens
      if ('tokens' in token && token.tokens) {
        return getTextFromMarkedTokens(token.tokens);
      }
      // Fallback to text property if available
      if ('text' in token && typeof token.text === 'string') {
        return decodeHtmlEntities(token.text);
      }
      return '';
  }
} 