import { marked } from 'marked';
import type { RevisionData } from '../types';
import { getTextFromMarkedTokens, type MarkedToken } from './markedUtils';

// Parse raw markdown and extract revisions
export function parseMarkdownRevisions(markdown: string): RevisionData[] {
  const tokens = marked.lexer(markdown);
  
  const revisions: RevisionData[] = [];
  let currentRevision: Partial<RevisionData> | null = null;
  let currentSection: string | null = null;
  
  const traverse = (token: MarkedToken) => {
    // Case 1: Heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      
      // Case 1a: Revision header - starts a new revision
      // Very tolerant regex to match any heading containing 'Revision' and a number
      if (/revision.*\d+/i.test(headingText)) {
        // Save previous revision if exists
        if (currentRevision) {
          const completedRevision = currentRevision as RevisionData;
          completedRevision.revision = completedRevision.revision?.trim() || '';
          completedRevision.feedback = completedRevision.feedback?.trim() || '';
          revisions.push(completedRevision);
        }
        
        // Initialize new revision
        currentRevision = {
          revision: '',
          feedback: '',
          learning_focus: [],
          user_vocabularies_used: []
        };
        currentSection = null;
      }
      // Case 1b: Section header within a revision
      else if (currentRevision) {
        // Determine which section we're entering with error tolerance
        if (/revised.*text/i.test(headingText) || /text/i.test(headingText)) {
          currentSection = 'revision';
          return; // Skip further processing of this main section heading
        } else if (/vocabulary/i.test(headingText)) {
          currentSection = 'vocabulary';
          return; // Skip further processing of this main section heading
        } else if (/feedback/i.test(headingText)) {
          currentSection = 'feedback';
          return; // Skip further processing of this main section heading
        } else if (/learning.*focus/i.test(headingText) || /focus/i.test(headingText)) {
          currentSection = 'learning';
          return; // Skip further processing of this main section heading
        } else {
          // If it's a heading within a known section, treat it as content
          if (currentRevision && (currentSection === 'revision' || currentSection === 'feedback')) {
            // Process this heading as content immediately
            const content = '#'.repeat(token.depth) + ' ' + headingText;
            if (currentSection === 'revision') {
              currentRevision.revision += content + '\n\n';
            } else if (currentSection === 'feedback') {
              currentRevision.feedback += content + '\n\n';
            }
            return; // Skip further processing of this heading token
          } else {
            // Unknown heading outside of a revision/feedback section, reset current section
            currentSection = null;
          }
        }
      }
    }
    
    // Case 2: Capture ALL content for revision and feedback sections
    else if (currentRevision && (currentSection === 'revision' || currentSection === 'feedback')) {
      let content = '';
      
      // Handle different token types
      if (token.type === 'paragraph') {
        content = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      } else if (token.type === 'list') {
        if (token.items && Array.isArray(token.items)) {
          const listItems = token.items
            .map(item => {
              let text = item.text || '';
              if (!text && item.tokens && Array.isArray(item.tokens)) {
                text = getTextFromMarkedTokens(item.tokens);
              }
              return '- ' + text.trim();
            })
            .filter(text => text.length > 2);
          content = listItems.join('\n');
        }
      } else if (token.type === 'code') {
        content = '```' + (token.lang || '') + '\n' + token.text + '\n```';
      } else if (token.type === 'blockquote') {
        content = '> ' + (token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '');
      } else if (token.type === 'hr') {
        content = '---';
      } else if (token.type === 'table') {
        // Basic table handling - you might want to enhance this
        content = '[Table content]';
      } else if (token.type === 'heading') {
        // Capture subheadings within revision/feedback sections with markdown formatting
        const headingText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
        content = '#'.repeat(token.depth) + ' ' + headingText;
      } else if (token.text) {
        content = token.text;
      }
      
      // Append content to appropriate section
      if (content) {
        if (currentSection === 'revision') {
          currentRevision.revision += content + '\n\n';
        } else if (currentSection === 'feedback') {
          currentRevision.feedback += content + '\n\n';
        }
      }
      
      // Don't recursively process tokens for revision and feedback sections
      // to avoid duplication - we've already extracted the content above
      return;
    }
    
    // Case 3: Handle vocabulary and learning sections (specific parsing)
    else if (token.type === 'list' && currentRevision && currentSection) {
      // Case 3a: List tokens - vocabulary section
      if (currentSection === 'vocabulary') {
        // Extract vocabulary IDs from list items
        if (token.items && Array.isArray(token.items)) {
          currentRevision.user_vocabularies_used = token.items
            .map(item => {
              // Use the text property directly from list item, or extract from tokens
              let text = item.text || '';
              
              // If text is empty, try to extract from tokens
              if (!text && item.tokens && Array.isArray(item.tokens)) {
                text = getTextFromMarkedTokens(item.tokens);
              }
              
              // Extract any number from the text with error tolerance
              // Handles formats like: "123", 123, "Word ID 123", "ID: 456", etc.
              const cleanText = text.trim();
              const match = cleanText.match(/(\d+)/);
              return match ? match[1] : null;
            })
            .filter(Boolean) as string[]; // Filter out null values (non-matches) and cast to string[]
        }
      }
      // Case 3b: List tokens - learning focus section
      else if (currentSection === 'learning') {
        // Extract learning points from list items
        if (token.items && Array.isArray(token.items)) {
          currentRevision.learning_focus = token.items
            .map(item => {
              // Use the text property directly from list item, or extract from tokens
              let text = item.text || '';
              
              // If text is empty, try to extract from tokens
              if (!text && item.tokens && Array.isArray(item.tokens)) {
                text = getTextFromMarkedTokens(item.tokens);
              }
              
              return text
                .replace(/^- /, '')  // Remove leading dash
                .trim();             // Trim whitespace
            })
            .filter(text => text.length > 0); // Filter out empty strings
        }
      }
    }
    
    // Case 4: Recursive traversal for tokens with nested tokens
    if (token.tokens && Array.isArray(token.tokens)) {
      // Process all nested tokens recursively
      token.tokens.forEach(traverse);
    }

    // Case 5: Handle list items (which have items array instead of tokens)
    if (token.type === 'list' && token.items && Array.isArray(token.items)) {
      token.items.forEach(traverse);
    }
  };
  
  // Start traversal from top-level tokens
  tokens.forEach(traverse);
  
  // Add final revision if exists
  if (currentRevision) {
    // Clean up text fields by trimming extra whitespace
    const finalRevision = currentRevision as RevisionData;
    finalRevision.revision = finalRevision.revision?.trim() || '';
    finalRevision.feedback = finalRevision.feedback?.trim() || '';
    revisions.push(finalRevision);
  }

  return revisions;
} 