import { type FormattedVocabulary, type RevisionHighlightInput } from '../types';

// Replace exports with new Markdown prompt functions (same pattern as client)
export { generateRevisionMarkdownPrompt as generateRevisionPrompt } from './generateRevisionMarkdownPrompt';
export { useMyVocabularyMarkdownPrompt as useMyVocabularyPrompt } from './useMyVocabularyMarkdownPrompt';
export { grammarCheckMarkdownPrompt as grammarCheckPrompt } from './grammarCheckMarkdownPrompt';
export { highlightVocabularyMarkdownPrompt as highlightVocabularyPrompt } from './highlightVocabularyMarkdownPrompt';