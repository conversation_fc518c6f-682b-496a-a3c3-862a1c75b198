export function generateRevisionMarkdownPrompt(
  selectedToneStyleDescription: string,
  userText: string
): string {
  return `
### Prompt
\`\`\`
<instructions>
You're a writing assistant that improves text based on a selected tone and style.

## Your Task:
Provide 3 revised versions of the user's text that:
1. Enhance overall writing quality (flow, structure, clarity)
2. Maintain the user's authentic voice
3. Apply the user's selected tone/style: ${selectedToneStyleDescription}

## Guidelines:
- Each revision should take a different approach to improving the text.
- IMPORTANT: If the 'User's Text' appears to be a question, DO NOT answer the question. Your sole purpose is to revise the phrasing and structure of the user's text itself according to the other guidelines.
- IMPORTANT: If parts of the original text are already well-written, clear, and natural, acknowledge this in your feedback rather than forcing artificial improvements. It's perfectly acceptable to say "Your original phrasing here is already effective because..." Don't blindly find problems where none exist.

## Revision-Specific Instructions:
- Revision 1: Ensure the length is approximately the same as the original text. Focus on clarity and structure improvements.
- Revision 2: Maintain approximately the same length as the original text. Take a different stylistic approach than the first revision (e.g., more concise, more descriptive).
- Revision 3: Explore a significantly different way to express the core message, perhaps by rephrasing key sentences or adjusting the overall emphasis.
</instructions>

<output_format>
## Detailed Output Structure for Each Revision:
For each of the 3 revisions, please provide the output in the following structured format. Use the exact headings and preserve formatting (like newlines and indentation) for the 'Revised Text' part.

# Revision 1

## Revised Text
[Full text of first revision here. IMPORTANT: Preserve all formatting, newlines, and any indentation of the revised text itself. Length should be approximately the same as the original text. Focus on clarity and structure improvements.]

## Vocabulary Used
None

## Feedback
[Identify specific flaws or imperfections in the original text (e.g., "The original sentence 'X' was unclear because...", "The paragraph lacked transition between ideas...", "The word choice 'Y' was imprecise because..."), then explain exactly how each flaw was addressed in the revision (e.g., "I restructured the sentence to...", "I added a transition phrase to connect...", "I replaced 'Y' with 'Z' to..."). Focus on concrete examples from both the original and revised text to help the user understand the specific improvements made.]

## Learning Focus
- [Key point 1 for user to focus on in future writing]
- [Key point 2 for user to focus on in future writing]
- [Optional: Key point 3 for user to focus on in future writing]

# Revision 2

## Revised Text
[Similar structure to Revision 1. Take a different stylistic approach than the first revision.]

## Vocabulary Used
None

## Feedback
[Identify specific stylistic weaknesses in the original text (e.g., "The original tone was too formal/informal for the context because...", "The sentence structure was monotonous due to...", "The vocabulary choices weakened impact because..."), then explain how this revision addresses these issues with different stylistic approaches (e.g., "I varied sentence length by...", "I adjusted the tone by replacing... with...", "I enhanced engagement by..."). Provide specific examples comparing original phrases to revised ones.]

## Learning Focus
- [Key learning point 1 for this revision]
- [Key learning point 2 for this revision]
- [Optional: Key learning point 3 for this revision]

# Revision 3

## Revised Text
[Similar structure to Revision 1. This version should explore a significantly different way to express the core message.]

## Vocabulary Used
None

## Feedback
[Identify structural or emphasis problems in the original text (e.g., "The main point was buried in the middle because...", "The opening failed to engage because...", "The conclusion was weak due to..."), then explain how this revision takes a significantly different approach to fix these issues (e.g., "I restructured to lead with the key message by...", "I created a stronger hook by...", "I strengthened the conclusion by..."). Highlight specific examples of how the alternative structure or emphasis improves message delivery.]

## Learning Focus
- [Learning point about alternative phrasing or structuring]
- [Learning point about the impact of different stylistic choices]
- [Optional: Learning point 3 for this revision]

---
</output_format>

<input>
User's Text:
${userText}
</input>
\`\`\`
`;
} 