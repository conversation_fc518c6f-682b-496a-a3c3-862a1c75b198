import { ref, computed, ComputedRef } from 'vue'
import type { RevisionData, LlmErrorResult, FormattedVocabulary, RevisionHighlightInput, HighlightedRevisionItem } from '../types'
import { useWritingRevisionApi } from './useWritingRevisionApi'
import { useWritingStore } from '../stores/writingStore'
import { generateRevisionPrompt, useMyVocabularyPrompt, highlightVocabularyPrompt, grammarCheckPrompt } from '../utils/promptUtils'
import { getToneStyleDescription } from '../utils/toneUtils'
import { formatCardForPrompt, prepareHighlightingInput } from '../utils/revisionUtils'
import type { SaveHistoryPayload, UpdateHistoryPayload } from '../api/index'
import type { Card } from '@/utils/card'

function isLlmErrorResult(obj: unknown): obj is LlmErrorResult {
  return !!obj && typeof obj === 'object' && 'error' in obj && (obj as any).error === true;
}

export function useWritingRevisionHandler() {
  // State
  const allRevisionsResult = ref<RevisionData[]>([])
  const currentRevisionIndex = ref(0)
  const isRevising = ref(false)
  const isLoadingVocabularyCards = ref(false)
  const revisionError = ref<string | null>(null)
  const cardCacheById = ref<Map<string, Card>>(new Map())
  const indexToIdMap = ref<Map<string, string>>(new Map())
  const vocabularyWasUsedForLastRevision = ref(false)
  const currentHistoryEntryId = ref<number | null>(null)

  // API
  const { generateRevision, saveHistory, findSimilarCards, generateHighlights, updateHistory } = useWritingRevisionApi()
  const writingStore = useWritingStore()

  // Computed
  const currentRevisionData = computed(() => {
    if (
      allRevisionsResult.value.length > 0 &&
      currentRevisionIndex.value >= 0 &&
      currentRevisionIndex.value < allRevisionsResult.value.length
    ) {
      return allRevisionsResult.value[currentRevisionIndex.value]
    }
    return undefined
  })

  // For backward compatibility - overall loading state
  const isLoading = computed(() => isRevising.value || isLoadingVocabularyCards.value)

  const revisedText = computed(() => currentRevisionData.value?.revision || '')
  const llmFeedback = computed(() => currentRevisionData.value?.feedback || '')
  const learningFocus = computed(() => currentRevisionData.value?.learning_focus || [])
  const totalRevisions = computed(() => allRevisionsResult.value.length)

  const originalRevisedTextResult: ComputedRef<string> = computed(() => {
    return currentRevisionData.value?.originalRevision || currentRevisionData.value?.revision || '';
  });

  // Methods
  async function saveRevisionHistory(payload: SaveHistoryPayload): Promise<void> {
    try {
      // Ensure cardIds is always present in the payload
      if (!payload.cardIds) {
        payload.cardIds = [];
      }
      const result = await saveHistory(payload);
      
      // If successful and we received a historyId, store it for potential updates
      if (!isLlmErrorResult(result) && 'historyId' in result) {
        currentHistoryEntryId.value = result.historyId;
      } else {
        // Reset to ensure we don't try to update a non-existent history
        currentHistoryEntryId.value = null;
      }
    } catch (saveErr: unknown) {
      // Log error, do not interrupt UX
      currentHistoryEntryId.value = null;
    }
  }

  // Helper functions for highlighting
  async function _generateAndParseHighlights(
    prompt: string
  ): Promise<HighlightedRevisionItem[] | null> {
    try {
      const result = await generateHighlights(prompt);
      
      if (isLlmErrorResult(result)) {
        console.warn('Highlighting failed:', result.message);
        return null;
      }
      
      return result;
    } catch (err: unknown) {
      console.error('Error during highlighting:', err);
      return null;
    }
  }

  function _applyHighlightsToState(
    highlightedItems: HighlightedRevisionItem[],
    revisionsToHighlight: RevisionHighlightInput[]
  ): void {
    // Follow client's position-based mapping approach
    highlightedItems.forEach((highlightedItem, highlightIndex) => {
      // Map the highlight index to the corresponding revision in revisionsToHighlight
      if (highlightIndex < revisionsToHighlight.length) {
        const targetRevision = revisionsToHighlight[highlightIndex];
        const targetIndex = targetRevision.originalIndex;
        
        // Apply highlight to the correct revision (same logic as client)
        if (targetIndex >= 0 && targetIndex < allRevisionsResult.value.length) {
          // Perform basic safety check (same as client)
          const originalText = targetRevision.revisionText;
          const highlightedTextPlain = highlightedItem.highlightedText.replace(/\*\*/g, '');
          
          if (
            originalText.length > highlightedTextPlain.length * 1.5 || 
            highlightedTextPlain.length > originalText.length * 1.5
          ) {
            console.warn(`Highlighting result for position ${highlightIndex} is too different in length from original`);
            return;
          }
          
          // Apply the highlight (same as client)
          allRevisionsResult.value[targetIndex] = {
            ...allRevisionsResult.value[targetIndex],
            revision: highlightedItem.highlightedText
          };
        }
      }
    });
  }

  async function _updateHistoryWithHighlights(): Promise<void> {
    // Check if we have a valid history entry to update
    if (currentHistoryEntryId.value === null) {
      console.warn('No history entry ID available for update');
      return;
    }
    
    try {
      const payload: UpdateHistoryPayload = {
        revisions: allRevisionsResult.value
      };
      
      const result = await updateHistory(currentHistoryEntryId.value, payload);
      
      if (isLlmErrorResult(result)) {
        console.warn('Failed to update history with highlights:', result.message);
      }
    } catch (err: unknown) {
      console.error('Error updating history with highlights:', err);
    }
  }

  async function _fetchAndApplyHighlights(): Promise<void> {
    try {
      // Step 1: Prepare input data for highlighting
      const revisionsToHighlight = prepareHighlightingInput(
        allRevisionsResult.value,
        cardCacheById.value
      );
      
      if (!revisionsToHighlight || revisionsToHighlight.length === 0) {
        console.log('No revisions to highlight or no vocabulary to highlight');
        return;
      }
      
      // Step 2: Generate highlighting prompt
      const prompt = highlightVocabularyPrompt(revisionsToHighlight);
      
      if (!prompt) {
        console.warn('Failed to generate highlighting prompt');
        return;
      }
      
      // Step 3: Call LLM to generate highlights
      const highlights = await _generateAndParseHighlights(prompt);
      
      if (!highlights || highlights.length === 0) {
        console.warn('No highlights generated or parsing failed');
        return;
      }
      
      // Step 4: Apply highlights to state
      _applyHighlightsToState(highlights, revisionsToHighlight);
      
      // Step 5: Update history with highlighted revisions
      await _updateHistoryWithHighlights();
      
    } catch (err: unknown) {
      console.error('Error in highlighting process:', err);
    }
  }

  async function initiateRevisionWithSearch() {
    isRevising.value = true
    isLoadingVocabularyCards.value = true
    revisionError.value = null
    // Reset history entry ID when starting a new revision
    currentHistoryEntryId.value = null

    try {
      // First, find similar cards
      const similarCardsResult = await findSimilarCards(writingStore.userText)

      if (isLlmErrorResult(similarCardsResult)) {
        revisionError.value = `Vocabulary search failed: ${similarCardsResult.message || 'Unknown error'}`
        isRevising.value = false
        isLoadingVocabularyCards.value = false
        return
      }

      // Process the cards and prepare for use in the prompt
      const { cards } = similarCardsResult
      
      // Reset caches and maps
      cardCacheById.value.clear()
      indexToIdMap.value.clear()
      
      // Format the cards for the vocabulary-aware prompt
      const formattedVocabs: FormattedVocabulary[] = []
      
      // Populate the cache with cards and format them for the prompt
      cards.forEach((card, idx) => {
        if (card.id) {
          const cardId = String(card.id)
          cardCacheById.value.set(cardId, card)
          
          // Create index-to-id mapping entries for each card
          // Index will be the position in the array (as string), ID will be the card ID
          const index = String(idx)
          indexToIdMap.value.set(index, cardId)
          
          // Format the card for the prompt
          const formatted = formatCardForPrompt(card, idx)
          if (formatted) {
            formattedVocabs.push(formatted)
          }
        }
      })
      
      isLoadingVocabularyCards.value = false
      
      // Build the real prompt using the vocabulary cards and selected tone
      const toneDescription = getToneStyleDescription(
        writingStore.selectedTone,
        writingStore.predefinedTones,
        writingStore.customTones
      )
      
      // Generate the vocabulary-aware prompt using the formatted cards
      const prompt = useMyVocabularyPrompt(toneDescription, writingStore.userText, formattedVocabs)
      
      // Generate revision with the vocabulary context
      const revisions = await generateRevision(prompt, indexToIdMap.value)
      
      if (isLlmErrorResult(revisions)) {
        revisionError.value = revisions.message || 'Failed to generate revision.'
        allRevisionsResult.value = []
        currentRevisionIndex.value = 0
        vocabularyWasUsedForLastRevision.value = false
      } else {
        const revisionsWithOriginal = revisions.map(rev => ({
          ...rev,
          originalRevision: rev.revision
        }));
        allRevisionsResult.value = revisionsWithOriginal;
        currentRevisionIndex.value = 0
        
        // Check if any vocabulary was actually used in the revisions
        const hasVocabularyInRevisions = revisions.some(revision => 
          Array.isArray(revision.real_card_ids) && revision.real_card_ids.length > 0
        )
        
        vocabularyWasUsedForLastRevision.value = hasVocabularyInRevisions
        
        // Extract all real card IDs from the revisions for the history payload
        const cardIds = new Set<string>()
        revisions.forEach(revision => {
          if (revision.real_card_ids) {
            revision.real_card_ids.forEach(id => cardIds.add(id))
          }
        })
        
        // Save history after successful revision with vocabulary flag
        await saveRevisionHistory({
          userText: writingStore.userText,
          selectedTone: writingStore.selectedTone ? JSON.stringify(writingStore.selectedTone) : '',
          revisions: revisions,
          triggerType: 'Revise',
          useMyVocabulary: true,
          cardIds: Array.from(cardIds)
        });
        
        // If vocabulary was used, trigger the highlighting process
        // Note: Do NOT await this - it should run in the background without blocking UI
        if (hasVocabularyInRevisions) {
          _fetchAndApplyHighlights();
        }
      }
    } catch (err: any) {
      revisionError.value = err?.message || 'Failed to generate revision.'
      allRevisionsResult.value = []
      currentRevisionIndex.value = 0
      vocabularyWasUsedForLastRevision.value = false
    } finally {
      isRevising.value = false
      isLoadingVocabularyCards.value = false
    }
  }

  async function initiateRevisionDirectly() {
    isRevising.value = true
    revisionError.value = null
    // Reset history entry ID when starting a new revision
    currentHistoryEntryId.value = null
    
    try {
      // Build the real prompt using the selected tone's name and description
      const toneDescription = getToneStyleDescription(
        writingStore.selectedTone,
        writingStore.predefinedTones,
        writingStore.customTones
      )
      const prompt = generateRevisionPrompt(toneDescription, writingStore.userText)
      const revisions: RevisionData[] | LlmErrorResult = await generateRevision(prompt)
      
      if (isLlmErrorResult(revisions)) {
        revisionError.value = revisions.message || 'Failed to generate revision.'
        allRevisionsResult.value = []
        currentRevisionIndex.value = 0
        vocabularyWasUsedForLastRevision.value = false
      } else {
        const revisionsWithOriginal = revisions.map(rev => ({
          ...rev,
          originalRevision: rev.revision
        }));
        allRevisionsResult.value = revisionsWithOriginal;
        currentRevisionIndex.value = 0
        
        // This is the direct path without vocabulary integration
        vocabularyWasUsedForLastRevision.value = false
        
        // Save history after successful revision
        await saveRevisionHistory({
          userText: writingStore.userText,
          selectedTone: writingStore.selectedTone ? JSON.stringify(writingStore.selectedTone) : '',
          revisions: revisions,
          triggerType: 'Revise',
          useMyVocabulary: false,
          cardIds: []
        });
      }
    } catch (err: any) {
      revisionError.value = err?.message || 'Failed to generate revision.'
      allRevisionsResult.value = []
      currentRevisionIndex.value = 0
      vocabularyWasUsedForLastRevision.value = false
    } finally {
      isRevising.value = false
    }
  }

  // Add new helper function for grammar check
  async function initiateGrammarCheck() {
    isRevising.value = true
    revisionError.value = null
    // Reset history entry ID when starting a new revision
    currentHistoryEntryId.value = null
    // Ensure vocabulary won't be used or highlighted for grammar check
    vocabularyWasUsedForLastRevision.value = false
    
    try {
      // Generate grammar-specific prompt
      const prompt = grammarCheckPrompt(writingStore.userText)
      
      // Call LLM with grammar check prompt
      const result = await generateRevision(prompt)
      
      if (isLlmErrorResult(result)) {
        revisionError.value = result.message || 'Failed to perform grammar check.'
        allRevisionsResult.value = []
        currentRevisionIndex.value = 0
      } else {
        allRevisionsResult.value = result
        currentRevisionIndex.value = 0
        
        // Save history with grammar check trigger type
        await saveRevisionHistory({
          userText: writingStore.userText,
          selectedTone: writingStore.selectedTone ? JSON.stringify(writingStore.selectedTone) : '',
          revisions: result,
          triggerType: 'GrammarCheck',
          useMyVocabulary: false,
          cardIds: []
        });
      }
    } catch (err: any) {
      revisionError.value = err?.message || 'Failed to perform grammar check.'
      allRevisionsResult.value = []
      currentRevisionIndex.value = 0
    } finally {
      isRevising.value = false
    }
  }

  // Modify the initiateRevision function to check for grammar check state
  async function initiateRevision() {
    // Check if grammar check is enabled
    if (writingStore.grammarCheckEnabled) {
      await initiateGrammarCheck()
      return
    }
    
    // Existing code for standard revisions
    if (writingStore.useVocabulary) {
      await initiateRevisionWithSearch()
    } else {
      await initiateRevisionDirectly()
    }
  }

  function nextRevision() {
    if (currentRevisionIndex.value < allRevisionsResult.value.length - 1) {
      currentRevisionIndex.value++
    }
  }

  function previousRevision() {
    if (currentRevisionIndex.value > 0) {
      currentRevisionIndex.value--
    }
  }

  return {
    allRevisionsResult,
    currentRevisionIndex,
    isLoading,
    isRevising,
    isLoadingVocabularyCards,
    revisionError,
    currentRevisionData,
    revisedText,
    llmFeedback,
    learningFocus,
    totalRevisions,
    cardCacheById,
    vocabularyWasUsedForLastRevision,
    initiateRevision,
    nextRevision,
    previousRevision,
    saveRevisionHistory,
    originalRevisedTextResult
  }
}