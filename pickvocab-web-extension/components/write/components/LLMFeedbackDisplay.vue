<script setup lang="ts">
import { defineProps, type PropType } from 'vue';
// @ts-ignore
import IconInfoCircle from '@tabler/icons-vue/dist/esm/icons/IconInfoCircle.mjs';
import MarkdownRenderer from '@/components/utils/MarkdownRenderer.vue';
import TiptapEditor from '@/components/editor/TiptapEditor.vue';

defineProps({
  llmFeedbackText: { type: String, required: true },
  learningFocus: { type: Array as PropType<string[]>, required: false, default: () => [] },
});
</script>

<template>
  <div 
    class="p-4 bg-white rounded-md border border-gray-200 text-sm text-gray-700 shadow-sm"
    v-if="llmFeedbackText"
  >
    <div class="flex items-start">
      <IconInfoCircle class="h-5 w-5 mr-3 text-emerald-600 flex-shrink-0 mt-0.5" />
      <div class="flex-1">
        <div class="text-sm text-gray-700">
          <TiptapEditor
            :text="llmFeedbackText"
            :editable="false"
            :enableMarkdown="true"
            :showOptions="false"
            :showBubbleMenu="false"
            cssClasses="prose prose-sm max-w-none p-0 border-none shadow-none bg-transparent text-sm text-gray-700 min-h-fit"
          />
        </div>

        <!-- Render Learning Focus if available -->
        <div v-if="learningFocus && learningFocus.length > 0" class="mt-4 pt-3 border-t border-gray-200">
          <h4 class="font-semibold text-gray-700 mb-1.5">Key Learning Points:</h4>
          <ul class="list-disc list-inside space-y-1 text-gray-600">
            <li v-for="(point, index) in learningFocus" :key="index">
              <MarkdownRenderer :source="point" cssClass="inline-markdown" />
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <!-- Optional: Message when no feedback is available -->
  <p v-else class="text-sm text-gray-500 px-4 py-2">
    No feedback available for this revision.
  </p>
</template>

<style scoped>
/* Component-specific styles if needed */
</style> 