<script setup lang="ts">
import { defineProps } from 'vue';
import type { DefinitionCard } from '@/utils/card';
import { stylesForPartOfSpeech } from '@/utils/utils';
import MarkdownRenderer from '@/components/utils/MarkdownRenderer.vue';

const props = defineProps<{
  card: DefinitionCard;
}>();

// Get the first example from the examples array if it exists
function getFirstExample() {
  if (props.card.definition?.examples && Array.isArray(props.card.definition.examples) && props.card.definition.examples.length > 0) {
    return props.card.definition.examples[0];
  }
  return null;
}
</script>

<template>
  <div class="cursor-pointer block p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100">
    <h5 class="mb-2 text-lg font-semibold tracking-tight text-gray-700">
      <span>{{ card.word }}</span>
      <span v-if="card.definition?.partOfSpeech" class="inline-block ml-2 text-sm border rounded-xl px-2 align-middle"
        :class="stylesForPartOfSpeech(card.definition.partOfSpeech)">
        {{ card.definition.partOfSpeech }}
      </span>
    </h5>
    <div class="font-normal text-gray-600" v-if="card.definition?.definition">
      <MarkdownRenderer :source="card.definition.definition" cssClass="inline-markdown" />
    </div>
    <div class="mt-2 italic font-normal text-sm text-gray-600" v-if="getFirstExample()">
      "<MarkdownRenderer :source="getFirstExample()!" cssClass="inline-markdown" />"
    </div>
  </div>
</template> 