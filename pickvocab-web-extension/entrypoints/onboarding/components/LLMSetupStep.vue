<script lang="ts" setup>
// TODO: Add icon imports for styling instructional steps
import { defineEmits } from 'vue';
import { Button } from '@/components/ui/button'; // Import Button component
import { Card, CardContent } from '@/components/ui/card'; // Import Card component
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
// @ts-ignore
import IconKey from '@tabler/icons-vue/dist/esm/icons/IconKey.mjs';

const emit = defineEmits(['next', 'prev']);

const nextStep = () => {
  emit('next');
};

const prevStep = () => {
  emit('prev');
};

const skipSetup = () => {
  emit('next'); // Skipping setup still goes to the next step (Get Started!)
};

// Method to open the options page
const openOptionsPage = () => {
  if (browser && browser.runtime && browser.runtime.openOptionsPage) {
    browser.runtime.openOptionsPage();
  } else {
    // Fallback for browsers that might not support openOptionsPage or if 'browser' is not available directly
    // Although WXT should make browser.runtime available, this is a safeguard.
    // Construct the URL and open in a new tab
    const optionsUrl = chrome.runtime.getURL('options.html');
    window.open(optionsUrl);
  }
};

</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-5xl mx-auto text-center">
      <!-- Header -->
      <div class="mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-2xl mb-6 shadow-lg">
          <IconKey class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Enhance AI Performance with Your API Key (optional)
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Get faster responses, higher limits, and better quality
        </p>
      </div>

      <!-- Main Content -->
      <div class="flex flex-col gap-12 items-center mb-12 text-left">
        <!-- Visual Demo Placeholder -->
        <div class="order-1 w-full lg:order-1">
          <video
            src="@/assets/api-key.mp4"
            class="w-full max-w-[1024px] rounded-lg border border-gray-200 shadow-lg"
            controls
            autoplay
            muted
            loop
            playsinline
            fetchpriority="high"
            aria-label="Demo of Pickvocab's API Key setup process">
            <track
              kind="captions"
              src="/vtt/api-key.vtt"
              srclang="en"
              label="English">
          </video>
        </div>

        <!-- Instructions -->
        <div class="order-2 w-full lg:order-2 space-y-6">
          <div class="space-y-4">
            <!-- Step 1: Open Settings Page -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <IconSettings class="w-4 h-4 text-indigo-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Step 1: Open Settings Page</h4>
                <p class="text-sm text-gray-600">
                  Click <a href="#" @click.prevent="openOptionsPage" class="text-blue-600 hover:underline font-semibold">here</a> to open the extension settings page in a new tab.
                </p>
              </div>
            </div>

            <!-- Step 2: Go to API Keys Section -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <IconKey class="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Step 2: Go to 'API Keys'</h4>
                <p class="text-sm text-gray-600">In the sidebar of the options page, click on the <span class="font-semibold">API Keys</span> entry.</p>
              </div>
            </div>

            <!-- Step 3: Get API Key and Enable Models -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IconKey class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Step 3: Get API Key & Enable Models</h4>
                <p class="text-sm text-gray-600">
                  In the 'API Keys' section, go to the <span class="font-semibold">Provider tab</span>. There's a link for each provider to get your API key. Input your key.
                  Then, go to the <span class="font-semibold">Model tab</span> and enable your preferred models.
                </p>
              </div>
            </div>
          </div>

          <!-- Tip -->
          <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
            <p class="text-sm text-blue-800">
              <strong>💡 Tip:</strong> We recommend enabling <span class="font-semibold">Google Gemini Flash 2.0</span> or higher models for optimal performance.
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-center space-x-4 mt-8">
        <Button @click="prevStep" variant="outline" size="lg" class="group">
          Previous
        </Button>
        <Button @click="skipSetup" variant="outline" size="lg" class="group">
          I'll do it later
        </Button>
        <Button @click="nextStep" size="lg" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
          Next: Finish Onboarding!
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add specific styles if needed */
</style> 