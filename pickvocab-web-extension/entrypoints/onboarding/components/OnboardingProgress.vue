<script lang="ts" setup>
import { defineProps, computed } from 'vue';

const props = defineProps<{
  currentStep: number;
  totalSteps: number;
}>();

// Create an array for rendering steps
const stepsArray = Array.from({ length: props.totalSteps }, (_, i) => i + 1);

// Step labels
const stepLabels = [
  'Welcome',
  'AI Dictionary',
  'Context Lookup',
  'Save Words',
  'Writing Assistant',
  '(Optional) Set up API Keys',
  'Get Started'
];

// Calculate progress percentage
const progressPercentage = computed(() => {
  return ((props.currentStep - 1) / (props.totalSteps - 1)) * 100;
});
</script>

<template>
  <div class="max-w-4xl mx-auto px-6">
    <!-- Progress Bar -->
    <div class="relative mb-8">
      <!-- Background line -->
      <div class="absolute top-1/2 left-0 right-0 h-0.5 bg-gray-200 -translate-y-1/2"></div>

      <!-- Progress line -->
      <div
        class="absolute top-1/2 left-0 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-600 -translate-y-1/2 transition-all duration-500 ease-out"
        :style="{ width: `${progressPercentage}%` }"
      ></div>

      <!-- Step indicators -->
      <div class="relative flex justify-between">
        <div
          v-for="step in stepsArray"
          :key="step"
          class="flex flex-col items-center"
        >
          <!-- Step circle -->
          <div
            class="w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-semibold transition-all duration-300 bg-white"
            :class="{
              'border-blue-500 text-blue-600 shadow-lg shadow-blue-500/25': step === props.currentStep,
              'border-green-500 bg-green-500 text-green-500': step < props.currentStep,
              'border-gray-300 text-gray-400': step > props.currentStep
            }"
          >
            <!-- Checkmark for completed steps -->
            <svg
              v-if="step < props.currentStep"
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
            <!-- Step number for current and future steps -->
            <span v-else>{{ step }}</span>
          </div>

          <!-- Step label -->
          <div
            class="mt-2 text-xs font-medium text-center transition-colors duration-300"
            :class="{
              'text-blue-600': step === props.currentStep,
              'text-green-600': step < props.currentStep,
              'text-gray-400': step > props.currentStep
            }"
          >
            {{ stepLabels[step - 1] }}
          </div>
        </div>
      </div>
    </div>

    <!-- Current step info -->
    <div class="text-center">
      <div class="text-sm text-gray-500 mb-1">
        Step {{ props.currentStep }} of {{ props.totalSteps }}
      </div>
      <h2 class="text-xl font-semibold text-gray-900">
        {{ stepLabels[props.currentStep - 1] }}
      </h2>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style>