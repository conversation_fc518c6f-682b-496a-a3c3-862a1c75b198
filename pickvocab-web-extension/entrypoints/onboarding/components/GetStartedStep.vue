<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconExternalLink from '@tabler/icons-vue/dist/esm/icons/IconExternalLink.mjs';
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
// @ts-ignore
import IconArrowLeft from '@tabler/icons-vue/dist/esm/icons/IconArrowLeft.mjs';
// @ts-ignore
import IconRocket from '@tabler/icons-vue/dist/esm/icons/IconRocket.mjs';

// Define emitted events
const emit = defineEmits(['prev', 'finish']);

const goToPrevious = () => {
  emit('prev');
};

const finishOnboarding = () => {
  emit('finish');
};

const openOptions = () => {
  chrome.runtime.openOptionsPage();
};

const openWebsite = () => {
  window.open('https://pickvocab.com', '_blank');
};
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-4xl mx-auto text-center">
      <!-- Celebration Header -->
      <div class="mb-12">
        <!-- Success icon with animation -->
        <div class="mb-8 relative">
          <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-600 rounded-full blur-3xl opacity-20 scale-150 animate-pulse"></div>
          <div class="relative bg-white rounded-2xl p-8 shadow-xl border border-gray-100 inline-block">
            <div class="w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto">
              <IconCheck class="w-12 h-12 text-white animate-bounce" />
            </div>
          </div>

          <!-- Confetti elements -->
          <div class="absolute top-0 left-0 w-4 h-4 bg-yellow-400 rounded-full animate-ping delay-100"></div>
          <div class="absolute top-10 right-0 w-3 h-3 bg-blue-400 rounded-full animate-ping delay-300"></div>
          <div class="absolute bottom-0 left-10 w-2 h-2 bg-red-400 rounded-full animate-ping delay-500"></div>
          <div class="absolute bottom-10 right-10 w-3 h-3 bg-purple-400 rounded-full animate-ping delay-700"></div>
        </div>

        <!-- Headline -->
        <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
          <span class="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
            You're All Set!
          </span>
        </h1>

        <p class="text-xl md:text-2xl text-gray-600 font-light mb-8">
          Ready to supercharge your English learning journey
        </p>
      </div>

      <!-- Success message -->
      <div class="mb-12">
        <Card class="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-lg max-w-2xl mx-auto">
          <CardContent class="p-8">
            <p class="text-lg text-gray-700 leading-relaxed mb-6">
              You're ready to make the most of Pickvocab! Start exploring, learning, and improving your English today.
            </p>

            <!-- Feature recap -->
            <div class="grid md:grid-cols-2 gap-4 text-sm">
              <div class="flex items-center space-x-2 text-green-700">
                <IconCheck class="w-4 h-4" />
                <span>AI-powered dictionary</span>
              </div>
              <div class="flex items-center space-x-2 text-green-700">
                <IconCheck class="w-4 h-4" />
                <span>Contextual lookups</span>
              </div>
              <div class="flex items-center space-x-2 text-green-700">
                <IconCheck class="w-4 h-4" />
                <span>Personal vocabulary notebook</span>
              </div>
              <div class="flex items-center space-x-2 text-green-700">
                <IconCheck class="w-4 h-4" />
                <span>Writing assistant</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Important Note -->
      <div class="mb-12 bg-gradient-to-r from-red-50 to-orange-50 rounded-xl p-4 border border-red-200 text-center shadow-sm">
        <div class="flex items-center justify-center space-x-2">
          <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
          <p class="text-base text-red-800 font-medium">
            <span class="font-bold">Important:</span> For the extension to work correctly, please <span class="font-bold">restart your browser</span> after closing this onboarding tab.
          </p>
        </div>
      </div>

      <!-- Action links -->
      <div class="mb-12 flex flex-col sm:flex-row justify-center items-center gap-6">
        <Button
          variant="outline"
          size="lg"
          class="group"
          @click="openWebsite"
        >
          <IconExternalLink class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
          Visit pickvocab.com
        </Button>

        <Button
          variant="outline"
          size="lg"
          class="group"
          @click="openOptions"
        >
          <IconSettings class="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
          Extension Settings
        </Button>
      </div>

      <!-- Navigation -->
      <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
        <Button
          @click="goToPrevious"
          variant="outline"
          size="lg"
          class="group order-2 sm:order-1"
        >
          <IconArrowLeft class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Previous
        </Button>

        <Button
          @click="finishOnboarding"
          size="lg"
          class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group order-1 sm:order-2"
        >
          <span class="mr-2">Start Using Pickvocab</span>
          <IconRocket class="w-5 h-5 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
        </Button>
      </div>

      <!-- Decorative elements -->
      <div class="absolute top-20 left-10 w-20 h-20 bg-green-200 rounded-full opacity-20 animate-pulse"></div>
      <div class="absolute bottom-20 right-10 w-16 h-16 bg-emerald-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 right-5 w-12 h-12 bg-yellow-200 rounded-full opacity-20 animate-pulse delay-500"></div>
    </div>
  </div>
</template>

<style scoped>
@keyframes confetti {
  0% { transform: translateY(0) rotate(0deg); opacity: 1; }
  100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

.animate-confetti {
  animation: confetti 2s ease-out infinite;
}
</style>