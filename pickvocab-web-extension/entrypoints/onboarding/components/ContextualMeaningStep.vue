<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// @ts-ignore
import IconCursorText from '@tabler/icons-vue/dist/esm/icons/IconCursorText.mjs';
// @ts-ignore
import IconClick from '@tabler/icons-vue/dist/esm/icons/IconClick.mjs';
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
// @ts-ignore
import IconBulb from '@tabler/icons-vue/dist/esm/icons/IconBulb.mjs';
// @ts-ignore
import IconKeyboard from '@tabler/icons-vue/dist/esm/icons/IconKeyboard.mjs';
// @ts-ignore
import IconArrowLeft from '@tabler/icons-vue/dist/esm/icons/IconArrowLeft.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl mb-6 shadow-lg">
          <IconCursorText class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Understand Words in Their True Context
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Get AI-powered explanations tailored to specific sentences and contexts
        </p>
      </div>

      <!-- Main Content -->
      <div class="grid lg:grid-cols-2 gap-12 items-center mb-12">
        <!-- Visual Demo -->
        <div class="order-2 lg:order-1">
          <video
            src="@/assets/contextual-lookup.mp4"
            class="w-full max-w-[1024px] rounded-lg border border-gray-200 shadow-lg"
            controls
            autoplay
            muted
            loop
            playsinline
            fetchpriority="high"
            aria-label="Demo of Pickvocab's Contextual Lookup in action">
            <track
              kind="captions"
              src="/vtt/contextual-lookup.vtt"
              srclang="en"
              label="English">
          </video>
        </div>

        <!-- Instructions -->
        <div class="order-1 lg:order-2 space-y-6">
          <div class="space-y-4">
            <!-- Step 1 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <IconCursorText class="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Select any text</h4>
                <p class="text-sm text-gray-600">Highlight words or phrases on any webpage</p>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconClick class="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Click the floating icon</h4>
                <p class="text-sm text-gray-600">The Pickvocab button appears near your selection</p>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <IconSearch class="w-4 h-4 text-indigo-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Choose "Lookup"</h4>
                <p class="text-sm text-gray-600">Select the lookup option from the menu</p>
              </div>
            </div>

            <!-- Step 4 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IconBulb class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Get contextual explanation</h4>
                <p class="text-sm text-gray-600">AI analyzes the specific sentence for accurate meaning</p>
              </div>
            </div>
          </div>

          <!-- Keyboard shortcut -->
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
            <div class="flex items-start space-x-3">
              <IconKeyboard class="w-5 h-5 text-purple-600 mt-0.5" />
              <div>
                <h4 class="font-semibold text-purple-900 mb-1">Quick shortcut</h4>
                <p class="text-sm text-purple-800">
                  Select text and press <kbd class="px-2 py-1 bg-white rounded text-xs font-mono shadow">Cmd+Shift+H</kbd> (Mac) /
                  <kbd class="px-2 py-1 bg-white rounded text-xs font-mono shadow">Ctrl+Shift+H</kbd> (Windows)
                </p>
              </div>
            </div>
          </div>

          <!-- Tip -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
            <p class="text-sm text-blue-800">
              <strong>💡 Tip:</strong> No more tab-switching! Understand complex phrases exactly as they're used.
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center">
        <Button
          @click="goToPrevious"
          variant="outline"
          size="lg"
          class="group"
        >
          <IconArrowLeft class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Previous
        </Button>

        <Button
          @click="goToNext"
          size="lg"
          class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 group"
        >
          Next: Save Words
          <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
kbd {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>