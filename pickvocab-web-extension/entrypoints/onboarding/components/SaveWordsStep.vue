<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// @ts-ignore
import IconBookmark from '@tabler/icons-vue/dist/esm/icons/IconBookmark.mjs';
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
// @ts-ignore
import IconDeviceFloppy from '@tabler/icons-vue/dist/esm/icons/IconDeviceFloppy.mjs';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconUser from '@tabler/icons-vue/dist/esm/icons/IconUser.mjs';
// @ts-ignore
import IconArrowLeft from '@tabler/icons-vue/dist/esm/icons/IconArrowLeft.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl mb-6 shadow-lg">
          <IconBookmark class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Build Your Personal Vocabulary Notebook
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Save words with their definitions and context for effective learning
        </p>
      </div>

      <!-- Main Content -->
      <div class="grid lg:grid-cols-2 gap-12 items-center mb-12">
        <!-- Visual Demo -->
        <div class="order-2 lg:order-1">
          <video
            src="@/assets/save-words.mp4"
            class="w-full max-w-[1024px] rounded-lg border border-gray-200 shadow-lg"
            controls
            autoplay
            muted
            loop
            playsinline
            fetchpriority="high"
            aria-label="Demo of Pickvocab's Save Words feature in action">
            <track
              kind="captions"
              src="/vtt/save-words.vtt"
              srclang="en"
              label="English">
          </video>
        </div>

        <!-- Instructions -->
        <div class="order-1 lg:order-2 space-y-6">
          <div class="space-y-4">
            <!-- Step 1 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconSearch class="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Look up a word</h4>
                <p class="text-sm text-gray-600">Use either the popup dictionary or contextual lookup</p>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IconDeviceFloppy class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Click "Save Word"</h4>
                <p class="text-sm text-gray-600">Find the save button in the definition popup</p>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                <IconBookmark class="w-4 h-4 text-emerald-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Word added to notebook</h4>
                <p class="text-sm text-gray-600">Includes definition, context, and original sentence</p>
              </div>
            </div>
          </div>

          <!-- Login requirement -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
            <div class="flex items-start space-x-3">
              <IconUser class="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 class="font-semibold text-blue-900 mb-1">Account required</h4>
                <p class="text-sm text-blue-800">
                  You need to sign in to your Pickvocab account to save words.
                </p>
              </div>
            </div>
          </div>

          <!-- Tip -->
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
            <p class="text-sm text-green-800">
              <strong>💡 Tip:</strong> Saved words are stored with their rich context, making your review sessions super effective!
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center">
        <Button
          @click="goToPrevious"
          variant="outline"
          size="lg"
          class="group"
        >
          <IconArrowLeft class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Previous
        </Button>

        <Button
          @click="goToNext"
          size="lg"
          class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 group"
        >
          Next: Writing Assistant
          <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style>