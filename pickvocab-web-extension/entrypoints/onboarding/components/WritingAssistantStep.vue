<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// @ts-ignore
import IconPencil from '@tabler/icons-vue/dist/esm/icons/IconPencil.mjs';
// @ts-ignore
import IconCursorText from '@tabler/icons-vue/dist/esm/icons/IconCursorText.mjs';
// @ts-ignore
import IconClick from '@tabler/icons-vue/dist/esm/icons/IconClick.mjs';
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
// @ts-ignore
import IconWand from '@tabler/icons-vue/dist/esm/icons/IconWand.mjs';
// @ts-ignore
import IconArrowLeft from '@tabler/icons-vue/dist/esm/icons/IconArrowLeft.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl mb-6 shadow-lg">
          <IconPencil class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Elevate Your Writing with AI Assistance
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Get intelligent suggestions to improve your writing style and use your saved vocabulary
        </p>
      </div>

      <!-- Main Content -->
      <div class="grid lg:grid-cols-2 gap-12 items-center mb-12">
        <!-- Visual Demo -->
        <div class="order-2 lg:order-1">
          <video
            src="@/assets/writing-assistant.mp4"
            class="w-full max-w-[1024px] rounded-lg border border-gray-200 shadow-lg"
            controls
            autoplay
            muted
            loop
            playsinline
            fetchpriority="high"
            aria-label="Demo of Pickvocab's Writing Assistant in action">
            <track
              kind="captions"
              src="/vtt/writing-assistant.vtt"
              srclang="en"
              label="English">
          </video>
        </div>

        <!-- Instructions -->
        <div class="order-1 lg:order-2 space-y-6">
          <div class="space-y-4">
            <!-- Step 1 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <IconCursorText class="w-4 h-4 text-orange-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Select your text</h4>
                <p class="text-sm text-gray-600">Highlight text in emails, documents, or social media posts</p>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <IconClick class="w-4 h-4 text-red-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Click the floating icon</h4>
                <p class="text-sm text-gray-600">The Pickvocab button appears near your selection</p>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <IconPencil class="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Choose "Fix writing"</h4>
                <p class="text-sm text-gray-600">Select the writing assistant option from the menu</p>
              </div>
            </div>

            <!-- Step 4 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconSettings class="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Customize settings</h4>
                <p class="text-sm text-gray-600">Adjust tone and enable vocabulary suggestions</p>
              </div>
            </div>

            <!-- Step 5 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IconWand class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Get AI improvements</h4>
                <p class="text-sm text-gray-600">Receive enhanced phrasing and grammar corrections</p>
              </div>
            </div>
          </div>

          <!-- Tip -->
          <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 border border-orange-200">
            <p class="text-sm text-orange-800">
              <strong>💡 Tip:</strong> The Writing Assistant helps you use your new words naturally and write with more confidence.
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center">
        <Button
          @click="goToPrevious"
          variant="outline"
          size="lg"
          class="group"
        >
          <IconArrowLeft class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Previous
        </Button>

        <Button
          @click="goToNext"
          size="lg"
          class="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 group"
        >
          Finish Setup
          <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style>