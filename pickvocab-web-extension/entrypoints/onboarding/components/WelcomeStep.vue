<script lang="ts" setup>
import { Button } from '@/components/ui/button';
// @ts-ignore
import IconSparkles from '@tabler/icons-vue/dist/esm/icons/IconSparkles.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';

// Define emitted events
const emit = defineEmits(['next']);

const startOnboarding = () => {
  emit('next');
};

// Get the URL for the public asset using chrome.runtime.getURL
const pickvocabLogoUrl = chrome.runtime.getURL('pickvocab.svg');
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-4xl mx-auto text-center">
      <!-- Hero Section -->
      <div class="mb-12">
        <!-- Logo with elegant styling -->
        <div class="mb-8 relative">
          <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-600 rounded-full blur-3xl opacity-20 scale-150"></div>
          <div class="relative bg-white rounded-2xl p-8 shadow-xl border border-gray-100 inline-block">
            <img :src="pickvocabLogoUrl" alt="Pickvocab Logo" class="h-24 w-24 object-contain mx-auto">
          </div>
        </div>

        <!-- Headline with gradient text -->
        <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
          Welcome to
          <span class="text-blue-600">
            Pickvocab
          </span>
        </h1>

        <p class="text-xl md:text-2xl text-gray-600 font-light mb-4">
          Your Smart English Companion
        </p>
      </div>

      <!-- Feature highlights -->
      <div class="mb-12">
        <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-100 max-w-2xl mx-auto">
          <p class="text-lg md:text-xl text-gray-700 leading-relaxed mb-6">
            Unlock a smarter way to learn and use English. Pickvocab helps you understand, save, and master new vocabulary effortlessly as you browse the web.
          </p>

          <!-- Key benefits -->
          <div class="grid md:grid-cols-3 gap-4 text-sm">
            <div class="flex items-center justify-center space-x-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-blue-600">
              <IconSparkles class="w-4 h-4" />
              <span class="font-medium">AI-Powered</span>
            </div>
            <div class="flex items-center justify-center space-x-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-700">
              <IconSparkles class="w-4 h-4" />
              <span class="font-medium">Context-Aware</span>
            </div>
            <div class="flex items-center justify-center space-x-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-800">
              <IconSparkles class="w-4 h-4" />
              <span class="font-medium">Effortless Learning</span>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Button -->
      <div class="flex justify-center">
        <Button
          @click="startOnboarding"
          size="lg"
          class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group"
        >
          <span class="mr-2">Let's Get Started!</span>
          <IconArrowRight class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>

      <!-- Decorative elements -->
      <div class="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
      <div class="absolute bottom-20 right-10 w-16 h-16 bg-blue-300 rounded-full opacity-20 animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 left-5 w-12 h-12 bg-blue-200 rounded-full opacity-20 animate-pulse delay-500"></div>
    </div>
  </div>
</template>

<style scoped>
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style>