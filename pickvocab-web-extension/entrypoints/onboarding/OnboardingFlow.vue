<script lang="ts" setup>
import { ref } from 'vue';
import WelcomeStep from './components/WelcomeStep.vue';
import AiDictionaryStep from './components/AiDictionaryStep.vue';
import ContextualMeaningStep from './components/ContextualMeaningStep.vue';
import SaveWordsStep from './components/SaveWordsStep.vue';
import WritingAssistantStep from './components/WritingAssistantStep.vue';
import GetStartedStep from './components/GetStartedStep.vue';
import OnboardingProgress from './components/OnboardingProgress.vue';
import LLMSetupStep from './components/LLMSetupStep.vue';

const currentStep = ref(1);
const totalSteps = 7;

const nextStep = () => {
  if (currentStep.value < totalSteps) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const finishOnboarding = async () => {
  // Close the current tab
  window.close();
};
</script>

<template>
  <div class="onboarding-container min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
    <!-- Progress Indicator -->
    <div class="bg-white/80 backdrop-blur-sm border-b border-gray-100">
      <OnboardingProgress :currentStep="currentStep" :totalSteps="totalSteps" class="w-full py-6" />
    </div>

    <!-- Step Content -->
    <div class="flex-grow">
      <Transition name="slide-fade" mode="out-in">
        <div v-if="currentStep === 1" key="step-1">
          <WelcomeStep @next="nextStep" />
        </div>
        <div v-else-if="currentStep === 2" key="step-2">
          <AiDictionaryStep @prev="prevStep" @next="nextStep" />
        </div>
        <div v-else-if="currentStep === 3" key="step-3">
          <ContextualMeaningStep @prev="prevStep" @next="nextStep" />
        </div>
        <div v-else-if="currentStep === 4" key="step-4">
          <SaveWordsStep @prev="prevStep" @next="nextStep" />
        </div>
        <div v-else-if="currentStep === 5" key="step-5">
          <WritingAssistantStep @prev="prevStep" @next="nextStep" />
        </div>
        <div v-else-if="currentStep === 6" key="step-6">
          <LLMSetupStep @prev="prevStep" @next="nextStep" />
        </div>
        <div v-else-if="currentStep === 7" key="step-7">
          <GetStartedStep @prev="prevStep" @finish="finishOnboarding" />
        </div>
      </Transition>
    </div>
  </div>
</template>

<style scoped>
.onboarding-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>