import { storage } from 'wxt/storage';
import { getAxiosInstance } from "@/utils/axiosInstance";
import { User } from "@/utils/user";
import { defineStore } from "pinia";
import { useRouter } from "vue-router";
import { getUser, registerAnnonymousUser } from '@/api/auth';
import { getMessagingService } from '@/utils/messaging';

export const useAuthStore = defineStore('auth', () => {
  const user: Ref<User | undefined> = ref(undefined);
  const token: Ref<string | undefined> = ref(undefined);
  const version: Ref<number | undefined> = ref(undefined);
  const annonymousUser: Ref<User | undefined> = ref(undefined);
  const annonymousToken: Ref<string | undefined> = ref(undefined);
  const isHydrated = ref(false);

  const isAuthenticated = computed(() => !!user.value);

  async function setToken(value: string) {
    const axios = getAxiosInstance();

    token.value = value;
    axios.defaults.headers.common['Authorization'] = `Token ${value}`;
  }
  
  function setUser(value: User) {
    user.value = value;
    // initSync();
  }
  
  async function signIn(key: string) {
    await setToken(key);
    // const user = await getUser(key);
    const messagingService = getMessagingService();
    const user = await messagingService.sendMessage('getUser', { token: key }, 'background') as any as User | undefined;
    if (user) setUser(user);
  }
  
  async function signOut() {
    const axios = getAxiosInstance();

    user.value = undefined;
    token.value = undefined;
    delete axios.defaults.headers.common['Authorization'];
    // set annonymous user and token (assign token to axios headers) here
    initUser();
  }

  async function initUser() {
    const storeVersion = Number(import.meta.env.WXT_AUTH_STORE_VERSION || 0);

    if (version.value === undefined || version.value < storeVersion) {
      token.value = undefined;
      user.value = undefined;
      annonymousToken.value = undefined;
      annonymousUser.value = undefined;
      await createAnnonymousUser();
      version.value = storeVersion;
      return;
    }

    if (token.value !== undefined) {
      await signIn(token.value);
    } else if (annonymousToken.value) {
      const success = await signInAnnonymous(annonymousToken.value);
      if (!success) {
        await createAnnonymousUser();
      }
    } else {
      await createAnnonymousUser();
    }
  }

  async function signInAnnonymous(key: string): Promise<boolean> {
    await setAnnonymousToken(key);
    const messagingService = getMessagingService();
    const user = await messagingService.sendMessage('getUser', { token: key }, 'background') as any as User | undefined;
    if (user) {
      setAnnonymousUser(user);
      return true;
    } else {
      const axios = getAxiosInstance();
      annonymousToken.value = undefined;
      annonymousUser.value = undefined;
      delete axios.defaults.headers.common['Authorization'];
      return false;
    }
  }

  async function createAnnonymousUser() {
    const result = await registerAnnonymousUser();
    await signInAnnonymous(result.key);
  }

  function setAnnonymousUser(user: User) {
    annonymousUser.value = user;
  }

  async function setAnnonymousToken(aToken: string) {
    const axios = getAxiosInstance();

    annonymousToken.value = aToken;
    if (!token.value) {
      axios.defaults.headers.common['Authorization'] = `Token ${aToken}`;
    }
  }

  const currentUser = computed(() => user.value || annonymousUser.value);

  // Persist data starts from here
  watch([user, token, annonymousUser, annonymousToken, version], (value) => {
    // only watch these props after hydrating store from storage
    if (!isHydrated.value) return;
    storage.setItem('local:auth', {
      user: user.value,
      token: token.value,
      annonymousUser: annonymousUser.value,
      annonymousToken: annonymousToken.value,
      version: version.value,
    });
  });

  async function hydrate() {
    if (isHydrated.value) return;
    const persistedStore = await storage.getItem('local:auth') as any;
    if (persistedStore) {
      if (persistedStore.user !== undefined) {
        user.value = persistedStore.user;
      }
      if (persistedStore.token !== undefined) {
        token.value = persistedStore.token;
      }
      if (persistedStore.annonymousUser !== undefined) {
        annonymousUser.value = persistedStore.annonymousUser;
      }
      if (persistedStore.annonymousToken !== undefined) {
        annonymousToken.value = persistedStore.annonymousToken;
      }
      if (persistedStore.version !== undefined) {
        version.value = persistedStore.version;
      }
    }
    await initUser();

    // need to explicitly persist store props to local storage
    storage.setItem('local:auth', {
      user: user.value,
      token: token.value,
      annonymousUser: annonymousUser.value,
      annonymousToken: annonymousToken.value,
      version: version.value,
    });
    isHydrated.value = true; // ready to watch these props
  }

  return {
    user,
    token,
    isAuthenticated,
    currentUser,
    annonymousUser,
    annonymousToken,
    version,
    isHydrated,
    setToken,
    setUser,
    signIn,
    signOut,
    createAnnonymousUser,
    setAnnonymousUser,
    setAnnonymousToken,
    signInAnnonymous,
    initUser,
    hydrate
  };
});
