<script setup lang="ts">
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
import type { WordInContextEntry } from "pickvocab-dictionary";
import { stylesForPartOfSpeech } from '@/utils/utils';
import MarkdownRenderer from '@/components/utils/MarkdownRenderer.vue';

const props = withDefaults(
  defineProps<{
    wordEntry: WordInContextEntry;
    showContext?: boolean;
  }>(),
  {
    showContext: false,
  }
);
</script>

<template>
  <div v-if="wordEntry.definition">
    <div class="flex mt-8 items-centers">
      <p class="text-base text-gray-600">
        <span
          class="inline-block text-sm border rounded-xl px-2 align-middle"
          :class="stylesForPartOfSpeech(wordEntry.definition.partOfSpeech)"
        >
          {{ wordEntry.definition.partOfSpeech }}
        </span>
        <span class="ml-1">
          <MarkdownRenderer :source="wordEntry.definition.definition" cssClass="inline-markdown" />
        </span>
      </p>
    </div>

    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Explanation</p>
      <div class="text-base text-gray-600 mt-2">
        <MarkdownRenderer :source="wordEntry.definition.explanation" />
      </div>
    </div>

    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Examples</p>
      <ol class="list-decimal list-inside text-base text-gray-600">
        <li v-for="example in wordEntry.definition.examples" class="p-1 mt-2">
          <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">
            <MarkdownRenderer :source="example.example" />
          </blockquote>
          <div class="mt-4">
            <MarkdownRenderer :source="example.explanation" />
          </div>
        </li>
      </ol>
    </div>

    <div class="mt-8">
      <div class="flex items-center">
        <p class="text-xl text-gray-700 font-semibold">Synonyms</p>
      </div>
      <ol class="text-base text-gray-600 mt-4">
        <li v-for="synonym in wordEntry.definition.synonyms" class="mt-4">
          <a
            :href="`https://pickvocab.com/app/dictionary?word=${synonym.synonym}`"
            rel="nofollow"
            class="font-semibold underline text-blue-800"
            target="_blank"
          >
            {{ synonym.synonym }}
          </a>
          <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">
            <MarkdownRenderer :source="synonym.example" />
          </blockquote>
          <div class="mt-4">
            <MarkdownRenderer :source="synonym.explanation" />
          </div>
        </li>
      </ol>
    </div>

    <div class="mt-8 border-t">
      <a
        :href="`https://pickvocab.com/app/dictionary?word=${wordEntry.word}`"
        rel="nofollow"
        class="mt-4 font-semibold text-blue-800 flex items-center hover:underline"
        target="_blank"
      >
        <span>See other definitions of "{{ wordEntry.word }}"</span>
        <icon-arrow-right class="w-4 h-4 ml-1"></icon-arrow-right>
      </a>
    </div>
  </div>
</template> 