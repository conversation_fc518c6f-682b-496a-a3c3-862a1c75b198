<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useLLMStore } from "../popup/stores/llm";
import { useContentStore } from "./store";
import {
  Dictionary,
  DictionarySource,
  WordInContextEntry,
} from "pickvocab-dictionary";
import { sendMessage } from "webext-bridge/content-script";
import ExplanationView from "./ExplanationView.vue";
import Spinner from "@/components/Spinner.vue";
import DictionaryWordViewInfoAlert from "@/components/lookup/DictionaryWordViewInfoAlert.vue";
import DictionaryWordViewErrorAlert from "@/components/lookup/DictionaryWordViewErrorAlert.vue";
import { reduceContext } from "./reduceContext";
import { BaseContextCard } from "@/utils/card";
import { useAuthStore } from "../popup/stores/auth";
import { SubmitButton } from '@/components/ui/submit-button';

const contentStore = useContentStore();
const llmStore = useLLMStore();
const authStore = useAuthStore();
llmStore.setExtensionUI("content-script");

const shadowRoot = useTemplateRef("shadowRoot");
const { word, context, offset } = storeToRefs(contentStore);
const wordEntry = ref<WordInContextEntry | undefined>(undefined);
const isLoading = ref(false);
const errorMessage = ref("");

const currentView: Ref<"definition" | "addCard"> = ref("definition");
const addCardError = ref("");
const cardUrl = ref("");
const isSaved = ref(false);
const isDetailed = ref(false);
const selectedSimpleViewLanguage = ref<string | undefined>("English");
const isSignInLoading = ref(false);

const isMac = computed(() => {
  return /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent);
});

const dictionary = computed(() => {
  if (!llmStore.isHydrated) return undefined;
  let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
  if (llmStore.activeUserModel) {
    sources = [
      llmStore.createDictionarySource(llmStore.activeUserModel),
      ...sources,
    ];
  }
  const dictionary = new Dictionary(sources);
  return dictionary;
});

const llmModel = computed(() => {
  return wordEntry.value
    ? llmStore.getModelById(wordEntry.value?.llm_model)
    : undefined;
});

watch(isDetailed, () => {
  if (word.value === undefined || context.value === undefined || offset.value === undefined) return;
  if (isDetailed.value) {
    if (wordEntry.value?.definition) return;
    refresh();
  } else {
    if (wordEntry.value?.definitionShort) return;
    refresh();
  }
});

watch(selectedSimpleViewLanguage, () => {
  simpleLookupForLanguage(selectedSimpleViewLanguage.value);
});

const reducedContext = computed(() => {
  return reduceContext(context.value, word.value, offset.value, 3);
});

async function handleLookup() {
  if (dictionary.value === undefined) throw new Error("Expected dictionary");

  try {
    wordEntry.value = undefined;
    isLoading.value = true;
    const base = isDetailed.value ? await dictionary.value.getMeaningInContext(
      reducedContext.value.selectedText,
      reducedContext.value.text,
      reducedContext.value.offset,
    ) : await dictionary.value.getMeaningInContextShort(
      reducedContext.value.selectedText,
      reducedContext.value.text,
      reducedContext.value.offset,
    );
    
    // Create temporary entry and show immediately
    const tempId = -Date.now();
    wordEntry.value = {
      ...base,
      id: tempId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as WordInContextEntry;
    isLoading.value = false;

    // Save to server in background
    sendMessage(
      "wordInContext:create",
      { base: base as any },
      "background"
    ).then((serverEntry: any) => {
      // Always update with server response since we only have one wordEntry
      wordEntry.value = serverEntry as WordInContextEntry;
    }).catch(err => {
      console.error('Failed to save to server:', err);
      // Keep the local version, user can still see the definition
    });
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
    isLoading.value = false;
  }
}

async function refresh(language = "English") {
  if (dictionary.value === undefined) throw new Error("Expected dictionary");

  try {
    const oldEntry = wordEntry.value!;
    wordEntry.value = undefined;
    isLoading.value = true;

    let base;
    if (language === "English") {
      base = isDetailed.value
        ? await dictionary.value.getMeaningInContext(
            reducedContext.value.selectedText,
            reducedContext.value.text,
            reducedContext.value.offset
          )
        : await dictionary.value.getMeaningInContextShort(
            reducedContext.value.selectedText,
            reducedContext.value.text,
            reducedContext.value.offset
          );
    } else {
      if (isDetailed.value) throw new Error("Unsupported");
      base = await dictionary.value.getMeaningInContextShortForLanguage(
        oldEntry,
        language
      );
    }

    // Create updated entry and show immediately
    const newEntry = {
      ...oldEntry,
      ...base,
    };
    wordEntry.value = newEntry;
    isLoading.value = false;

    // Save to server in background
    sendMessage(
      "wordInContext:put",
      { word: newEntry as any },
      "background"
    ).then((serverEntry: any) => {
      // Update with server response
      wordEntry.value = serverEntry as WordInContextEntry;
    }).catch(err => {
      console.error('Failed to save refresh to server:', err);
      // Keep the local version, user can still see the updated definition
    });
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
    isLoading.value = false;
  }
}

async function simpleLookupForLanguage(language = "English") {
  if (language === "English") {
    if (wordEntry.value?.definitionShort) return;
  } else {
    if (
      wordEntry.value?.definitionShort?.languages?.[language] &&
      wordEntry.value.definitionShort.languages[language].explanation
    )
      return;
  }
  refresh(language);
}

onMounted(async () => {
  await Promise.allSettled([llmStore.hydrate(), authStore.hydrate()]);
  await handleLookup();
});

const showInfoMessage = computed(() => {
  return llmStore.shouldShowAPIKeyAlert();
});

function setupApiKey() {
  sendMessage("openOptionsPage", {}, "background");
}

async function hideApiKeyAlert() {
  llmStore.lastShowAPIKeyAlert = Date.now();
}

async function addCard(wordEntry: WordInContextEntry, callback?: () => void) {
  if (!authStore.isAuthenticated) {
    addCardError.value = "You need to sign in to save this word";
    currentView.value = "addCard";
    if (callback) callback();
    return;
  }

  try {
    const baseCard: BaseContextCard = {
      wordInContext: wordEntry,
    };
    const card = (await sendMessage(
      "contextCard:create",
      { card: baseCard as any },
      "background"
    )) as any as WordInContextEntry;

    cardUrl.value = `${import.meta.env.WXT_CLIENT_API_URL}/app/cards/${card.id}`;
    currentView.value = "addCard";
    isSaved.value = true;
  } finally {
    // Execute callback when complete, regardless of success or failure
    if (callback) callback();
  }
}

async function signIn() {
  await sendMessage('googleSignIn', {}, 'background');
  // reload new user data from storage
  authStore.isHydrated = false;
  await authStore.hydrate();
}

async function addCardSignIn() {
  isSignInLoading.value = true;
  try {
    await signIn();
    // Pass a callback that just handles the sign-in loading state
    // The card loading state will be handled by the original callback
    await addCard(wordEntry.value!, () => {
      isSignInLoading.value = false;
    });
  } catch (error) {
    isSignInLoading.value = false;
    // Handle any errors from signIn operation
    console.error("Sign-in failed:", error);
  }
}

function backToDefinitionView(status = false) {
  currentView.value = "definition";
  addCardError.value = "";
  cardUrl.value = "";
  isSaved.value = status;
}
</script>

<template>
  <div ref="shadowRoot" id="pickvocab-popup-container"
    class="!visible bg-white min-w-[450px] max-w-[600px] max-h-[400px] overflow-auto p-6 border border-gray-200 shadow-2xl rounded">
    <div v-if="currentView === 'definition'">
      <DictionaryWordViewInfoAlert v-if="wordEntry && showInfoMessage" class="mb-4" @setup="setupApiKey()"
        @dismiss="hideApiKeyAlert()">
      </DictionaryWordViewInfoAlert>
      <DictionaryWordViewErrorAlert class="mb-4" v-if="errorMessage" @retry="refresh()" @setup="setupApiKey()"
        :message="errorMessage" :is-active-user-model="llmStore.activeUserModel ? true : false" />
      <ExplanationView 
        v-if="wordEntry"
        :word="word"
        :word-entry="wordEntry"
        :llm-model="llmModel"
        :is-loading="isLoading"
        v-model:is-detailed="isDetailed"
        v-model:selected-simple-view-language="selectedSimpleViewLanguage"
        @add-card="addCard"
        @refresh="(language) => refresh(language)"
        @simple-lookup-for-language="(language: string) => simpleLookupForLanguage(language)"
      />
      <div v-else-if="!errorMessage" class="w-full h-full flex items-center justify-center">
        <Spinner 
          :size="6" 
          :show-shortcut-tip="true" 
          :shortcut-tip="isMac ? 'Cmd+Shift+H' : 'Ctrl+Shift+H'"
          shortcut-message="to quickly look up any selected text"
        />
      </div>
    </div>
    <div v-else-if="currentView === 'addCard'">
      <div v-if="isSaved" class="flex flex-col items-center justify-center">
        <p class="text-lg font-semibold text-gray-800">
          Word saved successfully!
        </p>
        <a :href="cardUrl" target="_blank" class="text-blue-700 hover:text-blue-800 mt-2">
          View card
        </a>
        <div class="flex mt-4 space-x-2">
          <button type="button" @click="backToDefinitionView(true)"
            class="text-blue-800 bg-transparent border border-blue-800 hover:bg-blue-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center"
            data-dismiss-target="#alert-additional-content-1" aria-label="Close">
            Back
          </button>
        </div>
      </div>
      <div v-else>
        <p class="text-lg font-semibold text-gray-800">{{ addCardError }}</p>
        <div class="flex mt-4 space-x-2">
          <SubmitButton
            class="text-white bg-blue-800 hover:bg-blue-900 focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center inline-flex items-center"
            text="Sign In"
            loading-text="Signing in..."
            :is-loading="isSignInLoading" 
            @click="addCardSignIn()" 
            is-primary
          />
          <button type="button" @click="backToDefinitionView(false)"
            class="text-blue-800 bg-transparent border border-blue-800 hover:bg-blue-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center"
            data-dismiss-target="#alert-additional-content-1" aria-label="Close">
            Back
          </button>
        </div>
      </div>
    </div>
  </div>
</template> 