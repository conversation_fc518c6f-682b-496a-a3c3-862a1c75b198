import { RemoteGenericCardsApi, type ApiGenericCard } from '@/api/genericCard';
import type { Card } from '@/utils/card';
import * as axiosLib from 'axios';

// Polling configuration (matching client's useRevisionApi.ts)
const INITIAL_DELAY_MS = 50;
const MAX_DELAY_MS = 5000;
const POLLING_TIMEOUT_MS = 30000;

interface SimilaritySearchPayload {
  userText: string;
  limit?: number;
  threshold?: number;
}

interface SuccessResponse {
  success: true;
  cards: Card[];
}

interface ErrorResponse {
  error: true;
  type: 'start_failed' | 'search_failed' | 'timeout' | 'auth' | 'validation' | 'api' | 'network' | 'unknown' | 'unknown_status';
  message?: string;
  status?: number;
  detail?: unknown;
}

type SimilaritySearchResponse = SuccessResponse | ErrorResponse;

/**
 * Handles Axios and other errors, returning a structured ErrorResponse.
 * @param error The error object caught.
 * @returns A structured ErrorResponse.
 */
function handleApiError(error: any): ErrorResponse {
  if (axiosLib.isAxiosError(error)) {
    if (error.response) {
      if (error.response.status === 401 || error.response.status === 403) {
        return { error: true, type: 'auth', status: error.response.status };
      }
      if (error.response.status === 400) {
        return { error: true, type: 'validation', detail: error.response.data };
      }
      return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
    } else {
      return { error: true, type: 'network', message: error.message };
    }
  }
  return { error: true, type: 'unknown', message: String(error) };
}

/**
 * Polls the backend for similarity search results.
 * @param taskId The ID of the search task.
 * @param api Instance of RemoteGenericCardsApi.
 * @returns A promise that resolves with the final result (cards or error).
 */
async function pollSimilarityResults(taskId: string, api: RemoteGenericCardsApi): Promise<SimilaritySearchResponse> {
  return new Promise(async (resolve) => {
    let currentDelay = INITIAL_DELAY_MS;
    let overallTimeoutId: ReturnType<typeof setTimeout> | null = null;
    let nextPollTimeoutId: ReturnType<typeof setTimeout> | null = null;
    let finished = false;

    const cleanup = () => {
      if (overallTimeoutId) clearTimeout(overallTimeoutId);
      if (nextPollTimeoutId) clearTimeout(nextPollTimeoutId);
    };

    const poll = async () => {
      try {
        const pollResult = await api.getSimilaritySearchResults(taskId);

        if (pollResult.status === 'SUCCESS') {
          cleanup();
          finished = true;
          resolve({ success: true, cards: pollResult.results?.map(r => r.card) || [] });
          return;
        } else if (pollResult.status === 'FAILURE') {
          cleanup();
          finished = true;
          resolve({ error: true, type: 'search_failed', message: pollResult.error || pollResult.message || 'Similarity search failed' });
          return;
        } else if (pollResult.status === 'PENDING' || pollResult.status === 'STARTED') {
          const nextDelay = Math.min(currentDelay * 1.2, MAX_DELAY_MS);
          nextPollTimeoutId = setTimeout(poll, nextDelay);
          currentDelay = nextDelay;
        } else {
          cleanup();
          finished = true;
          resolve({ error: true, type: 'unknown_status', message: `Unknown similarity search status: ${pollResult.status}` });
          return;
        }
      } catch (error: any) {
        cleanup();
        finished = true;
        // Resolve with a structured error based on the caught exception
        resolve(handleApiError(error));
        return; // Explicit return after resolving
      }
    };

    overallTimeoutId = setTimeout(() => {
      if (!finished) {
        cleanup();
        finished = true;
        resolve({ error: true, type: 'timeout', message: 'Similarity search polling timed out' });
      }
    }, POLLING_TIMEOUT_MS);

    // Start the first poll
    nextPollTimeoutId = setTimeout(poll, currentDelay);
  });
}

/**
 * Handles the 'writingAssistantSimilaritySearch' message.
 * Initiates a similarity search and polls for results.
 * @param messageData The payload containing userText, limit, and threshold.
 * @returns A promise resolving to the search result (cards or error object).
 */
export async function handleSimilaritySearch(messageData: unknown): Promise<SimilaritySearchResponse> {
  const { userText, limit = 10, threshold } = (messageData || {}) as SimilaritySearchPayload;

  if (!userText) {
    return { error: true, type: 'validation', message: 'Missing userText in payload' };
  }

  const api = new RemoteGenericCardsApi();

  try {
    // 1. Start similarity search
    const startResult = await api.startSimilaritySearch(userText, { limit, threshold });
    if (!startResult.taskId) {
      return { error: true, type: 'start_failed', message: startResult.message || 'No task ID returned from similarity search' };
    }

    // 2. Poll for results using the helper function
    return await pollSimilarityResults(startResult.taskId, api);

  } catch (error: any) {
    // Handle errors specifically from the startSimilaritySearch call
    return handleApiError(error);
  }
} 