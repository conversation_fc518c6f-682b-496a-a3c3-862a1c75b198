import { loadLlmConfig, getModelByName, getApiKey } from './llmConfig';
import { selectActiveModel, selectPickvocabModel } from './modelSelection';
import { createChatSource, createPickvocabChatSource } from './chatSourceFactory';
import type { ChatResponse, ChatSource } from 'pickvocab-dictionary';
import { parseMarkdownRevisions } from '@/components/write/utils/markdownRevisionParser';
import type { RevisionData } from '@/components/write/types';

// Error result type for LLM operations
export type LlmErrorResult = { error: true; type: string; message: string };

interface GenerateRevisionPayload {
  prompt: string;
  indexToIdMap?: Record<string, string>;
}

export async function handleGenerateRevision(messageData: GenerateRevisionPayload): Promise<RevisionData[] | LlmErrorResult> {
  try {
    const { prompt, indexToIdMap } = messageData;

    // 1. Load config
    const { models, providers } = await loadLlmConfig();

    let modelSource: ChatSource | null = null;

    // 2. Try to select an active user model
    const selectedModel = selectActiveModel(models);
    if (selectedModel) {
      const apiKey = getApiKey(providers, selectedModel.provider);
      if (!apiKey) return errorResult('api_key', `Missing API key for provider: ${selectedModel.provider}`);
      modelSource = createChatSource(selectedModel, apiKey);
    } else {
      // 3. Fallback to Pickvocab model
      const pickvocabModelName = import.meta.env.WXT_PICKVOCAB_MODEL;
      const pickvocabModel = selectPickvocabModel(models, pickvocabModelName);
      if (!pickvocabModel) return errorResult('llm_config', 'No Pickvocab model found');
      modelSource = createPickvocabChatSource((name) => getModelByName(models, name), pickvocabModel);
    }

    if (!modelSource) {
      return errorResult('llm_source', 'Failed to instantiate chat source');
    }
    
    // Call LLM and process the result
    const result = await callLlmAndLog(modelSource, prompt);
    
    // If there was an error or no indexToIdMap, return the result as is
    if ('error' in result || !indexToIdMap) {
      return result;
    }
    
    // Process vocabulary indices in each revision
    return processLLMRevisions(result, indexToIdMap);
  } catch (err: unknown) {
    return errorResult('unexpected', String(err));
  }
}

/**
 * Maps LLM vocabulary indices to real card IDs
 * @param revisions The array of RevisionData from the LLM
 * @param indexToIdMap Map of vocabulary indices to real card IDs
 * @returns Processed revisions with real_card_ids field populated
 */
function processLLMRevisions(
  revisions: RevisionData[],
  indexToIdMap: Record<string, string>
): RevisionData[] {
  return revisions.map(revision => {
    const processedRevision = { ...revision };
    
    if (Array.isArray(revision.user_vocabularies_used) && revision.user_vocabularies_used.length > 0) {
      // Convert the indices from the LLM response to actual card IDs
      const realCardIds = revision.user_vocabularies_used
        .map(vocabIndex => {
          const cardId = indexToIdMap[vocabIndex];
          return cardId || null;
        })
        .filter((id): id is string => id !== null);
      
      // Add the real card IDs to the revision data
      processedRevision.real_card_ids = realCardIds;
    }
    
    return processedRevision;
  });
}

function errorResult(type: string, message: string): LlmErrorResult {
  return { error: true, type, message };
}

// Utility: Extract message string from LLM response
function getRawMessageFromLlmResponse(rawResponse: unknown): string | LlmErrorResult {
  if (typeof rawResponse === 'string') {
    return rawResponse;
  }
  if (
    rawResponse &&
    typeof rawResponse === 'object' &&
    'message' in rawResponse &&
    typeof (rawResponse as { message: unknown }).message === 'string'
  ) {
    return (rawResponse as { message: string }).message;
  }
  const errorObj: LlmErrorResult = {
    error: true,
    type: 'invalid_llm_response',
    message: 'LLM response is not a string or object with a message property',
  };
  return errorObj;
}

// Main Markdown extraction/validation pipeline
function extractAndParseMarkdownFromLlmResponse(rawResponse: unknown): RevisionData[] | LlmErrorResult {
  const rawMessage = getRawMessageFromLlmResponse(rawResponse);
  if (typeof rawMessage !== 'string') return rawMessage;
  
  // Parse entire response as Markdown using marked.lexer() (single step) - same as client
  const revisions = parseMarkdownRevisions(rawMessage);
  
  if (!revisions.length) {
    return errorResult('no_valid_revisions', 'No valid revisions found in Markdown response');
  }
  
  // Follow client's validation approach
  return revisions.filter((rev): rev is RevisionData =>
    typeof rev.revision === 'string' && typeof rev.feedback === 'string'
  );
}

async function callLlmAndLog(modelSource: ChatSource, prompt: string): Promise<RevisionData[] | LlmErrorResult> {
  try {
    const rawResponse = await modelSource.sendMessage(prompt);

    // --- Markdown Extraction and Parsing ---
    return extractAndParseMarkdownFromLlmResponse(rawResponse);
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : String(err);
    return errorResult('llm_call', `LLM call failed: ${message}`);
  }
} 