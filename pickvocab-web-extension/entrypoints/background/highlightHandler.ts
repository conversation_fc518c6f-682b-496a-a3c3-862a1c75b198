import { loadLlmConfig, getModelByName, getApi<PERSON>ey } from './llmConfig';
import { selectActiveModel, selectPickvocabModel } from './modelSelection';
import { createChatSource, createPickvocabChatSource } from './chatSourceFactory';
import type { ChatSource } from 'pickvocab-dictionary';
import { parseMarkdownHighlights } from '@/components/write/utils/markdownHighlightParser';
import type { HighlightedRevisionItem, LlmErrorResult } from '@/components/write/types';

interface HighlightVocabularyPayload {
  prompt: string;
}

/**
 * Main handler function for vocabulary highlighting requests
 * @param messageData Object containing the prompt for highlighting
 * @returns Array of highlighted revision items or error object
 */
export async function handleHighlightVocabulary(
  messageData: HighlightVocabularyPayload
): Promise<HighlightedRevisionItem[] | LlmErrorResult> {
  try {
    const { prompt } = messageData;
    if (!prompt) {
      return errorResult('invalid_prompt', 'Prompt is empty or invalid');
    }

    // Log the received prompt for debugging
    console.log('Received highlighting prompt:', prompt);

    // 1. Load LLM config
    const { models, providers } = await loadLlmConfig();

    let modelSource: ChatSource | null = null;

    // 2. Try to select an active user model
    const selectedModel = selectActiveModel(models);
    if (selectedModel) {
      console.log('Using active user model for highlighting:', selectedModel.name);
      const apiKey = getApiKey(providers, selectedModel.provider);
      if (!apiKey) {
        return errorResult('api_key', `Missing API key for provider: ${selectedModel.provider}`);
      }
      modelSource = createChatSource(selectedModel, apiKey);
    } else {
      // 3. Fallback to Pickvocab model
      console.log('No active user model found, falling back to Pickvocab model');
      const pickvocabModelName = import.meta.env.WXT_PICKVOCAB_MODEL;
      const pickvocabModel = selectPickvocabModel(models, pickvocabModelName);
      if (!pickvocabModel) {
        return errorResult('llm_config', 'No Pickvocab model found');
      }
      modelSource = createPickvocabChatSource((name) => getModelByName(models, name), pickvocabModel);
    }

    if (!modelSource) {
      return errorResult('llm_source', 'Failed to instantiate chat source');
    }

    // 4. Call LLM service and process the result
    return await callLlmAndLog(modelSource, prompt);
  } catch (err: unknown) {
    return errorResult('unexpected', String(err));
  }
}

/**
 * Call the LLM service and process the response
 * @param modelSource Instantiated chat source
 * @param prompt Highlighting prompt
 * @returns Array of highlighted revisions or error
 */
async function callLlmAndLog(
  modelSource: ChatSource, 
  prompt: string
): Promise<HighlightedRevisionItem[] | LlmErrorResult> {
  try {
    console.log('Sending highlighting prompt to LLM');
    const rawResponse = await modelSource.sendMessage(prompt);
    console.log('Received raw LLM response for highlighting');
    
    // Process the response
    return extractAndParseHighlightedRevisions(rawResponse);
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : String(err);
    return errorResult('llm_call', `LLM call failed: ${message}`);
  }
}

/**
 * Extract and parse the highlighting results from LLM response
 * @param rawResponse Raw response from LLM
 * @returns Array of highlighted revisions or error
 */
function extractAndParseHighlightedRevisions(
  rawResponse: unknown
): HighlightedRevisionItem[] | LlmErrorResult {
  const rawMessage = getRawMessageFromLlmResponse(rawResponse);
  if (typeof rawMessage !== 'string') return rawMessage;
  
  // Parse entire response as Markdown using marked.lexer() (single step) - same as client
  const highlightedRevisions = parseMarkdownHighlights(rawMessage);
  
  if (!highlightedRevisions.length) {
    return errorResult('no_valid_highlights', 'No valid highlighted revisions found in Markdown response');
  }
  
  // Follow client's validation approach (no index field)
  return highlightedRevisions.filter((item): item is HighlightedRevisionItem =>
    typeof item.highlightedText === 'string' &&
    item.highlightedText.trim().length > 0
  );
}

/**
 * Utility: Extract message string from LLM response
 */
function getRawMessageFromLlmResponse(rawResponse: unknown): string | LlmErrorResult {
  if (typeof rawResponse === 'string') {
    return rawResponse;
  }
  if (
    rawResponse &&
    typeof rawResponse === 'object' &&
    'message' in rawResponse &&
    typeof (rawResponse as { message: unknown }).message === 'string'
  ) {
    return (rawResponse as { message: string }).message;
  }
  const errorObj: LlmErrorResult = {
    error: true,
    type: 'invalid_llm_response',
    message: 'LLM response is not a string or object with a message property',
  };
  return errorObj;
}



/**
 * Create a standardized error result
 */
function errorResult(type: string, message: string): LlmErrorResult {
  console.error(`Highlighting error (${type}):`, message);
  return { error: true, type, message };
} 