import OpenAI from "openai";
import { ChatMessage, ChatResponse, ChatSource } from "./types";
import axios from "axios";
import { ChatCompletion } from "openai/resources";

export class ArliAIChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  maxTokens = 16000;
  messages: ChatMessage[] = [];

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey: string }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  toChatResponse (result: ChatCompletion): ChatResponse {
    return {
      message: result.choices[0].message.content as string,
      usage: {
        promptTokens: result.usage?.prompt_tokens,
        completionTokens: result.usage?.completion_tokens,
        totalTokens: result.usage?.total_tokens,
      },
      modelId: this.modelId
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });

    const response = await axios.post("https://api.arliai.com/v1/chat/completions", {
      model: `${this.modelName}`,
      messages: this.messages,
      repetition_penalty: 1.1,
      temperature: 0.5,
      top_p: 0.9,
      top_k: 40,
      max_tokens: this.maxTokens,
    }, {
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
    });
    const completion = response.data;
    const chatResponse = this.toChatResponse(completion);
    this.messages.push({
      role: 'assistant',
      content: chatResponse.message
    });
    return chatResponse;
  }
}