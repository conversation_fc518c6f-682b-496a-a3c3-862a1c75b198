import Groq from "groq-sdk";
import { ChatCompletion, ChatCompletionMessageParam } from "groq-sdk/resources/chat/completions";
import { ChatMessage, ChatResponse, ChatSource } from "./types";

export class GroqChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  maxTokens = 8192;
  groq: Groq;
  messages: ChatCompletionMessageParam[] = [];

  constructor(config: { modelName: string, modelId: number, maxToken?: number, apiKey: string }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.groq = new Groq({
      apiKey: this.apiKey,
      dangerouslyAllowBrowser: true
    });
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  toChatResponse (result: ChatCompletion): ChatResponse {
    return {
      message: result.choices[0].message.content as string,
      usage: {
        promptTokens: result.usage?.prompt_tokens,
        completionTokens: result.usage?.completion_tokens,
        totalTokens: result.usage?.total_tokens,
      },
      modelId: this.modelId
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });

    const completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: this.messages,
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false
    });
    const chatResponse = this.toChatResponse(completion);
    this.messages.push({
      role: 'assistant',
      content: chatResponse.message
    });
    return chatResponse;
  }
}