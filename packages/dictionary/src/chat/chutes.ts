import { ChatMessage, ChatResponse, ChatSource } from './types';
import { removeThinkBlocks } from '../sources/utils';

export class ChutesChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  isThinkingModel = false;
  maxTokens = 16000;
  apiUrl = 'https://llm.chutes.ai/v1/chat/completions';
  messages: ChatMessage[] = [];

  constructor(config: {
    modelName: string,
    modelId: number,
    maxToken?: number,
    apiKey: string,
    isThinkingModel?: boolean
  }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;

    if (this.shouldAddNothinkPrefix()) {
      this.messages.push({
        role: 'system',
        content: '/nothink'
      });
    }
  }

  private shouldAddNothinkPrefix(): boolean {
    return !this.isThinkingModel && this.modelName.toLowerCase().includes('qwen3');
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  toChatResponse(data: any): ChatResponse {
    const message = removeThinkBlocks(data.choices?.[0]?.message?.content || '');
    return {
      message,
      usage: {
        promptTokens: data.usage?.prompt_tokens,
        completionTokens: data.usage?.completion_tokens,
        totalTokens: data.usage?.total_tokens,
      },
      modelId: this.modelId,
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });
    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.modelName,
        messages: this.messages,
        stream: false,
        max_tokens: this.maxTokens,
        temperature: 0.7,
      }),
    });
    if (!response.ok) throw new Error(`Chutes API error: ${response.status}`);
    const data = await response.json();
    const chatResponse = this.toChatResponse(data);
    this.messages.push({ role: 'assistant', content: chatResponse.message });
    return chatResponse;
  }
} 