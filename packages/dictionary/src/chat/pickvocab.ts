import { shuffle } from 'lodash';
import { ChatMessage, ChatResponse, ChatSource } from "./types";

export class PickvocabChat implements ChatSource {
  sources1: ChatSource[];
  sources2: ChatSource[];
  sources: ChatSource[];
  idx: number;
  modelId: number;
  modelName: string;
  maxTokens = 16000;
  messages: ChatMessage[] = [];

  constructor (sources1: ChatSource[], sources2: ChatSource[], config: { modelId: number, modelName: string, maxToken?: number }) {
    this.sources1 = sources1;
    this.sources2 = sources2;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.sources =  shuffle(this.sources1).concat(shuffle(this.sources2));
    this.idx = 0;
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    const source = this.sources[this.idx];
    source.setHistory(this.messages);

    this.messages.push({ role: 'user', content: message });

    while (true) {
      try {
        console.log(this.modelName);
        const chatResponse = await this.sources[this.idx].sendMessage(message);
        this.messages.push({ role: 'assistant', content: chatResponse.message });
        return chatResponse;
      } catch (err) {
        console.log(err);
        if (this.idx + 1 >= this.sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        this.idx += 1;
      }
    }
  }
}