import { shuffle } from 'lodash';
import { BaseWordEntry, BaseWordInContextEntry } from "../../types";
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { correctLLMText } from '../utils';

export class PickvocabSource implements DictionarySource {
  sources1: DictionarySource[];
  sources2: DictionarySource[];
  modelId: number;
  modelName: string;
  maxTokens = 16000;

  constructor(sources1: DictionarySource[], sources2: DictionarySource[], config: { modelId: number, modelName: string, maxToken?: number }) {
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;

    this.sources1 = sources1;
    this.sources2 = sources2;
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const sources = shuffle(this.sources1).concat(shuffle(this.sources2));
    let idx = 0;
    let source = sources[idx];

    while (true) {
      try {
        console.log(`${this.modelName}`);
        const base = await source.listAllMeanings(word);
        base.definitions.forEach((definition) => {
          definition.context = definition.context ? correctLLMText(definition.context) : undefined
        });
        base.llm_model = this.modelId;
        return base;
      } catch (err) {
        console.log(err);
        if (idx + 1 >= sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        idx += 1;
        source = sources[idx];
      }
    }
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const sources = shuffle(this.sources1).concat(shuffle(this.sources2));
    let idx = 0;
    let source = sources[idx];

    while (true) {
      try {
        if (!source.listAllMeaningsForLanguage) {
          if (idx + 1 >= sources.length) {
            throw new Error(`The model does not currently support ${language}. Please select a different model or language.`);
          }
          idx += 1;
          continue;
        }

        console.log(`${this.modelName}`);
        return await source.listAllMeaningsForLanguage(input, language);
      } catch (err) {
        console.log(err);
        if (idx + 1 >= sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        idx += 1;
        source = sources[idx];
      }
    }
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const sources = shuffle(this.sources1).concat(shuffle(this.sources2));
    let idx = 0;
    let source = sources[idx];

    while (true) {
      try {
        console.log(`${this.modelName}`);
        return await source.getMoreExamples(input);
      } catch (err) {
        console.log(err);
        if (idx + 1 >= sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        idx += 1;
        source = sources[idx];
      }
    }
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const sources = shuffle(this.sources1).concat(shuffle(this.sources2));
    let idx = 0;
    let source = sources[idx];

    while (true) {
      try {
        console.log(`${this.modelName}`);
        return await source.getMoreSynonymsForDefinition(input);
      } catch (err) {
        console.log(err);
        if (idx + 1 >= sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        idx += 1;
        source = sources[idx];
      }
    }
  }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const sources = shuffle(this.sources1).concat(shuffle(this.sources2));
    let idx = 0;
    let source = sources[idx];

    while (true) {
      try {
        console.log(`${this.modelName}`);
        const result = await source.getMeaningInContext(word, context, offset);
        if (!result.definition) throw new Error('Expect definition');
        result.definition.explanation = correctLLMText(result.definition.explanation);
        result.definition.examples.forEach(example => {
          example.explanation = correctLLMText(example.explanation);
        });
        result.definition.synonyms.forEach(synonym => {
          synonym.explanation = correctLLMText(synonym.explanation);
        });
        result.llm_model = this.modelId;
        return result;
      } catch (err) {
        console.log(err);
        if (idx + 1 >= sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        idx += 1;
        source = sources[idx];
      }
    }
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const sources = shuffle(this.sources1).concat(shuffle(this.sources2));
    let idx = 0;
    let source = sources[idx];

    while (true) {
      try {
        console.log(`${this.modelName}`);
        const result = await source.getMeaningInContextShort(word, context, offset);
        if (!result.definitionShort) throw new Error('Expect definitionShort');
        result.llm_model = this.modelId;
        return result;
      } catch (err) {
        console.log(err);
        if (idx + 1 >= sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        idx += 1;
        source = sources[idx];
      }
    }
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    const sources = shuffle(this.sources1).concat(shuffle(this.sources2));
    let idx = 0;
    let source = sources[idx];

    while (true) {
      try {
        console.log(`${this.modelName}`);
        const result = await source.getMeaningInContextShortForLanguage(word, context, offset, language);
        return result;
      } catch (err) {
        console.log(err);
        if (idx + 1 >= sources.length) {
          throw new Error("We're experiencing high traffic right now. Please try again in a few minutes", { cause: err });
        }
        idx += 1;
        source = sources[idx];
      }
    }
  }
}
