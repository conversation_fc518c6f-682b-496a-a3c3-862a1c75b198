import Groq from 'groq-sdk';
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech } from '../../types';
import { definitionForLanguagePrompt, listAllMeaningPrompt, meaningInContextJsonPrompt, meaningInContextPrompt, meaningInContextShortForLanguagePrompt, meaningInContextShortPrompt, meaningInContextToJson, moreExamplesPrompt, moreSynonymsPrompt } from './prompt';
import { jsonrepair } from 'jsonrepair';
import { correctLLMResponse } from '../utils';

export class GroqSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 8192;
  groq: Groq;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey?: string }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.groq = new Groq({
      apiKey: this.apiKey,
      dangerouslyAllowBrowser: true
    });
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: listAllMeaningPrompt },
        { role: 'user', content: word }
      ],
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false,
    });
    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from Groq');

    console.log(`${this.modelName}-listAllMeanings`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const definitions = JSON.parse(jsonrepair(responseText)).definitions;
      const data: BaseWordEntry = {
        word,
        llm_model: this.modelId,
        definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
          partOfSpeech: d.partOfSpeech,
          definition: d.definition,
          context: d.context,
          examples: [d.example],
          synonyms: []
        }))
      };
      return data;
    } catch (err) {
      throw new Error('Failed to parse response from Groq', { cause: err });
    }
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: definitionForLanguagePrompt(language) },
        { role: 'user', content: input }
      ],
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false,
    });

    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from Groq');
    console.log(`${this.modelName}-listAllMeaningsForLanguage`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const definitions = JSON.parse(jsonrepair(responseText)).definitions;
      return definitions;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: moreExamplesPrompt },
        { role: 'user', content: input }
      ],
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false,
    });

    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from Groq');
    console.log(`${this.modelName}-getMoreExamples`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const examples = JSON.parse(jsonrepair(responseText)).result;
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: moreSynonymsPrompt },
        { role: 'user', content: input }
      ],
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false,
    });

    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from Groq');

    console.log(`${this.modelName}-getMoreSynonymsForDe
      finition`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const synonyms = JSON.parse(jsonrepair(responseText)).result;
      return synonyms;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  // async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
  //   // First message to get meaning in context
  //   let completion = await this.groq.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextPrompt(word, context) },
  //     ],
  //     temperature: 0.5,
  //     max_tokens: this.maxTokens,
  //     stream: false,
  //   });

  //   let responseText = completion.choices[0].message.content;
  //   if (!responseText) throw new Error('No response from Groq');

  //   console.log(`${this.modelName}-getMeaningInContext`, responseText);
  //   console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

  //   // Second message to convert to JSON
  //   completion = await this.groq.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextToJson(responseText) },
  //     ],
  //     temperature: 0.5,
  //     max_tokens: this.maxTokens,
  //     stream: false,
  //   });

  //   responseText = completion.choices[0].message.content;
  //   if (!responseText) throw new Error('No response from Groq');

  //   console.log(`${this.modelName}-toJson`, responseText);
  //   console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

  //   try {
  //     const result = JSON.parse(jsonrepair(responseText));
  //     return {
  //       word,
  //       context,
  //       offset,
  //       definition: result,
  //       llm_model: this.modelId,
  //     };
  //   } catch (err) {
  //     throw new Error('Cannot parse response', { cause: err });
  //   }
  // }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    // First message to get meaning in context
    let completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextJsonPrompt(word, context) },
      ],
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false,
    });

    let responseText = completion.choices[0].message.content;
    console.log(`${this.modelName}-toJson`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);
    if (!responseText) throw new Error('No response from Groq');
    responseText = correctLLMResponse(responseText);

    try {
      const result = JSON.parse(jsonrepair(responseText));
      return {
        word,
        context,
        offset,
        definition: result,
        llm_model: this.modelId,
      };
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    // First message to get meaning in context
    let completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextShortPrompt(word, context) },
      ],
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false,
    });

    let responseText = completion.choices[0].message.content;
    console.log(`${this.modelName}-toJson`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);
    if (!responseText) throw new Error('No response from Groq');

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: responseText,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    // First message to get meaning in context
    let completion = await this.groq.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextShortForLanguagePrompt(word, context, language) },
      ],
      temperature: 0.5,
      max_tokens: this.maxTokens,
      stream: false,
    });

    let responseText = completion.choices[0].message.content;
    console.log(`${this.modelName}-toJson`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);
    if (!responseText) throw new Error('No response from Groq');

    return responseText;
  }
}
