import OpenAI from "openai";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech } from "../../types";
import { definitionForLanguagePrompt, listAllMeaningPrompt, meaningInContextJsonPrompt, meaningInContextPrompt, meaningInContextShortForLanguagePrompt, meaningInContextShortPrompt, meaningInContextToJson, moreExamplesPrompt, moreSynonymsPrompt } from "./prompt";
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { jsonrepair } from "jsonrepair";
import { correctLLMResponse } from "../utils";

export class CerebrasSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 16000;
  isThinkingModel = false;
  openAI: OpenAI;

  constructor(config: { 
    modelId: number, 
    modelName: string, 
    maxToken?: number, 
    apiKey?: string,
    isThinkingModel?: boolean 
  }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
    this.openAI = new OpenAI({
      apiKey: this.apiKey,
      dangerouslyAllowBrowser: true,
      baseURL: 'https://api.cerebras.ai/v1',
    });
  }

  private shouldAddNothinkPrefix(): boolean {
    return !this.isThinkingModel && this.modelName.toLowerCase().includes('qwen-3');
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${listAllMeaningPrompt}` : listAllMeaningPrompt 
        },
        { role: 'user', content: word }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`Cerebras-${this.modelName}-listAllMeanings`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { definitions } = JSON.parse(jsonrepair(fixed));
    const data: BaseWordEntry = {
      word,
      llm_model: this.modelId,
      definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
        partOfSpeech: d.partOfSpeech,
        definition: d.definition,
        context: d.context,
        examples: [d.example],
        synonyms: []
      }))
    };
    return data;
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${definitionForLanguagePrompt(language)}` : definitionForLanguagePrompt(language) 
        },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`Cerebras-${this.modelName}-listAllMeaningsForLanguage`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { definitions } = JSON.parse(jsonrepair(fixed));
    return definitions;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${moreExamplesPrompt}` : moreExamplesPrompt 
        },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`Cerebras-${this.modelName}-getMoreExamples`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { result } = JSON.parse(jsonrepair(fixed));
    return result;
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${moreSynonymsPrompt}` : moreSynonymsPrompt 
        },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`Cerebras-${this.modelName}-getMoreSynonymsForDefinition`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { result } = JSON.parse(jsonrepair(fixed));
    return result;
  }

  // async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
  //   let completions = await this.openAI.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextPrompt(word, context) },
  //     ],
  //     max_tokens: this.maxTokens,
  //     temperature: 0.5,
  //   });
  //   let message = completions.choices[0].message.content;
  //   console.log(`Cerebras-${this.modelName}-getMeaningInContext`, message);
  //   console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

  //   if (!message) throw new Error('Cannot parse response');

  //   completions = await this.openAI.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextToJson(message) },
  //     ],
  //     max_tokens: this.maxTokens,
  //     temperature: 0.5,
  //   });

  //   message = completions.choices[0].message.content;
  //   console.log(`Cerebras-${this.modelName}-toJson`, message);
  //   console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

  //   if (!message) throw new Error('Cannot parse response');

  //   const result = JSON.parse(jsonrepair(message));
  //   return {
  //     word,
  //     context,
  //     offset,
  //     definition: result,
  //     llm_model: this.modelId,
  //   };
  // }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextJsonPrompt(word, context)}` : meaningInContextJsonPrompt(word, context) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`Cerebras-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const result = JSON.parse(jsonrepair(fixed));
    return {
      word,
      context,
      offset,
      definition: result,
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextShortPrompt(word, context)}` : meaningInContextShortPrompt(word, context) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`Cerebras-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: fixed,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextShortForLanguagePrompt(word, context, language)}` : meaningInContextShortForLanguagePrompt(word, context, language) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`Cerebras-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);
    return fixed;
  }
}
