import { Anthropic } from "@anthropic-ai/sdk";
import {
  DefinitionForLanguagePromptResult,
  DictionarySource,
  ExamplePromptResult,
  SynonymPromptResult,
} from "../types";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech } from "../../types";
import { definitionForLanguagePrompt, listAllMeaningPrompt, meaningInContextJsonPrompt, meaningInContextPrompt, meaningInContextShortForLanguagePrompt, meaningInContextShortPrompt, meaningInContextToJson, moreExamplesPrompt, moreSynonymsPrompt } from "./prompt";
import { jsonrepair } from "jsonrepair";
import { correctLLMResponse } from "../utils";

export class AnthropicSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 16000;
  anthropic: Anthropic;

  constructor(config: {
    modelId: number;
    modelName: string;
    maxToken?: number;
    apiKey?: string;
  }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.anthropic = new Anthropic({
      apiKey: this.apiKey,
      dangerouslyAllowBrowser: true
    });
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: [
        {
          role: "user",
          content: listAllMeaningPrompt(word),
        },
      ],
    });

    const responseText =
      response.content.length > 0 && response.content[0].type === "text"
        ? response.content[0].text
        : undefined;

    if (!responseText) throw new Error("Unreachable");
    console.log(`${this.modelName}-listAllMeanings`, responseText);
    console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
    console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

    try {
      const definitions = JSON.parse(jsonrepair(responseText));
      const data: BaseWordEntry = {
        word,
        llm_model: this.modelId,
        definitions: definitions.map(
          (d: {
            partOfSpeech: PartOfSpeech;
            definition: string;
            example: string;
            context?: string;
          }) => ({
            partOfSpeech: d.partOfSpeech,
            definition: d.definition,
            context: d.context,
            examples: [d.example],
            synonyms: [],
          }),
        ),
      };
      return data;
    } catch (err) {
      throw new Error("Cannot parse response", { cause: err });
    }
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: [
        {
          role: "user",
          content: definitionForLanguagePrompt(input, language),
        },
      ],
    });

    const responseText =
      response.content.length > 0 && response.content[0].type === "text"
        ? response.content[0].text
        : undefined;

    if (!responseText) throw new Error("Unreachable");
    console.log(`${this.modelName}-listAllMeaningsForLanguage`, responseText);
    console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
    console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error("Cannot parse response", { cause: err });
    }
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: [
        {
          role: "user",
          content: moreExamplesPrompt(input),
        },
      ],
    });

    const responseText =
      response.content.length > 0 && response.content[0].type === "text"
        ? response.content[0].text
        : undefined;

    if (!responseText) throw new Error("Unreachable");
    console.log(`${this.modelName}-getMoreExamples`, responseText);
    console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
    console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error("Cannot parse response", { cause: err });
    }
  }

  async getMoreSynonymsForDefinition(
    input: string,
  ): Promise<SynonymPromptResult> {
    const response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: [
        {
          role: "user",
          content: moreSynonymsPrompt(input),
        },
      ],
    });

    const responseText =
      response.content.length > 0 && response.content[0].type === "text"
        ? response.content[0].text
        : undefined;

    if (!responseText) throw new Error("Unreachable");
    console.log(`${this.modelName}-getMoreSynonymsForDefinition`, responseText);
    console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
    console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

    try {
      const synonyms = JSON.parse(jsonrepair(responseText));
      return synonyms;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err});
    }
  }

  // async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
  //   // First message to get meaning in context
  //   let response = await this.anthropic.messages.create({
  //     max_tokens: this.maxTokens,
  //     model: this.modelName,
  //     messages: [
  //       {
  //         role: "user",
  //         content: meaningInContextPrompt(word, context),
  //       },
  //     ],
  //   });

  //   let responseText = response.content.length > 0 && response.content[0].type === "text"
  //     ? response.content[0].text
  //     : undefined;

  //   if (!responseText) throw new Error("Unreachable");
  //   console.log(`${this.modelName}-getMeaningInContext`, responseText);
  //   console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
  //   console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

  //   // Second message to convert to JSON
  //   response = await this.anthropic.messages.create({
  //     max_tokens: this.maxTokens,
  //     model: this.modelName,
  //     messages: [
  //       {
  //         role: "user",
  //         content: meaningInContextToJson(responseText),
  //       },
  //     ],
  //   });

  //   responseText = response.content.length > 0 && response.content[0].type === "text"
  //     ? response.content[0].text
  //     : undefined;

  //   if (!responseText) throw new Error("Unreachable");
  //   console.log(`${this.modelName}-toJson`, responseText);
  //   console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
  //   console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

  //   try {
  //     const result = JSON.parse(jsonrepair(responseText));
  //     return {
  //       word,
  //       context,
  //       offset,
  //       definition: result,
  //       llm_model: this.modelId,
  //     };
  //   } catch (err) {
  //     throw new Error('Cannot parse response', { cause: err });
  //   }
  // }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    // First message to get meaning in context
    let response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: [
        {
          role: "user",
          content: meaningInContextJsonPrompt(word, context),
        },
      ],
    });

    let responseText = response.content.length > 0 && response.content[0].type === "text"
      ? response.content[0].text
      : undefined;

    if (!responseText) throw new Error("Unreachable");
    console.log(`${this.modelName}-getMeaningInContext`, responseText);
    console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
    console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);
    responseText = correctLLMResponse(responseText);

    try {
      const result = JSON.parse(jsonrepair(responseText));
      return {
        word,
        context,
        offset,
        definition: result,
        llm_model: this.modelId,
      };
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: [
        {
          role: "user",
          content: meaningInContextShortPrompt(word, context),
        },
      ],
    });

    let responseText = response.content.length > 0 && response.content[0].type === "text"
      ? response.content[0].text
      : undefined;

    if (!responseText) throw new Error("Unreachable");
    console.log(`${this.modelName}-getMeaningInContext`, responseText);
    console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
    console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: responseText,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    let response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: [
        {
          role: "user",
          content: meaningInContextShortForLanguagePrompt(word, context, language),
        },
      ],
    });

    let responseText = response.content.length > 0 && response.content[0].type === "text"
      ? response.content[0].text
      : undefined;

    if (!responseText) throw new Error("Unreachable");
    console.log(`${this.modelName}-getMeaningInContext`, responseText);
    console.log(`${this.modelName}-inputTokens`, response.usage.input_tokens);
    console.log(`${this.modelName}-outputTokens`, response.usage.output_tokens);

    return responseText;
  }
}
