import { Mistral } from "@mistralai/mistralai";
import {
  BaseWordEntry,
  BaseWordInContextEntry,
  PartOfSpeech,
} from "../../types";
import {
  definitionForLanguagePrompt,
  listAllMeaningPrompt,
  meaningInContextJsonPrompt,
  meaningInContextPrompt,
  meaningInContextShortForLanguagePrompt,
  meaningInContextShortPrompt,
  meaningInContextToJson,
  moreExamplesPrompt,
  moreSynonymsPrompt,
} from "./prompt";
import {
  DefinitionForLanguagePromptResult,
  DictionarySource,
  ExamplePromptResult,
  SynonymPromptResult,
} from "../types";
import { jsonrepair } from "jsonrepair";
import { correctLLMResponse } from "../utils";

export class MistralSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 16000;
  client: Mistral;

  constructor(config: {
    modelId: number;
    modelName: string;
    maxToken?: number;
    apiKey?: string;
  }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.client = new Mistral({
      apiKey: this.apiKey,
    });
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const completion = await this.client.chat.complete({
      model: this.modelName,
      messages: [
        { role: "system", content: listAllMeaningPrompt },
        { role: "user", content: word },
      ],
      maxTokens: this.maxTokens,
      temperature: 0.5,
    });

    let message = completion.choices![0].message.content as string;
    message = message.replace('```json', '');
    message = message.replace('```', '');

    console.log(`Mistral-${this.modelName}-listAllMeanings`, message);
    console.log(
      `${this.modelName}-promptTokens`,
      completion.usage?.promptTokens
    );
    console.log(
      `${this.modelName}-completionTokens`,
      completion.usage?.completionTokens
    );
    console.log(`${this.modelName}-totalTokens`, completion.usage?.totalTokens);

    if (!message) throw new Error("Cannot parse response");

    const { definitions } = JSON.parse(jsonrepair(message));
    const data: BaseWordEntry = {
      word,
      llm_model: this.modelId,
      definitions: definitions.map(
        (d: {
          partOfSpeech: PartOfSpeech;
          definition: string;
          example: string;
          context?: string;
        }) => ({
          partOfSpeech: d.partOfSpeech,
          definition: d.definition,
          context: d.context,
          examples: [d.example],
          synonyms: [],
        })
      ),
    };
    return data;
  }

  async listAllMeaningsForLanguage(
    input: string,
    language: string
  ): Promise<DefinitionForLanguagePromptResult> {
    const completion = await this.client.chat.complete({
      model: this.modelName,
      messages: [
        { role: "system", content: definitionForLanguagePrompt(language) },
        { role: "user", content: input },
      ],
      maxTokens: this.maxTokens,
      temperature: 0.5,
    });

    let message = completion.choices![0].message.content as string;
    message = message.replace('```json', '');
    message = message.replace('```', '');

    console.log(
      `Mistral-${this.modelName}-listAllMeaningsForLanguage`,
      message
    );
    console.log(
      `${this.modelName}-promptTokens`,
      completion.usage?.promptTokens
    );
    console.log(
      `${this.modelName}-completionTokens`,
      completion.usage?.completionTokens
    );
    console.log(`${this.modelName}-totalTokens`, completion.usage?.totalTokens);

    if (!message) throw new Error("Cannot parse response");

    const { definitions } = JSON.parse(jsonrepair(message));
    return definitions;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const completion = await this.client.chat.complete({
      model: this.modelName,
      messages: [
        { role: "system", content: moreExamplesPrompt },
        { role: "user", content: input },
      ],
      maxTokens: this.maxTokens,
      temperature: 0.5,
    });

    let message = completion.choices![0].message.content as string;
    message = message.replace('```json', '');
    message = message.replace('```', '');

    console.log(`Mistral-${this.modelName}-getMoreExamples`, message);
    console.log(
      `${this.modelName}-promptTokens`,
      completion.usage?.promptTokens
    );
    console.log(
      `${this.modelName}-completionTokens`,
      completion.usage?.completionTokens
    );
    console.log(`${this.modelName}-totalTokens`, completion.usage?.totalTokens);

    if (!message) throw new Error("Cannot parse response");

    const { result } = JSON.parse(jsonrepair(message));
    return result;
  }

  async getMoreSynonymsForDefinition(
    input: string
  ): Promise<SynonymPromptResult> {
    const completion = await this.client.chat.complete({
      model: this.modelName,
      messages: [
        { role: "system", content: moreSynonymsPrompt },
        { role: "user", content: input },
      ],
      maxTokens: this.maxTokens,
      temperature: 0.5,
    });

    let message = completion.choices![0].message.content as string;
    message = message.replace('```json', '');
    message = message.replace('```', '');

    console.log(
      `Mistral-${this.modelName}-getMoreSynonymsForDefinition`,
      message
    );
    console.log(
      `${this.modelName}-promptTokens`,
      completion.usage?.promptTokens
    );
    console.log(
      `${this.modelName}-completionTokens`,
      completion.usage?.completionTokens
    );
    console.log(`${this.modelName}-totalTokens`, completion.usage?.totalTokens);

    if (!message) throw new Error("Cannot parse response");

    const { result } = JSON.parse(jsonrepair(message));
    return result;
  }

  // async getMeaningInContext(
  //   word: string,
  //   context: string,
  //   offset: number
  // ): Promise<BaseWordInContextEntry> {
  //   let completion = await this.client.chat.complete({
  //     model: this.modelName,
  //     messages: [
  //       { role: "user", content: meaningInContextPrompt(word, context) },
  //     ],
  //     maxTokens: this.maxTokens,
  //     temperature: 0.5,
  //   });
  //   let message = completion.choices![0].message.content as string;
  //   message = message.replace('```json', '');
  //   message = message.replace('```', '');

  //   console.log(`Mistral-${this.modelName}-getMeaningInContext`, message);
  //   console.log(
  //     `${this.modelName}-promptTokens`,
  //     completion.usage?.promptTokens
  //   );
  //   console.log(
  //     `${this.modelName}-completionTokens`,
  //     completion.usage?.completionTokens
  //   );
  //   console.log(
  //     `${this.modelName}-totalTokens`,
  //     completion.usage?.totalTokens
  //   );

  //   if (!message) throw new Error("Cannot parse response");

  //   completion = await this.client.chat.complete({
  //     model: this.modelName,
  //     messages: [{ role: "user", content: meaningInContextToJson(message) }],
  //     maxTokens: this.maxTokens,
  //     temperature: 0.5,
  //   });

  //   message = completion.choices![0].message.content as string;
  //   message = message.replace('```json', '');
  //   message = message.replace('```', '');

  //   console.log(`Mistral-${this.modelName}-toJson`, message);
  //   console.log(
  //     `${this.modelName}-promptTokens`,
  //     completion.usage?.promptTokens
  //   );
  //   console.log(
  //     `${this.modelName}-completionTokens`,
  //     completion.usage?.completionTokens
  //   );
  //   console.log(
  //     `${this.modelName}-totalTokens`,
  //     completion.usage?.totalTokens
  //   );

  //   if (!message) throw new Error("Cannot parse response");

  //   const result = JSON.parse(jsonrepair(message));
  //   return {
  //     word,
  //     context,
  //     offset,
  //     definition: result,
  //     llm_model: this.modelId,
  //   };
  // }
  async getMeaningInContext(
    word: string,
    context: string,
    offset: number
  ): Promise<BaseWordInContextEntry> {
    let completion = await this.client.chat.complete({
      model: this.modelName,
      messages: [
        { role: "user", content: meaningInContextJsonPrompt(word, context) },
      ],
      maxTokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completion.choices![0].message.content as string;

    console.log(`Mistral-${this.modelName}-getMeaningInContextJson`, message);
    console.log(
      `${this.modelName}-promptTokens`,
      completion.usage?.promptTokens
    );
    console.log(
      `${this.modelName}-completionTokens`,
      completion.usage?.completionTokens
    );
    console.log(
      `${this.modelName}-totalTokens`,
      completion.usage?.totalTokens
    );

    if (!message) throw new Error("Cannot parse response");
    message = correctLLMResponse(message);

    const result = JSON.parse(jsonrepair(message));
    return {
      word,
      context,
      offset,
      definition: result,
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completion = await this.client.chat.complete({
      model: this.modelName,
      messages: [
        { role: "user", content: meaningInContextShortPrompt(word, context) },
      ],
      maxTokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completion.choices![0].message.content as string;

    console.log(`Mistral-${this.modelName}-getMeaningInContextJson`, message);
    console.log(
      `${this.modelName}-promptTokens`,
      completion.usage?.promptTokens
    );
    console.log(
      `${this.modelName}-completionTokens`,
      completion.usage?.completionTokens
    );
    console.log(
      `${this.modelName}-totalTokens`,
      completion.usage?.totalTokens
    );

    if (!message) throw new Error("Cannot parse response");

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: message,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string,
  ): Promise<string> {
    let completion = await this.client.chat.complete({
      model: this.modelName,
      messages: [
        { role: "user", content: meaningInContextShortForLanguagePrompt(word, context, language) },
      ],
      maxTokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completion.choices![0].message.content as string;

    console.log(`Mistral-${this.modelName}-getMeaningInContextJson`, message);
    console.log(
      `${this.modelName}-promptTokens`,
      completion.usage?.promptTokens
    );
    console.log(
      `${this.modelName}-completionTokens`,
      completion.usage?.completionTokens
    );
    console.log(
      `${this.modelName}-totalTokens`,
      completion.usage?.totalTokens
    );

    if (!message) throw new Error("Cannot parse response");

    return message;
  }
}
