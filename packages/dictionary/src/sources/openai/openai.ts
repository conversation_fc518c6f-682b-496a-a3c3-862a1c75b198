import OpenAI from "openai";
import { zodResponseFormat } from "openai/helpers/zod";
import { z } from "zod";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech } from "../../types";
import { definitionForLanguagePrompt, listAllMeaningPrompt, meaningInContextJsonPrompt, meaningInContextPrompt, meaningInContextShortPrompt, meaningInContextToJson, moreExamplesPrompt, moreSynonymsPrompt } from "./prompt";
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { jsonrepair } from "jsonrepair";
import { correctLLMResponse } from "../utils";

export class OpenAISource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 16000;
  openAI: OpenAI;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey?: string }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.openAI = new OpenAI({ apiKey: this.apiKey, dangerouslyAllowBrowser: true });
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const definitionsFormat = z.object({
      definitions: z.array(z.object({
        partOfSpeech: z.string(),
        definition: z.string(),
        example: z.string(),
        context: z.string().optional(),
      })),
    });

    const completion = await this.openAI.beta.chat.completions.parse({
      model: this.modelName,
      messages: [
        { role: 'system', content: listAllMeaningPrompt },
        { role: 'user', content: word }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
      response_format: zodResponseFormat(definitionsFormat, "definitions"),
    });

    const message = completion.choices[0].message.parsed;
    console.log(`${this.modelName}-listAllMeanings`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    const { definitions } = message;
    const data: BaseWordEntry = {
      word,
      llm_model: this.modelId,
      definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
        partOfSpeech: d.partOfSpeech,
        definition: d.definition,
        context: d.context,
        examples: [d.example],
        synonyms: []
      }))
    };
    return data;
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const definitionsFormat = z.object({
      definitions: z.array(z.object({
        word: z.string().optional(),
        definition: z.string(),
        context: z.string().optional()
      })),
    });

    const completion = await this.openAI.beta.chat.completions.parse({
      model: this.modelName,
      messages: [
        { role: 'system', content: definitionForLanguagePrompt(language) },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
      response_format: zodResponseFormat(definitionsFormat, "definitions"),
    });


    const message = completion.choices[0].message.parsed;
    console.log(`${this.modelName}-listAllMeaningsForLanguage`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    const { definitions } = message;
    return definitions;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const examplesFormat = z.object({
      result: z.array(z.object({
        examples: z.array(z.string()),
      })),
    });

    const completion = await this.openAI.beta.chat.completions.parse({
      model: this.modelName,
      messages: [
        { role: 'system', content: moreExamplesPrompt },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
      response_format: zodResponseFormat(examplesFormat, "examples"),
    });


    const message = completion.choices[0].message.parsed;
    console.log(`${this.modelName}-getMoreExamples`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    const { result } = message;
    return result;
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const synonymsFormat = z.object({
      result: z.array(z.object({
        synonym: z.string(),
        example: z.string(),
      })),
    });

    const completion = await this.openAI.beta.chat.completions.parse({
      model: this.modelName,
      messages: [
        { role: 'system', content: moreSynonymsPrompt },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
      response_format: zodResponseFormat(synonymsFormat, "examples"),
    });

    const message = completion.choices[0].message.parsed;
    console.log(`${this.modelName}-getMoreSynonymsForDefinition`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    const { result } = message;
    return result;
  }

  // async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
  //   let completions = await this.openAI.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextPrompt(word, context) },
  //     ],
  //     max_tokens: this.maxTokens,
  //     temperature: 0.5,
  //   });
  //   let message = completions.choices[0].message.content;
  //   console.log(`${this.modelName}-getMeaningInContext`, message);
  //   console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

  //   if (!message) throw new Error('Cannot parse response');

  //   completions = await this.openAI.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextToJson(message) },
  //     ],
  //     max_tokens: this.maxTokens,
  //     temperature: 0.5,
  //   });

  //   message = completions.choices[0].message.content;
  //   console.log(`${this.modelName}-toJson`, message);
  //   console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

  //   if (!message) throw new Error('Cannot parse response');

  //   const result = JSON.parse(jsonrepair(message));
  //   return {
  //     word,
  //     context,
  //     offset,
  //     definition: result,
  //     llm_model: this.modelId,
  //   };
  // }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextJsonPrompt(word, context) },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    message = correctLLMResponse(message);

    const result = JSON.parse(jsonrepair(message));
    return {
      word,
      context,
      offset,
      definition: result,
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextShortPrompt(word, context) },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: message,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextShortPrompt(word, context) },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    return message;
  }
}
