# Mobile App Strategy for Pickvocab

This document outlines the strategy for developing a Pickvocab mobile application for iOS and Android, based on the existing Nuxt.js web application. It covers the framework evaluation, technical requirements, and recommended approach.

## 1. Initial Feasibility

The existing `pickvocab-client` is built with Nuxt.js (a Vue.js framework). This makes it highly feasible to create mobile applications without starting from scratch. By using a web-native runtime like Capacitor, we can wrap the existing web application in a native shell for both iOS and Android, sharing the vast majority of the codebase.

## 2. Core Requirement: System-Wide Text Lookup

The primary "killer feature" for the mobile app is to allow users to select text in *any* third-party application (e.g., a web browser, a news app) and use Pickvocab to look it up. This requires deep integration with the mobile operating systems.

*   **Android**: The standard mechanism is to add a custom option to the **Text Selection Toolbar**. This is achieved by creating a native `Activity` that handles the `PROCESS_TEXT` intent. When a user selects text and chooses the "Pickvocab" option, the selected text is sent to our app.

*   **iOS**: iOS does not allow direct modification of the text selection menu system-wide. The standard approach is to create a **Share Extension**. When a user selects text and taps the "Share" button, our app's extension appears as a target, allowing the user to send the text to <PERSON><PERSON><PERSON> for lookup.

A key finding is that **no cross-platform framework can implement this system-wide feature without some native code.** The framework choice, therefore, depends on which one best leverages our existing assets and skills while providing a robust bridge to native functionality.

## 3. Framework Comparison

We evaluated three leading cross-platform solutions against building natively.

| Feature | Capacitor | React Native | Flutter | Native (Swift/Kotlin) |
| :--- | :--- | :--- | :--- | :--- |
| **Core Technology** | HTML, CSS, JavaScript (Vue, React, etc.) | JavaScript/TypeScript with React | Dart | Swift (iOS) or Kotlin/Java (Android) |
| **Performance** | Good (WebView-based) | Very Good (Native UI) | Excellent (Skia Engine) | Best |
| **Code Re-use from Web** | **Highest**. The existing Nuxt.js app is used almost entirely. | Low. Requires a rewrite in React. | Low. Requires a rewrite in Dart. | None. |
| **Team Skills** | **Excellent match**. Leverages existing web development expertise. | Requires React expertise. | Requires learning Dart. | Requires dedicated iOS and Android teams. |
| **Effort for System-Wide Lookup** | **Low-Medium**. Requires writing a focused native plugin. | **Medium**. Requires writing a native module. | **Medium**. Requires using platform channels. | **High**. Requires full native development. |
| **In-App Context Menu** | Challenging for native look-and-feel. | Possible with third-party libraries. | **Excellent native support**. The `contextMenuBuilder` provides full control. | Full native control. |

## 4. Communication Between Web and Native

A critical question was whether the chosen framework allows the web-based UI to communicate with the native code required for the system-wide lookup.

**Capacitor is explicitly designed for this.** It provides a robust **Plugin API** that acts as a bridge between the JavaScript running in the WebView and the native platform code.

This allows us to:
1.  Define a clear API in TypeScript (e.g., `LookupPlugin.onTextReceived(...)`).
2.  Implement the native side of this API in Swift (for iOS) and Kotlin/Java (for Android).
3.  Call the JavaScript API from our Vue.js components, and have the native code execute in response.
4.  Have the native code emit events back to the JavaScript layer when it receives text from the operating system.

## 5. Recommendation: Capacitor

**Capacitor is the unequivocally recommended framework for this project.**

It is the only solution that allows us to leverage our significant investment in the existing Nuxt.js codebase. The development path is faster, more cost-effective, and aligns with our current team's skillset. While Flutter has superior in-app context menu support, our primary feature is *system-wide*, which requires native code regardless of the framework. Capacitor provides the most direct path to achieving this while retaining our web app's code.

### Proposed Architecture with Capacitor

The application will consist of the Nuxt.js web app running inside a Capacitor native shell. A custom local plugin, `LookupPlugin`, will handle the bi-directional communication for the text-sharing feature.

```mermaid
graph TD
    subgraph "System-Wide OS Interaction"
        OS_Android[Android: PROCESS_TEXT Intent]
        OS_iOS[iOS: Share Extension]
    end

    subgraph "Native Layer (Swift/Kotlin)"
        Plugin_Native[LookupPlugin: Native Implementation]
    end

    subgraph "Capacitor Bridge"
        Bridge[Plugin API]
    end

    subgraph "Web Layer (Vue.js in WebView)"
        WebApp[Pickvocab Nuxt App] -- Calls --> Plugin_JS[LookupPlugin: JS Interface]
    end

    OS_Android -- Sends text to --> Plugin_Native
    OS_iOS -- Sends text to --> Plugin_Native
    Plugin_Native -- Communicates via --> Bridge
    Bridge -- Communicates with --> Plugin_JS
    Plugin_JS -- Emits event to --> WebApp