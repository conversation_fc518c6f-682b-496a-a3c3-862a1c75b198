# Individual Card Caching Implementation Plan

## Overview
This plan implements comprehensive caching for individual cards to eliminate redundant API calls and provide instant loading when users click to view cards from lists. The solution builds on the existing TanStack Query patterns while adding optimistic updates for core mutations.

## Current Problem Analysis

### Issues with Current Implementation:
- `useCardsQuery` effectively caches card lists
- When users click a card, `CardView.vue` refetches the same card data via `store.getGenericCard()`
- This causes unnecessary loading delays and API calls for data already available in lists
- No caching for direct URL access to cards
- No optimistic updates for user actions

### Current Card Mutation Actions:
1. **Delete Card** - `CardView.vue`, `AllCardView.vue`
2. **Update Definition Card** (Add Examples) - `CardViewAddExampleModal.vue`
3. **Create Definition Card** - `AddCardModal.vue` (navigation requires server ID)
4. **Create Context Card** - Various components (navigation requires server ID)
5. **Search Cards** - Search functionality
6. **Similarity Search** - Background embedding operations

## Solution: Individual Card Caching with Selective Optimistic Updates

### Key Requirements Addressed:
✅ Cache individual cards when fetched from lists or direct URLs  
✅ Background sync indicator similar to AllCardView  
✅ Optimistic updates for delete and update operations (NOT create)  
✅ Direct URL caching for `/app/cards/[slug]/[id]` routes  

### Why No Optimistic Updates for Card Creation:
- Navigation requires server-generated ID: `router.push({ name: 'app-cards-slug-id', params: { id: card.id } })`
- Current approach provides reliable user experience with fully-loaded card pages
- Low frequency operation doesn't justify the architectural complexity

## Implementation Plan

### 1. Create `useCardQuery.ts` Composable
**File:** `pickvocab-client/composables/useCardQuery.ts`

**Features:**
- Individual card fetching with TanStack Query caching
- Cache seeding from list data (when cards fetched in lists, populate individual cache)
- Support direct URL access with caching
- Background sync indicator integration
- Query keys: `['card', cardId]` and `['card_embedding', cardId]`
- Manual invalidation helpers

**Functions:**
```typescript
export function useCardQuery(cardId: Ref<number>, options?)
export function useInvalidateCardQuery()
export function useCardEmbeddingQuery(cardId: Ref<number>)
```

### 2. Create `useCardMutations.ts` Composable  
**File:** `pickvocab-client/composables/useCardMutations.ts`

**Features:**
- `useDeleteCard()` - Immediate removal + rollback on failure
- `useUpdateDefinitionCard()` - Instant example updates + background sync
- Error handling with automatic rollback mechanisms
- Integration with existing store methods

**Functions:**
```typescript
export function useDeleteCard()
export function useUpdateDefinitionCard()
```

### 3. Enhanced `CardView.vue`
**Updates:**
- Replace `store.getGenericCard()` with `useCardQuery()`
- Add background sync spinner (top-right corner like AllCardView)
- Cache card data when accessed via direct URL
- Optimistic delete with immediate UI feedback
- Maintain embedding status functionality
- Remove manual loading state management

**Background Sync Indicator:**
```vue
<div 
  v-if="isFetching && !isPending" 
  class="fixed top-20 right-6 z-50 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
>
  <Spinner class="w-4 h-4 text-blue-500" />
</div>
```

### 4. Enhanced `AllCardView.vue`
**Updates:**
- Populate individual card cache when lists are fetched using `queryClient.setQueryData()`
- Prefetch cards on hover/focus for instant loading
- Optimistic card deletion from lists
- Cache invalidation coordination between list and individual cards

**Cache Population Strategy:**
```typescript
// When list data loads, seed individual card cache
watch(queryData, (newData) => {
  if (newData?.result) {
    newData.result.forEach(card => {
      queryClient.setQueryData(['card', card.id], card)
    })
  }
})
```

### 5. Enhanced `CardViewAddExampleModal.vue`
**Updates:**
- Optimistic example additions using `useUpdateDefinitionCard()`
- Immediate UI updates with background sync
- Error handling with rollback capability
- Loading states on form submission

### 6. Enhanced `useCardsQuery.ts`
**Updates:**
- Add cache population logic for individual cards
- Coordinate invalidation between list and individual caches
- Maintain existing functionality

### 7. Cache Management Strategy

#### Cache Architecture:
```
Card Caches:
├── ['all_cards', { page, isDemoPage, owner }] - List pagination
├── ['card', cardId] - Individual card details  
├── ['card_embedding', cardId] - Embedding status
└── ['card_search', query] - Search results (future)
```

#### Cache Population Flow:
1. **From Lists**: `useCardsQuery` populates individual card cache automatically
2. **Direct URL**: `useCardQuery` caches individual cards for future instant access
3. **No Prefetching Needed**: List data already contains full card information

#### Cache Invalidation Strategy:
- **Delete Operations**: Remove individual card + invalidate related list pages
- **Update Operations**: Update individual card cache + selective list invalidation
- **Smart Targeting**: Avoid global invalidation, target specific cache entries
- **Consistency**: Maintain sync between list and individual card data

### 8. Background Sync & Error Handling

#### Sync Indicators:
- Fixed position spinner (top-right like AllCardView) during background operations
- Show when `isFetching && !isPending`
- Non-intrusive loading indication that doesn't block UI

#### Error Handling:
- Automatic rollback for failed optimistic updates
- Toast notifications for operation feedback
- Retry mechanisms for failed operations
- Graceful degradation for network issues

#### Optimistic Update Flow:
```
User Action → Immediate UI Update → Background API Call → Success: Keep Changes | Error: Rollback + Show Error
```

## Expected User Experience

### Before Implementation:
- Click card from list → Loading spinner → Card displays
- Direct URL access → Loading spinner → Card displays  
- Delete card → Loading spinner → Success message
- Add examples → Loading spinner → Modal closes

### After Implementation:
- Click card from list → **Instant display** (cached from list) + subtle background sync
- Direct URL access → Loading → Card displays + **cached for future instant access**
- Delete card → **Instant removal** + background confirmation + rollback if failed
- Add examples → **Instant preview** + background save + sync indicator

### Performance Benefits:
- Eliminate redundant API calls for cards already in cache
- Instant navigation between previously viewed cards
- Predictive loading through prefetching
- Reduced server load through intelligent caching

## Migration Strategy

### Phase 1: Core Infrastructure
1. Create `useCardQuery.ts` and `useCardMutations.ts` composables
2. Update `CardView.vue` to use new caching system
3. Test direct URL caching and background sync indicators

### Phase 2: Optimistic Updates
1. Implement optimistic delete operations
2. Add optimistic update for card examples
3. Enhance error handling and rollback mechanisms

### Phase 3: Cache Optimization
1. Add cache population from list queries
2. Implement prefetching capabilities
3. Optimize cache invalidation patterns

### Phase 4: Polish & Testing
1. Add comprehensive error handling
2. Performance testing and optimization
3. User acceptance testing

## Benefits

### User Experience:
- **Instant Loading**: Previously viewed cards load immediately
- **Responsive UI**: Optimistic updates provide immediate feedback
- **Clear Status**: Background sync indicators show operation progress
- **Error Recovery**: Graceful handling of failed operations

### Performance:
- **Reduced API Calls**: Eliminate redundant fetches through intelligent caching
- **Memory Efficient**: TanStack Query's automatic garbage collection
- **Network Optimization**: Prefetching and background sync reduce perceived latency

### Technical:
- **Type Safety**: Full TypeScript support with existing patterns
- **Maintainable**: Builds on established TanStack Query patterns
- **Scalable**: Composable architecture for easy extension
- **Robust**: Comprehensive error handling and rollback mechanisms

## Files to Create:
1. `pickvocab-client/composables/useCardQuery.ts`
2. `pickvocab-client/composables/useCardMutations.ts`

## Files to Modify:
1. `pickvocab-client/components/app/cards/CardView.vue`
2. `pickvocab-client/components/app/cards/AllCardView.vue` 
3. `pickvocab-client/components/app/cards/CardViewAddExampleModal.vue`
4. `pickvocab-client/composables/useCardsQuery.ts`

## Success Metrics:
- Reduced API calls for individual card views
- Faster perceived performance for card navigation
- Improved user satisfaction with instant UI feedback
- Reduced server load through efficient caching