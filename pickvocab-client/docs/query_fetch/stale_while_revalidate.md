# Modern Data Fetching in Vue: A Guide to `swrv` and TanStack Query

This guide covers modern data-fetching techniques in Vue, from the basics of `swrv` to advanced optimistic UI patterns with TanStack Query.

---

## 1. Getting Started with `swrv`

`swrv` (pronounced "swerve") is a lightweight Vue library for remote data fetching built upon the "stale-while-revalidate" caching strategy.

### Core Concept: Stale-While-Revalidate

This strategy allows your application to feel fast and responsive by:
1.  **Stale**: Immediately showing cached (stale) data.
2.  **While-Revalidate**: Simultaneously sending a request to fetch the latest data in the background.
3.  **Update**: Updating the UI with the fresh data once it arrives.

### Installation

```bash
# For Vue 3
npm install swrv

# For Vue 2.7
npm install swrv@v2-latest
```

### Basic Usage

Import the `useSWRV` hook within your component's `setup` function.

```vue
<template>
  <div>
    <div v-if="error">Failed to load</div>
    <div v-if="!data">Loading...</div>
    <div v-else>Hello, {{ data.name }}!</div>
  </div>
</template>

<script setup>
import useSWRV from 'swrv';

// A fetcher is any async function that returns data.
const fetcher = (url) => fetch(url).then((res) => res.json());

// useSWRV returns reactive refs
const { data, error } = useSWRV('/api/user', fetcher);
</script>
```

### Key Use Cases of `swrv`

-   **Fast Page Navigation**: By serving cached data first, navigations between previously visited pages feel instant.
-   **Reactive & Real-time UI**: Automatically refetches data on window focus (`revalidateOnFocus`) or at regular intervals (`refreshInterval`), which is great for dashboards or feeds.
-   **Simplified UI Patterns**: Drastically simplifies the logic for pagination and infinite scrolling.
-   **Improved Performance**: Reduces API calls through request deduplication (if multiple components request the same data, only one request is sent).

---

## 2. Manual Control with `mutate`

The `mutate` function is your direct write-access to the `swrv` cache, allowing you to manually update data without a refetch. This is the key to creating optimistic UI.

The function signature is `mutate(newData, options)`.

-   `newData`: The data you want to inject into the cache. Can be the data itself or a function `(currentData) => new_data`.
-   `options.revalidate`: A boolean that controls if `swrv` should perform a background refetch after updating the cache.
    -   `true` (default): Useful for "refresh" buttons.
    -   `false`: **Crucial for optimistic UI**. It updates the UI *without* triggering a network request.

### Example: Optimistic Update

```vue
<script setup>
import useSWRV from 'swrv';

const fetcher = (url) => fetch(url).then(res => res.json());
const { data, mutate } = useSWRV('/api/user', fetcher);

const updateName = async () => {
  const newName = 'Optimistic Name';
  const previousData = { ...data.value }; // Store old data for rollback

  // 1. Optimistically update the cache. The UI updates instantly.
  //    `revalidate` is false because we are handling the fetch ourselves.
  mutate(
    (currentUser) => ({ ...currentUser, name: newName }),
    { revalidate: false }
  );

  try {
    // 2. Send the actual request to the server in the background.
    await fetch('/api/user', {
      method: 'PATCH',
      body: JSON.stringify({ name: newName }),
    });

    // 3. (Optional) Revalidate to get the official server state.
    mutate();
  } catch (e) {
    // 4. If the request fails, roll back to the previous data.
    mutate(previousData, false);
    alert('Update failed!');
  }
};
</script>
```

---

## 3. UI Patterns: Pagination and Infinite Scroll

### Pagination

`swrv` makes pagination trivial by using a reactive key.

```vue
<template>
  <div>
    <!-- The isValidating flag is perfect for a loading state -->
    <div v-if="isValidating && !data">Loading...</div>
    <ul v-else>
      <li v-for="project in data.data" :key="project.id">{{ project.name }}</li>
    </ul>

    <div class="navigation">
      <button @click="page--" :disabled="page <= 1">Previous</button>
      <span>Page {{ page }}</span>
      <button @click="page++" :disabled="!data.has_more">Next</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import useSWRV from 'swrv';

const page = ref(1);
const fetcher = (url) => fetch(url).then((res) => res.json());

// The key is a function that depends on the `page` ref.
// When `page` changes, swrv automatically refetches.
const { data, isValidating } = useSWRV(
  () => `/api/projects?page=${page.value}`,
  fetcher,
  // This option prevents a jarring flash of content on page change.
  // It continues showing old data until the new data is loaded.
  { keepPreviousData: true }
);
</script>
```

### Infinite Scrolling with `useSWRVInfinite`

For infinite scrolling, `swrv` provides a dedicated hook.

```vue
<template>
  <div>
    <!-- data is now an array of pages, so we need a nested loop -->
    <div v-for="(page, index) in data" :key="index">
      <div v-for="message in page.messages" :key="message.id">
        <p>{{ message.text }}</p>
      </div>
    </div>
    
    <button @click="setSize(size + 1)" :disabled="isReachingEnd || isValidating">
      {{ isReachingEnd ? 'No more messages' : 'Load More' }}
    </button>
  </div>
</template>

<script setup>
import { useSWRVInfinite } from 'swrv';

const fetcher = (url) => fetch(url).then((res) => res.json());

// `getKey` determines the URL for the next page.
// It receives the index and the data from the previous page.
const getKey = (pageIndex, previousPageData) => {
  // Reached the end
  if (previousPageData && !previousPageData.nextCursor) return null;
  // First page
  if (pageIndex === 0) return '/api/messages';
  // Subsequent pages
  return `/api/messages?cursor=${previousPageData.nextCursor}`;
};

const { data, size, setSize, isValidating } = useSWRVInfinite(getKey, fetcher);
</script>
```

---

## 4. `swrv` vs. TanStack Vue Query

TanStack Query is a more powerful, "batteries-included" alternative to `swrv`.

| Aspect | `swrv` | TanStack Vue Query |
| :--- | :--- | :--- |
| **Philosophy** | Lightweight, simple, elegant | Powerful, flexible, "batteries-included" |
| **API** | `useSWRV` for reads, `mutate` for writes | `useQuery` for reads, `useMutation` for writes |
| **Devtools** | None | Best-in-class, dedicated devtools |
| **Mutations** | Handled via the flexible `mutate` function | Dedicated `useMutation` hook with a structured recipe (`onMutate`, `onSuccess`, `onError`) |
| **Bundle Size** | Very lightweight | Larger, more feature-rich |
| **Ecosystem** | Standalone | Part of the powerful **TanStack** ecosystem |

**Choose `swrv` if:** You prioritize simplicity and a minimal bundle size for straightforward projects.
**Choose TanStack Query if:** You are building a complex, large-scale application and need powerful devtools, granular cache control, and a formal structure for mutations.

---

## 5. Advanced: Optimistic UI with TanStack Query

TanStack Query provides a formal recipe for optimistic updates within its `useMutation` hook, making the logic cleaner and more robust.

### The Optimistic "Add Todo" Recipe

```javascript
const { mutate: addTodo } = useMutation({
  // 1. The function that sends the request to the server
  mutationFn: (newTodo) => fetch('/api/todos', { method: 'POST', body: JSON.stringify(newTodo) }),

  // 2. Runs BEFORE mutationFn. This is where the optimistic magic happens.
  onMutate: async (newTodo) => {
    // A. Cancel any outgoing refetches for this query
    await queryClient.cancelQueries({ queryKey: ['todos'] });

    // B. Snapshot the previous data for rollback
    const previousTodos = queryClient.getQueryData(['todos']);

    // C. Optimistically update the cache. The UI updates instantly.
    queryClient.setQueryData(['todos'], (old) => [...old, newTodo]);

    // D. Return the snapshot to be used as context in onError
    return { previousTodos };
  },

  // 3. If the mutation fails, use the context to roll back
  onError: (err, newTodo, context) => {
    queryClient.setQueryData(['todos'], context.previousTodos);
  },

  // 4. Always refetch after success or error to ensure consistency
  onSettled: () => {
    queryClient.invalidateQueries({ queryKey: ['todos'] });
  },
});
```

---

## 6. The Redirect Problem: Handling Server-Generated IDs

How can you optimistically redirect to a new page (e.g., `/posts/123`) when the ID (`123`) is only generated by the server *after* the request succeeds?

### Pattern 1: Standard Redirect (Recommended)

Wait for the server response in `onSuccess` and then perform the redirect. This is the simplest and most reliable pattern.

```javascript
const { mutate } = useMutation({
  mutationFn: createPostAPI, // This function must return the server's response
  onSuccess: (data) => {
    // `data` is the new post from the server, e.g., { id: 123, ... }
    // Now we have the real ID and can redirect.
    router.push(`/posts/${data.id}`);
  },
});
```
**Experience:** Click -> See loading state -> Redirect after a short delay.

### Pattern 2: "Soft Navigation" Hack

Instantly hide the form and show a "fake" skeleton view of the new page. When `onSuccess` fires, do a real redirect.

```javascript
const isOptimisticallyNavigating = ref(false);

const { mutate } = useMutation({
  mutationFn: createPostAPI,
  onMutate: () => {
    // Instantly hide the form and show a skeleton UI
    isOptimisticallyNavigating.value = true; 
  },
  onSuccess: (data) => {
    // Replace the fake view with the real one
    router.replace(`/posts/${data.id}`); 
  },
  onError: () => {
    // Show the form again on failure
    isOptimisticallyNavigating.value = false; 
  }
});
```
**Experience:** Click -> Instantly see a new view (at the old URL) -> URL and final content pop in a moment later. Feels faster but is more complex and the URL is temporarily out of sync.

### Pattern 3: The Gold Standard - Client-Side UUIDs

This is the ultimate solution but requires backend changes. The client generates a UUID, making the final URL known *before* the request is sent.

**Requirements:**
-   A UUID library on the client (e.g., `uuid`).
-   Your backend API must accept a client-provided ID.
-   Your database primary key must support UUIDs.

```javascript
import { v4 as uuidv4 } from 'uuid';

const { mutate } = useMutation({
  mutationFn: createPostAPI,
  onMutate: async (newPost) => {
    // Because we know the permanent ID, we can navigate instantly.
    router.push(`/posts/${newPost.id}`);

    // We can also optimistically update the posts list...
    // ...
  },
  // ... onError and onSettled for cleanup ...
});

const handleCreatePost = () => {
  // 1. Generate the ID on the client FIRST
  const newPostId = uuidv4();

  // 2. Call the mutation with the complete object
  createPost({
    id: newPostId,
    title: title.value,
  });
};
```
**Experience:** Click -> Instantly navigate to the final, permanent URL. The experience is seamless and feels magical. This is the most robust but most architecturally involved solution.
