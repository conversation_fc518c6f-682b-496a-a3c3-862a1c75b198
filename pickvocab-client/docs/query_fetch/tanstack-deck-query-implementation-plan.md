# TanStack Vue Query Implementation Plan for Deck Components

## Overview
This plan implements TanStack Vue Query for deck components following the successful patterns used in card components. The implementation provides intelligent caching, background sync, and optimistic updates for better user experience.

## Component Analysis

### AllDeckView.vue
- **Current**: Fetches deck metadata only via `store.listDecks()`
- **Pattern**: Simple list caching without individual deck cache population
- **API**: Returns array of deck metadata (no pagination)

### DeckView.vue  
- **Current**: Fetches full deck with cards via `store.getDeck()` with pagination
- **Pattern**: Individual deck caching with card cache population (like AllCardView)
- **API**: Returns deck with paginated cards, similar to AllCardView structure

## Implementation Plan

### 1. Create useDecksQuery.ts Composable
**File**: `pickvocab-client/composables/useDecksQuery.ts`

**Features**:
- Simple deck list caching (metadata only)
- Query key: `['all_decks', { isDemoPage, owner }]`
- No individual deck cache population (as requested)
- Manual invalidation helpers for deck CRUD operations

**Functions**:
```typescript
export function useDecksQuery(isDemoPage: boolean = false)
export function useInvalidateDecksQuery()
```

### 2. Create useDeckQuery.ts Composable
**File**: `pickvocab-client/composables/useDeckQuery.ts`

**Features**:
- Individual deck fetching with pagination support
- Query key: `['deck', deckId, page]`
- **Card cache population** (like AllCardView pattern)
- Background sync indicators
- Integration with existing pagination logic

**Functions**:
```typescript
export function useDeckQuery(deckId: Ref<number>, page: Ref<number>)
export function useInvalidateDeckQuery()
export function useDeckQueryWithCardCachePopulation()
```

### 3. Create useDeckMutations.ts Composable
**File**: `pickvocab-client/composables/useDeckMutations.ts`

**Features**:
- Optimistic deck deletion
- Optimistic card removal from decks
- Automatic cache invalidation coordination
- Error handling with rollback

**Functions**:
```typescript
export function useDeleteDeck()
export function useRemoveCardFromDeck()
```

### 4. Update AllDeckView.vue
**Changes**:
- Replace `fetchDecks()` with `useDecksQuery()`
- Add background loading indicator (top-right corner)
- Optimistic deck deletion
- Remove manual loading state management
- Use TanStack Query loading states (`isPending`, `isFetching`, `error`)

### 5. Update DeckView.vue
**Changes**:
- Replace `initDeck()` with `useDeckQuery()`
- **Populate individual card cache** when deck data loads
- Add background sync indicator during page changes
- Maintain existing pagination with improved caching
- Cache invalidation on card operations
- Integrate with existing route watching logic

## Cache Architecture

### Cache Structure
```
Deck Caches:
├── ['all_decks', { isDemoPage, owner }] - Deck list metadata
├── ['deck', deckId, page] - Individual deck with paginated cards
└── ['card', cardId] - Individual cards (populated from deck view)
```

### Cache Population Flow
1. **AllDeckView**: Caches deck metadata only, no population
2. **DeckView**: Caches deck + populates individual card cache for instant navigation
3. **Card Navigation**: Cards available instantly in CardView via populated cache

### Cache Invalidation Strategy
- **Deck Deletion**: Remove from deck list + individual deck cache
- **Card Operations**: Update deck cache + individual card cache
- **Smart Targeting**: Avoid global invalidation, target specific entries

## Expected User Experience

### Before Implementation
- Navigate to decks → Loading spinner → Deck list displays
- Click deck → Loading spinner → Deck view displays
- Navigate pages → Loading spinner → New page displays
- Click card → Loading spinner → Card displays (separate fetch)

### After Implementation
- Navigate to decks → **Instant display** (if cached) + background refresh
- Click deck → Loading → Deck displays + **cards cached for navigation**
- Navigate pages → **Instant cached page** + background fresh data
- Click card → **Instant display** (cached from deck view)

## Implementation Steps

### Phase 1: Core Infrastructure
1. Create `useDecksQuery.ts` composable
2. Create `useDeckQuery.ts` composable
3. Create `useDeckMutations.ts` composable

### Phase 2: Component Updates
4. Update `AllDeckView.vue` with TanStack Query
5. Update `DeckView.vue` with caching and card population

### Phase 3: Testing & Polish
6. Test caching behavior and user experience
7. Verify card cache population works correctly
8. Test optimistic updates and error handling

## Files to Create
1. `pickvocab-client/composables/useDecksQuery.ts`
2. `pickvocab-client/composables/useDeckQuery.ts`
3. `pickvocab-client/composables/useDeckMutations.ts`

## Files to Modify
1. `pickvocab-client/components/app/decks/AllDeckView.vue`
2. `pickvocab-client/components/app/decks/DeckView.vue`

## Success Metrics
- Instant deck list navigation with background refresh
- Cached deck pages for immediate display
- Individual cards cached for instant CardView navigation
- Reduced API calls through intelligent caching
- Improved perceived performance and user experience

## Technical Benefits
- **Consistency**: Same patterns as successful card implementation
- **Performance**: Intelligent caching reduces redundant API calls
- **UX**: Background sync with non-blocking loading indicators
- **Maintainability**: Reusable composable architecture
- **Type Safety**: Full TypeScript support with existing patterns