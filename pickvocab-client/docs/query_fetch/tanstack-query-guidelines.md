# TanStack Vue Query Guidelines

## Overview
This document provides comprehensive guidelines for implementing TanStack Vue Query in Pickvocab components. The primary focus is on delivering exceptional user experience through intelligent caching, background sync, and optimistic updates.

## Core Principles

### 1. User Experience First
- **Never show global spinners** when cached data is available
- **Always render data immediately** if it exists in cache
- **Use background sync indicators** for non-blocking updates
- **Provide instant feedback** for user actions through optimistic updates

### 2. Always Refetch on Navigation
- **Trigger background refetch** when first visiting a component
- **Ensure fresh data** without blocking the UI
- **Maintain cache** for instant subsequent visits

### 3. Optimistic Updates
- **Implement optimistic updates** for update and delete operations
- **Provide immediate UI feedback** before server confirmation
- **Handle rollback gracefully** on failure
- **No optimistic updates for create operations** - navigate to object page using server-generated ID

## Implementation Patterns

### 1. Query Hook Structure

#### Basic Query Pattern
```typescript
// composables/useEntityQuery.ts
import { useQuery, useQueryClient } from '@tanstack/vue-query'

export function useEntityQuery(entityId: Ref<number>) {
  const store = useAppStore()

  return useQuery({
    queryKey: ['entity', entityId.value],
    queryFn: async () => await store.getEntity(entityId.value),
    staleTime: Infinity, // Never automatically stale - manual control
    enabled: computed(() => entityId.value !== undefined)
  })
}
```

#### List Query Pattern with Cache Population
```typescript
// composables/useEntitiesQuery.ts
export function useEntitiesQuery(page: Ref<number>, isDemoPage: boolean = false) {
  const store = useAppStore()
  const authStore = useAuthStore()

  const queryKey = computed(() => [
    'all_entities', 
    { 
      page: page.value, 
      isDemoPage,
      owner: isDemoPage ? undefined : authStore.currentUser?.id 
    }
  ])

  return useQuery({
    queryKey,
    queryFn: async () => {
      const params = isDemoPage 
        ? { page: page.value, is_demo: true }
        : { page: page.value, owner: authStore.currentUser?.id }
      
      return await store.listEntities(params)
    },
    staleTime: Infinity,
    enabled: computed(() => !isDemoPage || authStore.currentUser?.id !== undefined)
  })
}

// With automatic individual cache population
export function useEntitiesQueryWithCachePopulation(
  page: Ref<number>, 
  isDemoPage: boolean = false
) {
  const queryClient = useQueryClient()
  const entitiesQuery = useEntitiesQuery(page, isDemoPage)

  // Populate individual entity cache when list data loads
  watch(() => entitiesQuery.data.value, (newData) => {
    if (newData?.result) {
      newData.result.forEach(entity => {
        const entityId = typeof entity.id === 'string' ? parseInt(entity.id) : entity.id
        queryClient.setQueryData(['entity', entityId], entity)
      })
    }
  }, { immediate: true })

  return entitiesQuery
}
```

### 2. Component Integration

#### List Components (e.g., AllEntityView.vue)
```vue
<script setup lang="ts">
import { useEntitiesQuery, useInvalidateEntitiesQuery } from '~/composables/useEntitiesQuery'

const props = withDefaults(defineProps<{
  isDemoPage?: boolean
}>(), { isDemoPage: false })

const route = useRoute()
const router = useRouter()

// TanStack Query integration
const { data: queryData, isPending, isFetching, error, refetch: refetchEntities } = useEntitiesQuery(props.isDemoPage)
const { invalidateAllEntities } = useInvalidateEntitiesQuery()

// Computed properties for template
const renderedEntities = computed(() => queryData.value || [])

// REQUIRED: Watch for route changes to trigger background fetch
watch(() => route.path, (newPath, oldPath) => {
  const isEntitiesPage = newPath?.includes('/entities') && !newPath?.includes('/entities/');
  const wasOnEntitiesPage = oldPath?.includes('/entities') && !oldPath?.includes('/entities/');
  
  if (isEntitiesPage && !wasOnEntitiesPage) {
    refetchEntities() // Background refresh on navigation
  }
}, { immediate: false })

// REQUIRED: Always refetch on mount
onMounted(() => {
  refetchEntities() // Ensure fresh data when component mounts
})

// Optimistic delete handler
async function handleRemoveEntity(entityId: EntityId) {
  if (window.confirm('Are you sure?')) {
    try {
      await store.deleteEntity(entityId)
      await invalidateAllEntities() // Invalidate cache after mutation
    } catch (e) {
      console.error('Failed to remove entity:', e)
      alert('Failed to remove entity. See console for details.')
    }
  }
}
</script>

<template>
  <div class="w-full h-full box-border bg-gray-50">
    <!-- REQUIRED: Background sync indicator -->
    <div 
      v-if="isFetching && !isPending"
      class="fixed top-20 right-6 z-50 transition-opacity duration-200 flex items-center space-x-2"
      aria-label="Updating data in background"
    >
      <Spinner :show-message="false" :size="'4'" class="w-4 h-4 text-gray-400" />
      <span class="text-xs text-gray-400 font-normal">Syncing...</span>
    </div>

    <!-- Render data immediately if available -->
    <div v-if="renderedEntities.length > 0">
      <!-- Entity list content -->
    </div>

    <!-- Error handling -->
    <div v-else-if="error">
      <p class="text-red-500">Error loading entities: {{ error.message }}</p>
      <button @click="refetchEntities()" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
        Retry
      </button>
    </div>

    <!-- Empty state (only show when not initial loading) -->
    <div v-else-if="!isPending">
      <!-- Empty state content -->
    </div>

    <!-- Initial loading state -->
    <div v-else>
      <Spinner />
    </div>
  </div>
</template>
```

#### Detail Components (e.g., EntityView.vue)
```vue
<script setup lang="ts">
import { useEntityQueryWithCachePopulation, useInvalidateEntityQuery } from '~/composables/useEntityQuery'

const route = useRoute()
const entityId = computed(() => Number(route.params.id))

// TanStack Query integration with cache population
const { data: entity, isPending, isFetching, error, refetch: refetchEntity } = useEntityQueryWithCachePopulation(entityId)
const { invalidateEntity } = useInvalidateEntityQuery()

// REQUIRED: Always refetch when entity ID changes
watch(entityId, async (newEntityId) => {
  if (newEntityId) {
    refetchEntity() // Background refresh when viewing entity
  }
}, { immediate: true })

// URL slug update when data changes
watch(() => entity.value, (newEntity) => {
  if (newEntity) {
    const newRoute = router.resolve({
      params: { slug: slugifyText(newEntity.name), ...route.params },
      query: route.query
    });
    window.history.replaceState('', '', newRoute.fullPath);
  }
}, { immediate: true })
</script>

<template>
  <div class="w-full h-full box-border bg-gray-50">
    <!-- REQUIRED: Background sync indicator -->
    <div 
      v-if="isFetching && !isPending" 
      class="fixed top-20 right-6 z-50 transition-opacity duration-200 flex items-center space-x-2"
      aria-label="Updating entity data in background"
    >
      <Spinner :show-message="false" :size="'4'" class="w-4 h-4 text-gray-400" />
      <span class="text-xs text-gray-400 font-normal">Syncing...</span>
    </div>

    <!-- Render entity immediately if available -->
    <div v-if="entity">
      <!-- Entity detail content -->
    </div>

    <!-- Error handling -->
    <div v-else-if="error">
      <p class="text-red-500">Error loading entity: {{ error.message }}</p>
      <button @click="refetchEntity()" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
        Retry
      </button>
    </div>

    <!-- Not found state -->
    <div v-else-if="!isPending">
      <!-- Entity not found content -->
    </div>

    <!-- Initial loading state -->
    <div v-else>
      <Spinner />
    </div>
  </div>
</template>
```

### 3. Mutation Patterns

#### Create Operations (No Optimistic Updates)
```typescript
// composables/useEntityMutations.ts
export function useCreateEntity() {
  const store = useAppStore()
  const router = useRouter()

  return useMutation({
    mutationFn: async (entity: BaseEntity) => {
      return await store.createEntity(entity)
    },
    onSuccess: (createdEntity) => {
      // Navigate to the created entity using server-generated ID
      router.push({ 
        name: 'entity-detail', 
        params: { id: createdEntity.id, slug: slugifyText(createdEntity.name) }
      })
      
      // Invalidate list caches to include new entity
      queryClient.invalidateQueries({ queryKey: ['all_entities'] })
    },
    onError: (error) => {
      console.error('Failed to create entity:', error)
      // Show error notification
    }
  })
}
```

#### Update/Delete Operations (Optimistic Updates)
```typescript
// composables/useEntityMutations.ts
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useDeleteEntity() {
  const store = useAppStore()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (entityId: EntityId) => {
      return await store.deleteEntity(entityId)
    },
    // Optimistic update
    onMutate: async (entityId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['all_entities'] })
      
      // Snapshot previous value
      const previousEntities = queryClient.getQueryData(['all_entities'])
      
      // Optimistically remove from cache
      queryClient.setQueryData(['all_entities'], (old: any) => {
        if (!old?.result) return old
        return {
          ...old,
          result: old.result.filter((entity: any) => entity.id !== entityId)
        }
      })
      
      return { previousEntities }
    },
    onError: (err, entityId, context) => {
      // Rollback on error
      if (context?.previousEntities) {
        queryClient.setQueryData(['all_entities'], context.previousEntities)
      }
    },
    onSettled: () => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: ['all_entities'] })
    }
  })
}

export function useUpdateEntity() {
  const store = useAppStore()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (entity: Entity) => {
      return await store.updateEntity(entity)
    },
    onSuccess: (updatedEntity) => {
      // Update individual entity cache
      queryClient.setQueryData(['entity', updatedEntity.id], updatedEntity)
      
      // Update in list caches
      queryClient.setQueryData(['all_entities'], (old: any) => {
        if (!old?.result) return old
        return {
          ...old,
          result: old.result.map((entity: any) => 
            entity.id === updatedEntity.id ? updatedEntity : entity
          )
        }
      })
    }
  })
}
```

## Required Implementation Checklist

### ✅ For All Query Components:

1. **Background Sync Indicator**
   ```vue
   <div 
     v-if="isFetching && !isPending"
     class="fixed top-20 right-6 z-50 transition-opacity duration-200 flex items-center space-x-2"
   >
     <Spinner :show-message="false" :size="'4'" class="w-4 h-4 text-gray-400" />
     <span class="text-xs text-gray-400 font-normal">Syncing...</span>
   </div>
   ```

2. **Active Refetching on Navigation**
   ```typescript
   // For list components
   onMounted(() => {
     refetchEntities() // Always refetch on mount
   })
   
   // For detail components  
   watch(entityId, async (newEntityId) => {
     if (newEntityId) {
       refetchEntity() // Refetch when entity changes
     }
   }, { immediate: true })
   ```

3. **Proper Loading States**
   ```vue
   <!-- Show data immediately if available -->
   <div v-if="data">...</div>
   
   <!-- Error with retry -->
   <div v-else-if="error">
     <p>Error: {{ error.message }}</p>
     <button @click="refetch()">Retry</button>
   </div>
   
   <!-- Empty state (not initial loading) -->
   <div v-else-if="!isPending">...</div>
   
   <!-- Initial loading only -->
   <div v-else><Spinner /></div>
   ```

4. **Cache Configuration**
   ```typescript
   {
     staleTime: Infinity, // Never automatically stale
     gcTime: 1000 * 60 * 30, // 30 minutes garbage collection
     refetchOnWindowFocus: false,
     retry: 3
   }
   ```

### ✅ For Mutation Components:

1. **Create Operations**
   - **No optimistic updates** - wait for server response
   - Navigate to object page using server-generated ID
   - Invalidate list caches after successful creation

2. **Update/Delete Operations**
   - Implement `onMutate` for immediate UI feedback
   - Handle `onError` with rollback mechanism
   - Use `onSettled` for cache invalidation

3. **Cache Invalidation Strategy**
   - Target specific cache keys when possible
   - Avoid global invalidation unless necessary
   - Coordinate between list and individual caches

## Cache Architecture Guidelines

### Cache Key Patterns
```typescript
// List queries
['all_entities', { page, filters, owner }]

// Individual entities  
['entity', entityId]

// Related data
['entity_stats', entityId]
['entity_relations', entityId]
```

### Cache Population Strategy
```typescript
// Automatically populate individual cache from list data
watch(listQueryData, (newData) => {
  if (newData?.result) {
    newData.result.forEach(entity => {
      queryClient.setQueryData(['entity', entity.id], entity)
    })
  }
}, { immediate: true })
```

## Performance Best Practices

1. **Enable Queries Conditionally**
   ```typescript
   enabled: computed(() => entityId.value !== undefined)
   ```

2. **Use Proper TypeScript Types**
   ```typescript
   const { data, error } = useQuery<EntityResponse, Error>({...})
   ```

3. **Batch Cache Updates**
   ```typescript
   queryClient.setQueryData(['entities'], (old) => ({
     ...old,
     result: old.result.map(entity => 
       entity.id === updatedId ? updatedEntity : entity
     )
   }))
   ```

4. **Memory Management**
   - Set appropriate `gcTime` for cache cleanup
   - Use `removeQueries` for cleanup when needed

## Route Parameter Best Practices

### Route Validation Pattern (REQUIRED for all route-based queries)

#### ✅ Correct Pattern
```typescript
// Use route validation utility to prevent race conditions
import { useValidatedRouteId, ROUTE_PATTERNS } from '~/utils/routeValidation'

const entityId = useValidatedRouteId(computed(() => route), ROUTE_PATTERNS.CARDS)
const { data, isPending, error } = useEntityQuery(entityId)

// No manual watchers needed - TanStack Query handles reactivity automatically
```

#### ❌ Problematic Patterns
```typescript
// DON'T: Direct route param usage without validation
const entityId = computed(() => Number(route.params.id)) // Race condition risk

// DON'T: Manual refetch watchers (redundant with TanStack Query)
watch(entityId, () => refetchEntity()) // TanStack Query already handles this

// DON'T: Manual refetch on mount (redundant)
onMounted(() => refetchEntity()) // TanStack Query fetches automatically
```

### Route Validation Utility Functions

```typescript
// Available utilities in ~/utils/routeValidation.ts
useValidatedRouteId(route, routePattern) // Safe reactive ID extraction
isValidRouteForComponent(route, pattern) // Route pattern validation
shouldManuallyRefetch() // Usually returns false (TanStack Query handles it)

// Route patterns
ROUTE_PATTERNS.CARDS        // 'cards'
ROUTE_PATTERNS.DECKS        // 'notebooks' 
ROUTE_PATTERNS.REVIEWS      // 'reviews'
ROUTE_PATTERNS.CONTEXTUAL_MEANING // 'contextual-meaning'
ROUTE_PATTERNS.DICTIONARY   // 'dictionary'
```

### Why Route Validation is Critical

**Problem**: During navigation transitions (e.g., CardView → DeckView), components briefly see wrong route parameters:
- CardView might see deck ID (123) instead of card ID
- This triggers invalid queries like `GET /api/cards/123` where 123 is a deck ID
- Results in 3+ redundant requests and race conditions

**Solution**: Route validation ensures queries only run with correct parameters for the current route context.

### When Manual Refetches Are Acceptable

```typescript
// ✅ Strategic manual refetches (rare cases)
- Modal/dialog opening (fresh data when user opens)
- Error retry buttons (user-triggered)
- After mutations (cache invalidation)
- Background sync after specific user actions

// ❌ Never manually refetch for
- Route parameter changes (TanStack Query handles this)
- Component mounting (TanStack Query handles this)  
- Page changes (TanStack Query handles this)
- Navigation between views (TanStack Query handles this)
```

## Anti-Patterns to Avoid

❌ **Never block UI with loading spinners when cache exists**
❌ **Don't use automatic stale time - use manual invalidation**  
❌ **Avoid global cache invalidation - target specific keys**
❌ **Don't forget error boundaries and retry mechanisms**
❌ **Never skip background refetch on navigation**
❌ **Don't use optimistic updates for create operations** - navigation requires server ID
❌ **NEVER use manual refetch watchers with route parameters** - causes redundant requests
❌ **NEVER extract route params without validation** - causes race conditions during navigation

## Examples in Codebase

### Implemented Examples:
- ✅ `AllCardView.vue` - Perfect list component implementation with route validation
- ✅ `CardView.vue` - Perfect detail component with route validation (prevents deck ID race conditions)
- ✅ `AllDeckView.vue` - Deck list with metadata caching and optimized refetch patterns
- ✅ `DeckView.vue` - Deck detail with card cache population and route validation
- ✅ `HistoryDialog.vue` - Modal component with strategic refetch patterns

### Query Composables:
- ✅ `useCardsQuery.ts` - List queries with cache population
- ✅ `useCardQuery.ts` - Individual queries with caching
- ✅ `useDecksQuery.ts` - Deck list queries
- ✅ `useDeckQuery.ts` - Deck detail queries
- ✅ `useWriteHistoryQuery.ts` - History queries with pagination

### Mutation Composables:
- ✅ `useCardMutations.ts` - Optimistic card operations
- ✅ `useDeckMutations.ts` - Optimistic deck operations
- ✅ `useWriteHistoryMutations.ts` - History mutations with optimistic updates

### Utility Functions:
- ✅ `~/utils/routeValidation.ts` - Route parameter validation and safety utilities

Follow these patterns consistently across all components to ensure exceptional user experience with instant loading, background sync, and seamless data management.