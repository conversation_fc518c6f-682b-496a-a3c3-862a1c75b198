# TanStack Vue Query Integration Plan

## Introduction
This plan implements TanStack Vue Query to enhance the AllCardView component with intelligent caching and improved user experience. The current implementation fetches data on every page navigation, causing unnecessary loading delays and poor user experience.

## Product Behavior Goals
We want to achieve the following user experience:

### Current Behavior (Problem)
- Go to page 1: Shows loading spinner, fetches data, displays cards
- Go to page 2: Shows loading spinner, fetches data, displays cards  
- Go back to page 1: Shows loading spinner again, refetches data, displays cards

### Desired Behavior (Solution)
- Go to page 1: Shows loading spinner, fetches and caches data, displays cards
- Go to page 2: Shows loading spinner, fetches and caches data, displays cards
- Go back to page 1: **Instantly shows cached cards** + small loading indicator in top-right corner while fetching fresh data in background

### Key User Experience Improvements
1. **Instant Page Display**: Previously visited pages show cached content immediately
2. **Background Updates**: Fresh data loads silently without blocking the UI
3. **Visual Feedback**: Small, non-intrusive loading indicator during background fetch
4. **Manual Control**: Cache invalidation triggered by user actions (navigation, deletions)

## Overview
Add TanStack Vue Query (v5) to the AllCardView component to provide page-based caching with manual invalidation control for better user experience.

## Goal
Implement intelligent caching for card listings that:
- Shows cached data immediately when navigating between pages
- Fetches fresh data only when explicitly invalidated
- Displays a subtle loading indicator during background fetches
- Provides manual control over cache invalidation

## Current State Analysis
- AllCardView.vue currently uses manual API calls via `store.listGenericCards()`
- No caching mechanism - each page navigation triggers a full reload
- Loading spinner blocks the entire view during data fetching
- Pagination works but doesn't preserve previously loaded data

## Implementation Steps

### 1. Install Dependency
```bash
cd pickvocab-client
pnpm add @tanstack/vue-query
```

### 2. Configure Query Client Plugin
**File**: `pickvocab-client/plugins/vue-query.client.ts`

```typescript
import { VueQueryPlugin, QueryClient } from '@tanstack/vue-query'

export default defineNuxtPlugin((nuxtApp) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: Infinity, // Never automatically stale
        gcTime: 1000 * 60 * 30, // 30 minutes - data kept in cache (renamed from cacheTime in v5)
        refetchOnWindowFocus: false,
        retry: 3,
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
    },
  })

  nuxtApp.vueApp.use(VueQueryPlugin, { queryClient })
})
```

### 3. Create Cards Query Composable
**File**: `pickvocab-client/composables/useCardsQuery.ts`

```typescript
import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'

export function useCardsQuery(page: Ref<number>, isDemoPage: boolean = false) {
  const store = useAppStore()
  const authStore = useAuthStore()

  const queryKey = computed(() => [
    'all_cards', 
    { 
      page: page.value, 
      isDemoPage,
      owner: isDemoPage ? undefined : authStore.currentUser?.id 
    }
  ])

  return useQuery({
    queryKey,
    queryFn: async () => {
      const params = isDemoPage 
        ? { page: page.value, decks__is_demo: true }
        : { page: page.value, owner: authStore.currentUser?.id }
      
      return await store.listGenericCards(params)
    },
    staleTime: Infinity, // Never automatically stale
    enabled: computed(() => !isDemoPage || authStore.currentUser?.id !== undefined)
  })
}

export function useInvalidateCardsQuery() {
  const queryClient = useQueryClient()
  
  return {
    invalidateAllCards: () => queryClient.invalidateQueries({ queryKey: ['all_cards'] }),
    invalidateCardsPage: (page: number, isDemoPage: boolean) => {
      const key = ['all_cards', { page, isDemoPage }]
      return queryClient.invalidateQueries({ queryKey: key })
    },
    refetchAllCards: () => queryClient.refetchQueries({ queryKey: ['all_cards'] }),
    refetchCardsPage: (page: number, isDemoPage: boolean) => {
      const key = ['all_cards', { page, isDemoPage }]
      return queryClient.refetchQueries({ queryKey: key })
    }
  }
}
```

### 4. Refactor AllCardView Component
**File**: `pickvocab-client/components/app/cards/AllCardView.vue`

#### Key Changes:
1. Replace manual `fetchCards()` function with `useCardsQuery()`
2. Remove manual `isLoading` state management
3. Use query's `data`, `isLoading`, `isFetching`, `error` states
4. Add background loading indicator
5. Update delete handler to invalidate cache
6. Add navigation-based cache invalidation

#### New Script Section:
```typescript
<script setup lang="ts">
// ... existing imports
import { useCardsQuery, useInvalidateCardsQuery } from '~/composables/useCardsQuery'

const props = withDefaults(defineProps<{
  isDemoPage?: boolean
}>(), { isDemoPage: false })

// ... existing setup code

// Replace fetchCards with query (using v5 API)
const { data: queryData, isPending, isFetching, error } = useCardsQuery(currentPage, props.isDemoPage)
const { invalidateAllCards, invalidateCardsPage } = useInvalidateCardsQuery()

// Computed properties for template
const renderedCards = computed(() => queryData.value?.result || [])
const totalPages = computed(() => queryData.value?.totalPages || 0)

// V5 loading states: isPending = initial loading, isLoading = isPending && isFetching
const isLoading = computed(() => isPending.value && isFetching.value)

// Watch for page changes and invalidate current page cache
watch(currentPage, (newPage, oldPage) => {
  if (oldPage !== undefined && newPage !== oldPage) {
    // Invalidate the new page to ensure fresh data
    invalidateCardsPage(newPage, props.isDemoPage)
  }
})

// Updated delete handler
async function handleRemoveCard(cardId: CardId) {
  if (window.confirm('Are you sure you want to remove this card?')) {
    try {
      await store.deleteGenericCard(cardId)
      await invalidateAllCards() // Invalidate all card caches
      longPressedCardId.value = null
    } catch (e) {
      console.error('Failed to remove card:', e)
      alert('Failed to remove card. See console for details.')
    }
  }
}

// Remove old fetchCards function and manual loading state
</script>
```

### 5. Add Background Loading Indicator
Add to the template section:

```vue
<template>
  <div class="w-full h-full box-border bg-gray-50">
    <!-- Background fetch indicator (v5: show when fetching but not initial loading) -->
    <div 
      v-if="isFetching && !isPending" 
      class="fixed top-20 right-6 z-50 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md transition-opacity duration-200"
      aria-label="Updating data in background"
    >
      <Spinner class="w-4 h-4 text-blue-500" />
    </div>

    <!-- Rest of existing template -->
    <div v-if="renderedCards.length > 0">
      <!-- ... existing content -->
    </div>
    
    <!-- Update error handling -->
    <div v-else-if="error" class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <p class="text-red-500">Error loading cards: {{ error.message }}</p>
      <button @click="invalidateCardsPage(currentPage, props.isDemoPage)" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
        Retry
      </button>
    </div>

    <!-- Update loading state check (v5: use isPending for initial loading) -->
    <div v-else-if="!isPending" class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <!-- ... existing empty state -->
    </div>
    
    <div v-else class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <Spinner />
    </div>
  </div>
</template>
```

### 6. Cache Configuration Details

#### Query Keys Structure:
```typescript
['all_cards', { page: number, isDemoPage: boolean, owner?: number }]
```

#### Cache Behavior:
- **Stale Time**: Infinity (never automatically stale)
- **GC Time**: 30 minutes (data kept in memory - renamed from cacheTime in v5)
- **Manual Invalidation**: Triggered by navigation and mutations
- **Retry Logic**: 3 attempts with exponential backoff

### 7. Cache Invalidation Strategy

#### Trigger Points:
1. **Page Navigation**: Invalidate target page before navigation
2. **Card Deletion**: Invalidate all card caches
3. **Card Creation**: Invalidate all card caches (if implemented)
4. **Manual Refresh**: User-triggered invalidation

#### Benefits of Manual Control:
- **Predictable Behavior**: Cache invalidation only when needed
- **Performance**: No unnecessary background refetches
- **Data Consistency**: Fresh data guaranteed when explicitly requested
- **Network Efficiency**: Reduced API calls

### 8. Testing Plan

#### Test Scenarios:
1. **Initial Load**: Verify page 1 loads and caches correctly
2. **Navigation Forward**: Go to page 2, verify fresh data loads and caches
3. **Navigation Backward**: Return to page 1, verify cached data shows instantly
4. **Cache Invalidation**: Navigate to any page, verify fresh data is fetched
5. **Delete Action**: Confirm all caches invalidated after card deletion
6. **Error Handling**: Test network errors and retry functionality

#### Expected Behavior:
- Page 1 → Page 2: Fresh data fetch (invalidated on navigation)
- Page 2 → Page 1: Cached data display + fresh fetch (invalidated on navigation)
- Background loading indicator shows during fresh data fetch (isFetching && !isPending)
- Cache persists until explicitly invalidated

## TanStack Query v5 Changes Applied

### Key API Updates:
1. **Loading States**: `isLoading` → `isPending` for initial loading
2. **Cache Configuration**: `cacheTime` → `gcTime` 
3. **Background Loading**: Use `isFetching && !isPending` for background fetch indicator
4. **Error Type**: Now defaults to `Error` instead of `unknown`

### Loading State Logic:
- `isPending`: Initial loading (first time fetching)
- `isFetching`: Any fetch operation (initial or background)
- `isLoading`: Computed as `isPending && isFetching` (but we use isPending directly)
- Background indicator: Show when `isFetching && !isPending`

## Benefits

### User Experience:
- **Instant Display**: Cached data shows immediately while fresh data loads
- **Fresh Data**: Guaranteed current information on navigation
- **Visual Feedback**: Subtle loading indicator for background operations
- **Predictable Behavior**: Consistent data freshness

### Performance:
- **Intelligent Caching**: Data cached until explicitly invalidated
- **Memory Efficient**: Automatic cache cleanup after 30 minutes
- **Network Efficiency**: No unnecessary automatic refetches
- **Background Updates**: Non-blocking data refresh

### Developer Experience:
- **Declarative**: Query state management handled automatically
- **Type Safe**: Full TypeScript support
- **Manual Control**: Explicit cache invalidation strategy
- **Maintainable**: Cleaner separation of concerns

## Migration Strategy
1. Install dependency and configure plugin
2. Create composable alongside existing code
3. Gradually migrate AllCardView to use query
4. Test thoroughly before removing old implementation
5. Apply same pattern to other paginated components

## Future Enhancements
- Extend pattern to other list components (decks, reviews)
- Add infinite scroll support
- Implement optimistic updates for mutations
- Add offline support with cache persistence