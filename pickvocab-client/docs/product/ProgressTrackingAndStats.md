# Pickvocab: Progress Tracking & Statistics - Product Design

## 1. Introduction & Goals

This document outlines the product design for implementing progress tracking and statistics features within Pickvocab. The primary goals are to:

*   Boost user engagement and retention.
*   Provide users with clear insights into their learning progress.
*   Make the learning process more motivating and fun.
*   Integrate vocabulary learning stats with the Writing Assistant to reinforce learning.

## 2. Key Backend Insights

Our design is informed by the existing backend structure:

*   **`Card.progress_score`**: An integer (0-5) on each vocabulary card, indicating mastery level (0 = needs review, 5 = mastered). This is the core SRS-like metric.
*   **Review Selection**: The `pick_cards_to_review` service prioritizes cards with lower `progress_score`, ensuring users focus on challenging words.
*   **Score Updates**: At the end of a review, `delta_score` (positive for correct, negative for incorrect) from the frontend updates the `Card.progress_score`.
*   **Generic Card System**: The platform supports different card types (`DefinitionCard`, `ContextCard`) unified under `GenericCard`, allowing for consistent progress tracking.

## 3. Competitor Insights & Inspiration

Analysis of popular language learning apps (Duolingo, Memrise, Quizlet, Anki, Busuu) reveals common strategies and inspiring features:

*   **Granular Mastery Levels:** Apps define clear stages of word knowledge (e.g., "Learning," "Familiar," "Mastered") often tied to point systems or proficiency frameworks.
    *   *Pickvocab Inspiration:* Use our `progress_score` to create clear, user-facing labels for mastery stages.
*   **Activity Tracking & Streaks:** Daily streaks (Duolingo) and detailed activity logs (Anki, Duolingo's "Year in Review") are highly motivating.
    *   *Pickvocab Inspiration:* Augment our planned Activity Heatmap with daily streak tracking in a future phase. A "Year in Review" summary could be a powerful engagement tool.
*   **Points & Gamification:** XP, badges, leaderboards, and virtual currency are common for engagement.
    *   *Pickvocab Inspiration:* Consider a simple point system for reviews and writing assistant usage for the "My Progress" dashboard, paving the way for future gamification like badges or levels.
*   **Personalized Feedback & Adaptive Learning:** Apps tailor content and highlight areas needing review based on performance.
    *   *Pickvocab Inspiration:* Our plan to show "Words to Focus On" and integrate `progress_score` with the Writing Assistant aligns well.
*   **Clear Dashboards & Visual Reports:** Visual summaries of progress are standard.
    *   *Pickvocab Inspiration:* Our proposed dashboard is a good foundation.
*   **Specific Feature Ideas from Competitors:**
    *   **"Words Not Yet Studied" Indicator (Quizlet):** Show a count of unreviewed words in a deck.
    *   **"Time Spent Learning" Metric (Duolingo, Anki):** Track and display total time in review sessions.
    *   **Explicit Mastery Definitions:** Clearly define what `progress_score` threshold means a word is "learned" (e.g., score >= 4).

## 4. Proposed Features (Incorporating Inspirations)

### 4.1. Post-Review Session Summary

**Objective:** Provide immediate, motivating feedback.

*   **Display Location:** New screen or comprehensive modal post-session.
*   **Information to Display:**
    1.  **Overall Performance:**
        *   "You reviewed **X** words in **Y minutes**!"
        *   "You correctly recalled **Z** out of X words! (**P%**)"
        *   Visual: Progress bar/pie chart.
    2.  **Words Strengthened:**
        *   "Well Done! These words are now stronger:"
        *   List words whose `progress_score` increased significantly (e.g., "Ephemeral: Learning → Familiar").
    3.  **Words Needing More Practice:**
        *   "Keep Practicing! These words need a bit more attention:"
        *   List words marked incorrect or whose `progress_score` decreased.
    4.  **Motivational Message & Streak (Future):** "Great job! You're on a 3-day review streak!"
    5.  **Calls to Action:** "Review More," "Practice in Writing Assistant," "View Progress Dashboard," "Return Home."

### 4.2. "My Progress" Dashboard / Stats Page

**Objective:** Comprehensive overview of learning journey and trends.

*   **Key Metrics & Visualizations:**
    1.  **Vocabulary Snapshot:**
        *   "Total Words Learned: **Y**" (e.g., `progress_score` >= 4).
        *   "Words Actively Learning: **Z**" (e.g., `progress_score` 1-3).
        *   Chart: "Words Learned Over Time."
    2.  **Review Activity:**
        *   "Words Reviewed Today: **A**"
        *   "Total Review Time Today: **T minutes**"
        *   "Current Daily Review Streak: **S days**" (Future Phase)
        *   **Activity Heatmap:** Showing days with review activity (intensity = words reviewed).
    3.  **Word Mastery & Challenges:**
        *   **"My Strongest Words":** List/grid of words with `progress_score` = 5 (Mastered).
        *   **"Words to Focus On":** List/grid of words with `progress_score` 0-1 or frequently incorrect.
            *   Show current mastery label (e.g., "Needs Practice").
            *   Link to review/define.
    4.  **Accuracy Insights:**
        *   "Overall Review Accuracy: **P%**".
        *   Chart: "Accuracy Trend" (daily/weekly).
    5.  **Points/Level (Future Phase):** "Your Pickvocab Score: 1250 points (Level 5 Explorer)"

*   **Design Considerations:** Engaging charts, filtering by deck/time.

### 4.3. Writing Assistant Integration

**Objective:** Reinforce vocabulary through application.

1.  **Enhanced Vocabulary Insights (on word details/dashboard):**
    *   Times reviewed, success rate in reviews.
    *   Times suggested by Writing Assistant, times incorporated.
2.  **Smarter Writing Suggestions:**
    *   Prioritize suggesting words user is struggling with (low `progress_score`).
    *   Encourage use of "learned" words not yet written.
    *   Show word "strength" (mastery label) alongside suggestions.

### 4.4. Gamification & Engagement Boosters (Future Considerations)

*   **Streaks:** Daily/weekly review streaks.
*   **Achievements/Badges:** For milestones.
*   **Personalized Goals:** User-set or suggested learning goals.

## 5. Key Design Definitions & Considerations

*   **Defining "Correct" for a Session:**
    *   Classic Flashcard: User indicated recall (positive `delta_score`).
    *   MCQ: Correct answer selected.
*   **Mastery Labels (User-Facing for `progress_score`):**
    *   0-1: "Needs Practice"
    *   2-3: "Familiar"
    *   4: "Well Known"
    *   5: "Mastered"
*   **Defining "Learned" Word:** `progress_score` >= 4 ("Well Known").
*   **"Words to Focus On" Logic:** Lowest `progress_score` (0-1), or recently decreased score.
*   **Review Activity Heatmap Intensity:** Number of unique cards reviewed.
*   **Data Storage for Trends:**
    *   `ReviewSessionSummary` model: (user, review_id, timestamp, total_cards, correct_cards, incorrect_cards, time_spent).
    *   Daily activity summary table for streaks and heatmap.

## 6. User Flow Diagram (Placeholder)

```mermaid
graph TD
    A[User Finishes Review Session] --> B{Show Session Summary?};
    B -- Yes --> C[Display Post-Review Summary Screen/Modal];
    C --> D[Option: Review More];
    C --> E[Option: Practice in Writing Assistant];
    C --> F[Option: View Progress Dashboard];
    C --> G[Option: Return Home];

    H[User Navigates to App Menu] --> I[Selects "My Progress"];
    I --> J[Display Progress Dashboard];
    J -- Interaction --> K[View Word Lists: Strongest/Focus On];
    J -- Interaction --> L[Filter Data by Deck/Time];
    K --> M[Action: Review Selected 'Focus On' Words];
```

## 7. Next Steps / MVP Discussion

Phased approach recommended:

*   **Phase 1 (Core Feedback):**
    *   Implement Post-Review Session Summary (total cards, correct/incorrect, time spent).
    *   Define and display Mastery Labels based on `progress_score`.
    *   Log `ReviewSessionSummary`.
*   **Phase 2 (Basic Dashboard):**
    *   "My Progress" Dashboard: "Total Words Learned," Activity Heatmap, "Words to Focus On," "My Strongest Words."
*   **Phase 3 (Enhanced Insights & Integration):**
    *   Expand Post-Review Summary (strengthened/struggling words).
    *   Add Accuracy Trends to Dashboard.
    *   Writing Assistant Integration.
*   **Future Phases:** Streaks, Points, Achievements, "Year in Review."