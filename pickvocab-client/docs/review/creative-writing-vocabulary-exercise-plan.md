# Creative Writing Vocabulary Exercise Implementation Plan

## Overview
Add a new AI-powered creative writing exercise where users write sentences/paragraphs using target vocabulary words in context. The system evaluates writing quality and vocabulary usage, providing feedback and guidance.

## High-Level Flow

### User Journey
```
1. Review Setup
   ├─ User selects "Creative Writing" checkbox in ReviewModal
   ├─ System fetches review cards from server
   └─ AI generates contextual writing prompts for each word

2. Writing Exercise
   ├─ Display context/scenario (WITHOUT showing target word)
   ├─ Show writing prompt with clear instructions
   ├─ User writes text in response
   └─ Submit button when ready

3. Evaluation & Feedback
   ├─ AI evaluates writing on multiple criteria
   ├─ System checks if target word was used
   └─ Branching logic based on results:
       
   If word NOT used:
       ├─ Show gentle reminder
       ├─ Highlight target word
       ├─ Keep user's text in editor
       └─ Loop back to writing

   If word used but writing needs work:
       ├─ Show specific feedback
       ├─ Offer three options:
       │   ├─ "Try Again" → Keep text, edit
       │   ├─ "Show Examples" → Display usage examples
       │   └─ "Next Question" → Progress
       └─ Track partial success

   If word used correctly:
       ├─ Show success message
       ├─ Display improvement tips
       ├─ Show "Next" button
       └─ Track full success

4. Session Completion
   ├─ Show summary statistics
   ├─ Highlight challenging words
   └─ Update spaced repetition
```

### Technical Flow
```
ReviewModal.vue
    ↓ (checkbox selection)
useAiReviewGenerator.ts
    ↓ (extend with creative writing support)
CreativeWritingPrompt.ts → LLM API
    ↓ (parse response, fallback to basic review on failure)
CreativeWritingItem[] → useReviewSession.ts
    ↓ (navigation)
pages/app/reviews/[id].vue
    ↓ (render component with @answer callback)
CreativeWritingView.vue
    ↓ (user writes, emits score events)
WritingEvaluator.ts → LLM API
    ↓ (multi-criteria evaluation)
CreativeWritingFeedback.vue → Loop or Progress
```

## Product Features

### Exercise Flow
1. **Context Generation**: AI creates realistic scenarios/contexts for each vocabulary word (without revealing the word)
2. **Writing Challenge**: User recalls and writes text fitting the context, incorporating the target word from memory
3. **Multi-tier Evaluation**: 
   - Grammar and spelling
   - Fluency and coherence
   - Context appropriateness
   - Target word usage and correctness
   - Writing style and creativity
4. **Interactive Feedback**:
   - If word not used: Gentle reminder to incorporate it
   - If writing needs improvement: Specific feedback with rewrite option
   - If successful: Positive reinforcement with improvement suggestions
   - Examples only shown on demand via "Show examples" button

### Evaluation Criteria
- **Grammar & Mechanics**: Spelling, punctuation, basic grammar
- **Fluency & Coherence**: Natural flow, logical connections
- **Context Appropriateness**: Fits the given scenario
- **Target Word Usage**: Present and correctly used
- **Semantic Accuracy**: Word used with correct meaning
- **Grammatical Integration**: Proper conjugation/inflection
- **Collocations**: Natural word combinations
- **Register Appropriateness**: Formal/informal match
- **Sentence Complexity**: Variety in structure
- **Creativity & Style**: Engaging and original expression

## Technical Architecture

### New Components

#### 1. CreativeWritingView.vue
- Main exercise component
- Text area for user writing
- Context display (without revealing target word)
- Submit/retry controls
- Integration with `CreativeWritingFeedback.vue`
- "Show Examples" button (only when needed)
- Progress to next question
- **Event Handling**: Emits `@answer="updateScore"` with delta score
- **Pattern**: Follows existing review component callback pattern

#### 2. WritingEvaluator.ts
- AI-powered writing assessment
- Multi-criteria evaluation logic
- Feedback generation
- Word usage detection with inflection handling
- Score calculation for spaced repetition

#### 3. CreativeWritingPrompt.ts
- LLM prompt for context generation
- XML-structured format following existing patterns
- Examples for various word types
- Error-tolerant output format

#### 4. CreativeWritingTypes.ts
- `CreativeWritingItem` interface
- `WritingEvaluation` result type
- `EvaluationCriteria` enum
- Integration with `LocalSessionReviewItem`

#### 5. CreativeWritingFeedback.vue
- Dedicated feedback component for creative writing evaluation
- Multi-criteria score display
- Retry/continue options
- Example usage display (on demand)
- Different from existing `LLMFeedbackDisplay.vue` due to specialized needs

### Data Structures

```typescript
interface CreativeWritingItem {
  id: string;
  llmSourceCardId?: string;
  word: string;        // Target word (not shown to user initially)
  context: string;     // Scenario description
  prompt: string;      // Writing instruction
  hints?: string[];    // Optional usage hints
  originalCardId?: string;
}

interface WritingEvaluation {
  overallScore: number; // 0-100
  wordUsed: boolean;
  wordUsedCorrectly: boolean;
  criteria: {
    grammar: EvaluationResult;
    fluency: EvaluationResult;
    context: EvaluationResult;
    wordUsage: EvaluationResult;
    creativity: EvaluationResult;
  };
  feedback: string;
  suggestions: string[];
  examples?: string[]; // Generated but only shown on demand
  canProgress: boolean;
}

interface EvaluationResult {
  score: number; // 0-100
  feedback: string;
}
```

### Integration Points
- **Review Session**: Integrates with existing `useReviewSession.ts` composable
- **AI Generation**: Extends existing `useAiReviewGenerator.ts` with creative writing support
- **LLM Infrastructure**: Reuses LLM infrastructure from writing assistant
- **UI Patterns**: Follows established patterns from `TypeInQuestionView.vue`
- **Event System**: Uses existing `@answer="updateScore"` callback pattern
- **Error Handling**: Falls back to basic review when AI generation fails
- **Type System**: Extends `LocalSessionReviewItem` union type
- **Spaced Repetition**: Compatible with existing delta score system

### Spaced Repetition Scoring
```typescript
function calculateDeltaScore(overallScore: number): number {
  if (overallScore > 80) return 1;   // Good performance, increase interval
  if (overallScore >= 50) return 0;  // Adequate performance, maintain interval
  return -1;                         // Needs improvement, decrease interval
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (2-3 days)
- [ ] Update `reviewTypes.ts` to extend `LocalSessionReviewItem` union with `'creative-writing'` type
- [ ] Create `CreativeWritingTypes.ts` with interfaces
- [ ] Implement `CreativeWritingPrompt.ts` with XML format
- [ ] Extend `useAiReviewGenerator.ts` with creative writing support (remove separate generator)
- [ ] Add "Creative Writing" checkbox to ReviewModal
- [ ] Create basic `CreativeWritingView.vue` structure with `@answer` callback
- [ ] Update review page component switching to handle creative writing case

### Phase 2: Writing Evaluation (3-4 days)
- [ ] Implement `WritingEvaluator.ts` with multi-criteria logic
- [ ] Create evaluation prompt with clear criteria
- [ ] Add word usage detection (handle inflections)
- [ ] Implement feedback generation system
- [ ] Add retry logic for word not used
- [ ] Create scoring algorithm for spaced repetition:
      - Overall score > 80: delta = +1 (good progress)
      - Overall score 50-80: delta = 0 (maintain current interval)
      - Overall score < 50: delta = -1 (needs more practice)

### Phase 3: UI/UX Polish (2-3 days)
- [ ] Design writing interface with elegant text area
- [ ] Implement character/word counter
- [ ] Create `CreativeWritingFeedback.vue` component with criteria breakdown
- [ ] Add "Show Examples" button (conditional display)
- [ ] Add smooth transitions between states
- [ ] Implement rewrite flow with options
- [ ] Add loading states and error handling
- [ ] Update `ReviewSummaryView.vue` to handle creative writing statistics
- [ ] Add creative writing specific metrics to summary (attempts, criteria scores, etc.)

### Phase 4: Testing & Refinement (2 days)
- [ ] Test with various vocabulary types
- [ ] Refine context generation prompts
- [ ] Adjust evaluation strictness
- [ ] Optimize LLM usage patterns
- [ ] Add comprehensive error handling
- [ ] Performance testing with longer texts

## Key Considerations

### User Experience
- **Clear Instructions**: Context and prompt without revealing target word
- **Memory Challenge**: User must recall the word being reviewed
- **Encouraging Feedback**: Positive, constructive tone
- **Flexible Evaluation**: Not overly strict on style
- **Progress Tracking**: Visual indicators of completion
- **Smooth Flow**: Minimal friction between questions

### Technical Challenges
- **Word Detection**: Handle inflections, conjugations, plurals
- **Context Quality**: Ensure varied, interesting scenarios
- **Evaluation Balance**: Not too strict or lenient
- **Performance**: Efficient with longer texts
- **Error Recovery**: Graceful handling of LLM failures

### Cost Management
- **Batch Evaluation**: Combine multiple criteria in one call
- **Smart Caching**: Cache evaluation results by text hash
- **Lazy Evaluation**: Only evaluate when submitted
- **Single LLM Call**: Generate context and evaluate in one request where possible

### Edge Cases
- User writes in wrong language
- Extremely short responses
- Copy-pasted content
- Multiple uses of target word
- Homonyms and word variations
- Network failures during evaluation

## Success Metrics
- User engagement rate with creative writing mode
- Completion rate vs other exercise types
- Average attempts before success
- Improvement in vocabulary retention
- User feedback and satisfaction scores

## Future Enhancements
- Voice-to-text input option
- Peer review features
- Writing templates/scaffolds
- Difficulty levels (beginner/advanced)
- Genre-specific contexts
- Collaborative writing exercises
- Integration with writing goals

## Markdown Parsers

### Parser Requirements
Both markdown parsers must be **error-tolerant and relaxed** to handle LLM output variations:

1. **Case-insensitive matching** for headings and sections
2. **Flexible whitespace handling** (trim extra spaces, handle various line breaks)
3. **Partial content extraction** (don't fail if some sections missing)
4. **Fallback values** for missing fields
5. **Robust regex patterns** that handle variations in formatting
6. **Continue parsing** even if one item fails

### Context Generation Parser (`markdownCreativeWritingParser.ts`)
```typescript
// Following patterns from existing parsers:
// 1. Split by sections: markdown.split(/(?=^## )/m)
// 2. Extract word/ID with flexible regex: /^##\s*(.*?)\s*\(.*?(\d+).*?\)\s*$/m
// 3. Case-insensitive section matching: /###\s*Context\s*\n/i
// 4. Extract all content until next section: /(.*?)(?=###|$)/s
// 5. Use try-catch for each item, continue on error
// 6. Log warnings but don't fail: console.warn()
// 7. Return partial results even if some fail

Example patterns to handle:
- "## word (id: 123)", "## Word (ID: 123)", "## word(123)"
- "### context", "### Context", "### CONTEXT"
- Missing sections get empty strings as defaults
- Continue parsing even with malformed sections
```

### Evaluation Parser (`markdownWritingEvaluationParser.ts`)
```typescript
// Error-tolerant parsing approach:
// 1. Use marked.lexer() for structured parsing
// 2. Traverse tokens looking for patterns
// 3. Normalize text: .toLowerCase().trim()
// 4. Extract values with flexible regex
// 5. Provide defaults for missing values
// 6. Never throw, always return best-effort result

Example flexible patterns:
- Word usage: /used:?\s*(yes|no)/i
- Scores: /(\d+)\s*(?:\/\s*100)?/
- Bullet points: token.type === 'list_item'
- Headings: check token.depth and normalize text
- Missing sections: use sensible defaults
```

## LLM Prompt Template

### Context Generation Prompt

```typescript
export function constructCreativeWritingPrompt(cards: LLMCardInput[]): string {
  const cardsJson = JSON.stringify(cards);
  
  return `<instructions>
You are an expert language teacher creating creative writing exercises. For each vocabulary word provided, generate a context/scenario that naturally calls for the use of that word WITHOUT revealing the word itself.

The context should:
1. Create a situation where the target word would naturally fit
2. Be interesting and engaging
3. NOT mention or hint at the exact word
4. Be clear enough that someone who knows the word would think to use it
5. Include a specific writing task or prompt
</instructions>

<output_format>
For each word, create a markdown section with this EXACT format:

## [word] (id: [card_id])

### Context
[A 2-3 sentence scenario that sets up a situation where this word would naturally be used. Do not mention the word itself.]

### Prompt
[A specific writing instruction that guides the user to write something that would naturally include this word. For example: "Write about...", "Describe...", "Explain..."]

### Hint
[A subtle clue about the type of word needed, such as "Think of a word that means..." or "Use an adjective that..." - but never give the exact word]
</output_format>

<example_output>
## meticulous (id: 123)

### Context
Sarah is preparing for the most important presentation of her career. Every slide must be perfect, every number double-checked, and every detail considered. She's known for her extremely careful and precise approach to work.

### Prompt
Write a paragraph describing Sarah's preparation process for this presentation, emphasizing her attention to detail and careful approach.

### Hint
Think of an adjective that means extremely careful and precise about details.

## surreptitious (id: 456)

### Context
During the boring company meeting, Jake needed to check an urgent personal message on his phone. The boss has a strict no-phone policy and watches everyone like a hawk.

### Prompt
Describe how Jake attempts to check his phone without anyone noticing, especially his watchful boss.

### Hint
Use an adjective that describes doing something secretly or stealthily.
</example_output>

<input>
${cardsJson}
</input>`;
}
```

### Writing Evaluation Prompt

```typescript
export function constructWritingEvaluationPrompt(
  targetWord: string,
  userText: string,
  context: string
): string {
  return `<instructions>
You are evaluating a user's creative writing exercise. The user was given a context and asked to write text that naturally incorporates a specific vocabulary word. Evaluate their writing on multiple criteria.

Target word: "${targetWord}"
Context provided: "${context}"
</instructions>

<evaluation_criteria>
1. Word Usage: Did they use the target word? Is it used correctly with the right meaning?
2. Grammar: Are there grammar or spelling errors?
3. Fluency: Does the writing flow naturally?
4. Context Fit: Does the writing match the given context/scenario?
5. Word Integration: Is the target word integrated naturally, not forced?
</evaluation_criteria>

<output_format>
Provide your evaluation in markdown format with these sections:

# Evaluation Results

## Word Usage
- **Used:** [Yes/No]
- **Used Correctly:** [Yes/No]

## Scores
- **Grammar:** [0-100]/100
- **Fluency:** [0-100]/100
- **Context Fit:** [0-100]/100
- **Word Integration:** [0-100]/100

## Feedback
[Overall feedback message about the writing quality and word usage]

## Issues
- [Specific issue 1]
- [Specific issue 2]
(Only include if there are actual issues)

## Suggestions
- [Suggestion for improvement 1]
- [Suggestion for improvement 2]

## Examples
[If the word was used incorrectly or not used, provide 1-2 examples of how to use it correctly in this context]
</output_format>

<example_output>
# Evaluation Results

## Word Usage
- **Used:** Yes
- **Used Correctly:** Yes

## Scores
- **Grammar:** 95/100
- **Fluency:** 90/100
- **Context Fit:** 100/100
- **Word Integration:** 85/100

## Feedback
The writing effectively captures Sarah's meticulous approach to her presentation preparation. The target word is used correctly and fits naturally within the context. The description is vivid and engaging.

## Issues
- Minor repetition of "careful" could be varied with synonyms
- One comma splice in the second sentence

## Suggestions
- Consider varying vocabulary to avoid repetition
- Break the longer sentence into two for better flow

## Examples
(Not needed as word was used correctly)
</example_output>

<user_text>
${userText}
</user_text>`;
}
```

## Conclusion
This creative writing exercise provides an engaging, production-based method for vocabulary practice that complements existing review modes. By requiring users to actively use words in context, it promotes deeper learning and better retention than recognition-based exercises.