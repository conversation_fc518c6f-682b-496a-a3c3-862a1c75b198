# Plan: AI-Generated Multiple-Choice Reviews

## Introduction
This document outlines the implementation plan for adding AI-generated multiple-choice questions to PickVocab's review system, enabling users to choose between basic flashcard reviews, AI-generated MCQs, or a mix of both for initial review sessions. Subsequent reviews of the same session will revert to basic flashcards. Backend review creation remains unchanged.

## Original High-Level Implementation Strategy

The original strategy involved several key areas:

1.  **UI Enhancements ([`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)):**
    *   Add checkboxes for selecting "Basic Review" and "AI Multiple-Choice Questions".
    *   Manage selection state and button enablement.

2.  **Core Frontend Logic:**
    *   **Card Fetching & Division ([`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)):** Fetch cards via `store.createReview()` and divide them based on selected review modes (basic, AI, or mixed).
    *   **AI Question Generation (New Composable: `useAiReviewGenerator.ts`):**
        *   Format cards for LLM input.
        *   Construct LLM prompt using a template ([`pickvocab-client/components/app/reviews/ai_generated_review.md`](pickvocab-client/components/app/reviews/ai_generated_review.md:5-48)).
        *   Call LLM API via `useLLMStore`.
        *   Parse LLM response using a new parser.
    *   **Markdown Parsing (New File: `markdownReviewQuestionParser.ts`):**
        *   Parse markdown from LLM into structured `MCQItem` objects.
    *   **Consolidation ([`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)):** Combine basic cards and AI MCQs into a `localSessionReviewItems` list for initial display, then navigate.

3.  **Store Modifications (Pinia `useAppStore`):**
    *   No changes to the `createReview` action's backend interaction. Review mode selection is client-side only for initial display.

4.  **Review Display ([`pages/app/reviews/[id].vue`](pickvocab-client/pages/app/reviews/[id].vue:1) & New [`MultipleChoiceQuestionView.vue`](pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue:1)):**
    *   Dynamically display items from `localSessionReviewItems` on initial load.
    *   On refresh/direct visit, fetch and display all cards as basic flashcards.
    *   Create [`MultipleChoiceQuestionView.vue`](pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue:1) to render and manage MCQ interactions.

5.  **Data Structures (New Client-Side TypeScript Types in `reviewTypes.ts`):**
    *   Define `LLMCardInput`, `MCQItem`, `LocalSessionReviewItem` for managing AI review flow. Existing backend API types ([`pickvocab-client/api/review/types.ts`](pickvocab-client/api/review/types.ts)) remain unchanged.

6.  **Backend Modifications:**
    *   None required.

7.  **Coding Guidelines:**
    *   Use Tailwind CSS, focus on UI/UX, follow SOLID principles, and aim for manageable file sizes.
    *   Make sure the UI looks elegant and beautiful

## Detailed Implementation Tasks

**Phase 1: UI and Core Logic Setup in `ReviewModal.vue` [DONE]**

*   **Task 1: Implement Review Mode Selection UI**
    *   **File:** [`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)
    *   **Action:** Add "Basic Review" and "AI Multiple-Choice Questions" checkboxes.
    *   **Verification:** Checkboxes visible and labeled in Review Modal.

*   **Task 2: Implement Review Mode State and Button Logic**
    *   **File:** [`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)
    *   **Action:** Create reactive variables (`useBasicReview`, `useAiReview`), manage "Review" button disable logic.
    *   **Verification:** Button state reflects selections; variables update.

*   **Task 3: Implement Card Division Logic**
    *   **File:** [`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)
    *   **Action:** Distribute `sessionCardsFromBackend` into `basicReviewCards` and `aiQuestionCards` based on mode selection.
    *   **Verification:** `basicReviewCards` and `aiQuestionCards` populated correctly.

**Phase 2: AI Question Generation [DONE]**

*   **Task 4: Create `reviewTypes.ts` for Client-Side Data Structures**
    *   **File:** (New) [`pickvocab-client/components/app/reviews/reviewTypes.ts`](pickvocab-client/components/app/reviews/reviewTypes.ts:1)
    *   **Action:** Define and export `LLMCardInput`, `MCQItem`, `LocalSessionReviewItem` interfaces. Import `Card` from [`pickvocab-client/utils/card.ts`](pickvocab-client/utils/card.ts:1).
    *   **Verification:** File created; interfaces correctly defined and exported.

*   **Task 5: Create `useAiReviewGenerator.ts` Composable Structure**
    *   **File:** (New) [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1)
    *   **Action:** Create composable. Import types (`Card`, `CardType`, `DefinitionCard`, `ContextCard` from [`pickvocab-client/utils/card.ts`](pickvocab-client/utils/card.ts:1); `LLMCardInput`, `MCQItem` from `reviewTypes.ts`). Define `async function generateMcqs(cards: Card[]): Promise<MCQItem[]>`.
    *   **Verification:** File created; correct function signature and imports.

*   **Task 6: Implement `formatCardsForLLM` Helper in `useAiReviewGenerator.ts`**
    *   **File:** [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1)
    *   **Action:** Implement `formatCardsForLLM(cardsToFormat: Card[]): LLMCardInput[]`, handling `DefinitionCard` and `ContextCard`.
    *   **Verification:** Function transforms `Card[]` to `LLMCardInput[]`.

*   **Task 7: Implement LLM Prompt Preparation in `useAiReviewGenerator.ts`**
    *   **File:** [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1)
    *   **Action:** Call `formatCardsForLLM`. Construct prompt using template from [`pickvocab-client/components/app/reviews/ai_generated_review.md`](pickvocab-client/components/app/reviews/ai_generated_review.md:5-48), injecting `JSON.stringify(formattedCards)`.
    *   **Verification:** Correctly formatted LLM prompt string generated.

*   **Task 8: Implement LLM API Call Logic in `useAiReviewGenerator.ts`**
    *   **File:** [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1)
    *   **Action:** Import `useLLMStore`. Adapt logic from [`pickvocab-client/components/app/write/useRevisionApi.ts`](pickvocab-client/components/app/write/useRevisionApi.ts:113-134) for model config, chat source, and `chatSource.sendMessage(prompt)`. Basic error handling.
    *   **Verification:** LLM API call made (can be mocked); response/error handled.

*   **Task 9: Create `markdownReviewQuestionParser.ts` Structure**
    *   **File:** (New) [`pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:1)
    *   Look at `pickvocab-client/components/app/write/markdownRevisionParser.ts` to see how we write parser for writing-assistant feature
    *   The parser should be error-tolerant. Don't parse using strict rules, always keep in mind that LLM can return defect output. For example, we should:
        * Handling case insensitive headings, answer, options, etc.
        * Redundant whitespaces in headings, answer, etc.
        * Get all the text (including sub-headings, list, etc.) for question and explanation section
    *   **Action:** Create parser file. Import `MCQItem` from `reviewTypes.ts`. Export `function parseMarkdownReviewQuestions(markdown: string): MCQItem[]`.
    *   **Verification:** File created; correct function signature and import.

*   **Task 10: Implement Markdown Parsing Logic in `markdownReviewQuestionParser.ts`**
    *   **File:** [`pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:1)
    *   **Action:** Import `marked`. Use `marked.lexer(markdown)` to extract data for questions based on structure in [`pickvocab-client/components/app/reviews/ai_generated_review.md`](pickvocab-client/components/app/reviews/ai_generated_review.md:31-48). Construct and return `MCQItem[]`.
    *   **Verification:** Sample LLM markdown output parsed into `MCQItem[]`.

*   **Task 11: Integrate Parser and Finalize `generateMcqs` in `useAiReviewGenerator.ts`**
    *   **File:** [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1)
    *   **Action:** Import and call `parseMarkdownReviewQuestions` with LLM response. Ensure `generateMcqs` returns `Promise<MCQItem[]>`.
    *   **Verification:** `generateMcqs` returns promise resolving to `MCQItem[]`.

**Phase 3: Consolidating Review Items and Navigation [DONE]**

*   **Task 12: Consolidate Review Items in `ReviewModal.vue`**
    *   **File:** [`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)
    *   **Action:** If `aiQuestionCards.length > 0`, use `useAiReviewGenerator` to get `parsedMcqList`. Create and populate `localSessionReviewItems: LocalSessionReviewItem[]` from `basicReviewCards` and `parsedMcqList`. Import `LocalSessionReviewItem`.
    *   **Verification:** `localSessionReviewItems` correctly populated.

*   **Task 13: Implement Shuffling and Navigation in `ReviewModal.vue`**
    *   **File:** [`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1)
    *   **Action:** If mixed mode, shuffle `localSessionReviewItems`. After `store.createReview()`, navigate to review page, passing `localSessionReviewItems`.
    *   **Verification:** `localSessionReviewItems` shuffled in mixed mode. Navigation occurs; data accessible on destination.

**Phase 4: Review Display Modifications [Done]**

*   **Task 14: Create `MultipleChoiceQuestionView.vue` Component Structure**
    *   **File:** (New) [`pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue`](pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue:1)
    *   **Action:** Create component. Define `mcqData: MCQItem` prop. Basic layout for question text and options. Import `MCQItem`.
    *   **Verification:** Component renders question/options from `mcqData`.

*   **Task 15: Implement Initial Load Logic in `pages/app/reviews/[id].vue`**
    *   **File:** [`pages/app/reviews/[id].vue`](pickvocab-client/pages/app/reviews/[id].vue:1)
    *   **Action:** Retrieve `localSessionReviewItems`. Conditionally render `ReviewCardFrontView`/`ReviewCardBackView` or `MultipleChoiceQuestionView` based on item `type`. Import types.
    *   **Verification:** Correct components rendered based on `localSessionReviewItems`.

*   **Task 16: Implement Refresh/Direct Visit Logic in `pages/app/reviews/[id].vue`**
    *   **File:** [`pages/app/reviews/[id].vue`](pickvocab-client/pages/app/reviews/[id].vue:1)
    *   **Action:** If `localSessionReviewItems` unavailable, fetch review from backend via `store.fetchReview()`. Render all cards as basic flashcards.
    *   **Verification:** On refresh/direct visit, only basic flashcards displayed from backend data.

*   **Task 17: Implement MCQ Interaction in `MultipleChoiceQuestionView.vue`**
    *   **File:** [`pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue`](pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue:1)
    *   **Action:** Implement full MCQ interaction: selection, feedback, answer reveal, explanation, progression.
    *   **Verification:** User can interact with MCQs, get feedback, and progress.

## Plan Update (2025-06-02): Allow Duplicate Words for AI-Generated MCQs

**Goal:** Modify the MCQ parsing logic in [`pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:1) to create a unique `MCQItem` for every question block received from the LLM, even if multiple question blocks pertain to the same word string. This accommodates scenarios where a single word has multiple meanings or contexts, each deserving its own question.

**Primary File to Modify:**

*   [`pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:1)

**Specific Changes in `markdownReviewQuestionParser.ts`:**

1.  **Remove `processedWords` Set:**
    *   Delete the line: `const processedWords = new Set<string>();` (this was around line 13 in the original parser code).

2.  **Remove Duplicate Word Check Logic:**
    *   Delete the conditional block (this was around lines 34-39 in the original parser code):
        ```typescript
        // const wordKey = headingText.trim().toLowerCase(); 
        // if (processedWords.has(wordKey)) {
        //   console.log(\`Skipping duplicate word: "\${headingText}"\`);
        //   currentMCQ = null; // Skip this MCQ
        //   return;
        // }
        ```
    *   The `wordKey` variable declaration will also be removed.

3.  **Remove Adding Word to `processedWords`:**
    *   Delete the line: `processedWords.add(wordKey);` (this was around line 42 in the original parser code).

**Rationale:**
By removing the `processedWords` tracking, the parser will no longer skip subsequent occurrences of the same word. Each time a level 2 heading (indicating a new word/question block from the LLM) is encountered, a new `Partial<MCQItem>` will be initialized. The existing `generateMCQId()` function in [`markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:298) will ensure each `MCQItem` has a unique `id`, allowing them to be distinguished throughout the review process.

**Analysis of Other Related Files (No Changes Expected):**

*   **[`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1):** No changes. Will handle the potentially longer list of `MCQItem[]` returned by the modified parser.
*   **[`pickvocab-client/components/app/reviews/reviewTypes.ts`](pickvocab-client/components/app/reviews/reviewTypes.ts:1):** No changes. The `MCQItem` type already includes a unique `id` field.
*   **[`pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue`](pickvocab-client/components/app/reviews/MultipleChoiceQuestionView.vue:1):** No changes. This component displays a single `MCQItem` and is unaffected by whether other items have the same `word` property.
*   **[`pickvocab-client/components/app/reviews/ReviewModal.vue`](pickvocab-client/components/app/reviews/ReviewModal.vue:1):** No changes. It processes a list of `MCQItem` objects and will handle the potentially longer list correctly.
*   **[`pickvocab-client/pages/app/reviews/[id].vue`](pickvocab-client/pages/app/reviews/[id].vue:1):** No changes. It iterates and renders distinct items from `localSessionReviewItems` based on their unique properties.

## Plan Update (2025-06-02): Robust MCQ Mapping via Card ID Echo and Deduplication

**Context:**
The previous method of mapping AI-generated Multiple-Choice Questions (MCQs) to their original vocabulary cards relied on matching the `word` property. This approach proved problematic when:
1.  Multiple cards in a review session shared the same word (e.g., "bank" as a river side vs. "bank" as a financial institution). The MCQ would always map to the *first* encountered card with that word.
2.  The LLM or the markdown parser might filter out or fail to generate an MCQ for an intermediate card, leading to misaligned indices if a simple sequential mapping was attempted.

**Goal:**
To ensure MCQs are accurately and unambiguously mapped to their original vocabulary cards, especially when dealing with duplicate words or potential filtering during the generation/parsing process.

**Strategy:**
This will be achieved by implementing a "Card ID Echo" mechanism combined with upfront deduplication of cards:
1.  **Deduplicate Input Cards:** Before processing cards for the LLM, the input list will be deduplicated based on `card.id` to prevent redundant MCQ generation for the exact same card instance.
2.  **Include Card ID in LLM Input:** For each unique, valid card sent to the LLM, its original `card.id` (or a relevant unique numeric part if applicable) will be included in the input data.
3.  **Instruct LLM to Echo Card ID:** The LLM prompt will be modified to instruct it to include this `card.id` in the heading of each MCQ it generates, preferably in the format `## [word] (id: [NUMERIC_ID_FROM_INPUT])`.
4.  **Extract Card ID in Parser:** The markdown parser will be updated to extract this echoed numeric `card_id` from the MCQ heading using a flexible regex.
5.  **Direct Mapping:** The extracted `card_id` (now available as `llmSourceCardId` on the `MCQItem`) will be used to directly set the `originalCardId` on the `MCQItem`, ensuring an accurate link.

**Affected Files:**

*   [`pickvocab-client/components/app/reviews/reviewTypes.ts`](pickvocab-client/components/app/reviews/reviewTypes.ts:1)
*   [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1)
*   [`pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:1)

**Detailed Implementation Tasks:**

**Phase 1: Update Data Structures**
*(File: [`pickvocab-client/components/app/reviews/reviewTypes.ts`](pickvocab-client/components/app/reviews/reviewTypes.ts:1))*

*   **Task 1.1: Modify `LLMCardInput` Type**
    *   **Action:** Add an `id: string` field (this ID should be the one intended for echoing, e.g., a numeric string if that's the target format).
        ```typescript
        export interface LLMCardInput {
          id: string; // Unique ID of the original card (e.g., "123" or "card-123")
          word: string;
          definition: string;
        }
        ```
    *   **Verification:** `LLMCardInput` type correctly includes the `id` field.

*   **Task 1.2: Modify `MCQItem` Type**
    *   **Action:** Add an `llmSourceCardId?: string` field.
        ```typescript
        export interface MCQItem {
          id: string; // Unique ID for the MCQ item itself
          llmSourceCardId?: string; // Card ID as echoed by LLM (e.g., "123")
          word: string;
          questionType: string;
          questionText: string;
          options: { a: string; b: string; c: string; d: string };
          correctAnswer: 'a' | 'b' | 'c' | 'd';
          explanation: string;
          originalCardId?: string; // To be populated from llmSourceCardId
        }
        ```
    *   **Verification:** `MCQItem` type correctly includes `llmSourceCardId`.

**Phase 2: Update AI Review Generator Logic**
*(File: [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1))*

*   **Task 2.1: Update `formatCardsForLLM` (or rename to `prepareUniqueValidInputsForLLM`)**
    *   **Action:**
        1.  Deduplicate input `cardsToFormat` by `card.id`, keeping the first occurrence.
        2.  Map unique cards to `LLMCardInput`, including `card.id` (ensure this `id` field in `LLMCardInput` is formatted as desired for the LLM echo, e.g., just the numeric part if `card.id` is `card-123` and you want `(id: 123)`).
        3.  Filter out items with no word/definition.
    *   **Conceptual Code:**
        ```typescript
        function prepareUniqueValidInputsForLLM(cardsToFormat: Card[]): LLMCardInput[] {
          const uniqueCards: Card[] = [];
          const seenCardIds = new Set<string>();

          for (const card of cardsToFormat) {
            if (card.id && !seenCardIds.has(card.id)) {
              uniqueCards.push(card);
              seenCardIds.add(card.id);
            }
          }

          return uniqueCards.map(card => {
            let word: string; // Extracted from card
            let definition: string; // Extracted from card
            // ... logic to extract word & definition ...
            if (card.cardType === CardType.DefinitionCard) { // Example extraction
                const defCard = card as DefinitionCard;
                word = defCard.word;
                definition = defCard.definition.definition || '';
            } else if (card.cardType === CardType.ContextCard) {
                const contextCard = card as ContextCard;
                word = contextCard.wordInContext.word;
                definition = contextCard.wordInContext.definition?.definition ||
                             contextCard.wordInContext.definitionShort?.explanation || '';
            } else {
                word = '';
                definition = '';
            }
            // Example: If card.id is "card-123" and LLM needs "123"
            // const llmEchoId = card.id.startsWith('card-') ? card.id.substring(5) : card.id;
            const llmEchoId = card.id; // Assuming card.id is already in the desired format for echo

            return {
              id: llmEchoId, // This ID is sent to LLM
              word: word.trim(),
              definition: definition.trim(),
            };
          }).filter(item => item.word && item.definition);
        }
        ```
    *   **Verification:** Function deduplicates cards and produces valid `LLMCardInput[]` with original IDs formatted for LLM echo.

*   **Task 2.2: Update `constructPrompt` Function**
    *   **Action:** Modify `output_format` in `promptTemplate` to: `## [word] (id: [NUMERIC_ID_FROM_INPUT])`. Clarify that `[NUMERIC_ID_FROM_INPUT]` refers to the unique identifier of the card provided in the LLM input.
    *   **Verification:** Prompt instructs LLM correctly.

*   **Task 2.3: Simplify MCQ to Card ID Association (in `generateMcqs`)**
    *   **Action:** Directly assign `mcq.originalCardId = mcq.llmSourceCardId`.
    *   **Conceptual Code:**
        ```typescript
        const mcqItemsWithCardIds = mcqItems.map(mcq => {
          if (!mcq.llmSourceCardId) {
            console.warn(/* ... */);
          }
          // If llmSourceCardId is numeric ("123") but original card IDs have a prefix (e.g., "card-123"),
          // reconstruct the full ID here if necessary before assigning to mcq.originalCardId.
          // For now, assuming llmSourceCardId directly matches the format of Card.id or is the desired final format.
          return { ...mcq, originalCardId: mcq.llmSourceCardId };
        });
        ```
    *   **Verification:** `originalCardId` is correctly assigned.

**Phase 3: Update Markdown Parser Logic**
*(File: [`pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:1))*

*   **Task 3.1: Modify Level 2 Heading Parsing**
    *   **Action:** Parse heading (e.g., "word (id: 123)") to extract `word` and the numeric `card_id`. The regex should be flexible for "id:", "cardid:", "identifier:" (case-insensitive) and spacing, but capture the number.
    *   **Conceptual Code (in `traverse` for heading depth 2):**
        ```typescript
        const headingText = /* ... extracted heading text ... */;
        const match = headingText.match(/^(.*?)\s*\((?:id|cardid|identifier)\s*:\s*(\d+)\)$/i);

        if (match && match[1] && match[2]) {
            const parsedWord = match[1].trim();
            const parsedCardId = match[2].trim(); // Numeric string "123"
            currentMCQ = {
                // ...
                word: parsedWord,
                llmSourceCardId: parsedCardId,
                // ...
            };
        } else {
            console.warn(`AI Review Parser: Could not parse word and numeric card ID from heading: "${headingText}".`);
            // Fallback logic or skip if critical
            if (currentMCQ && isValidMCQ(currentMCQ)) { 
                mcqItems.push(currentMCQ as MCQItem);
            }
            currentMCQ = null; 
        }
        ```
    *   **Verification:** Parser extracts `word` and numeric `llmSourceCardId` and handles variations/errors.

## Plan Update (2025-06-03): Relaxed Card ID Parsing in Markdown Parser

**Goal:** Make the card ID parsing in the markdown parser more flexible to handle variations in LLM output format while still reliably extracting the numeric card ID.

**Primary File to Modify:**
* [`pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts`](pickvocab-client/components/app/reviews/markdownReviewQuestionParser.ts:1)

**Specific Changes:**
1. **Updated Regex Pattern:**
   * New pattern: `/^(.*?)\s*\(.*?(\d+).*?\)$/i`
   * This change makes the parser:
     * More tolerant of variations in the ID format (e.g., "word (123)", "word (id: 123)", "word (card-123)")
     * Less strict about requiring specific ID prefixes (id/cardid/identifier)
     * Still reliably captures the first numeric sequence within parentheses

2. **Updated Warning Message:**
   * Enhanced the warning message to show expected format examples when parsing fails

**Rationale:**
The LLM may output card IDs in slightly different formats than strictly specified. This change:
* Makes the parser more robust against LLM output variations
* Maintains the ability to extract the essential numeric ID while being more forgiving of formatting
* Reduces the chance of valid MCQs being skipped due to minor formatting differences
* Still maintains the critical card-to-MCQ mapping functionality

**Impact Analysis:**
* **Backward Compatibility:** The new parser will handle all previously valid formats plus additional variations
* **Error Handling:** The parser will still warn about unparseable headings but with more helpful guidance
* **Performance:** No significant impact - the simplified regex may actually be slightly more efficient
* **Data Integrity:** The core functionality of mapping MCQs to original cards via IDs remains intact

## Plan Update (2025-06-03): Type Safety Fixes for Card ID Comparisons

**Goal:** Ensure consistent type handling when comparing card IDs between original cards (`CardId` which can be `string | number`) and MCQ items (`originalCardId` which is `string`).

**Context:**
The original implementation had potential type mismatches when comparing card IDs:
- `Card.id` is of type `CardId` (can be `string | number`)
- `MCQItem.originalCardId` is a `string`
- Comparisons between these could fail if types don't match exactly

**Affected Files:**
* [`pickvocab-client/components/app/reviews/reviewTypes.ts`](pickvocab-client/components/app/reviews/reviewTypes.ts:1)
* [`pickvocab-client/components/app/reviews/useAiReviewGenerator.ts`](pickvocab-client/components/app/reviews/useAiReviewGenerator.ts:1)
* [`pickvocab-client/pages/app/reviews/[id].vue`](pickvocab-client/pages/app/reviews/[id].vue:1)

**Specific Changes:**

1. **Updated `MCQItem` Interface in `reviewTypes.ts`:**
   * Clarified that `originalCardId` is always a `string` for consistent comparison
   * Improved the union type definition for `LocalSessionReviewItem` for better type safety

2. **Enhanced Type Safety in `useAiReviewGenerator.ts`:**
   * Added explicit `String()` conversion when assigning `originalCardId` from `llmSourceCardId`
   * Ensured the assignment handles undefined values properly

3. **Fixed Comparison Logic in `pages/app/reviews/[id].vue`:**
   * Updated the `originalCardForMCQ` computed property to use `String()` conversion on both sides of the comparison
   * This ensures consistent string-to-string comparison regardless of the original card ID type

**Rationale:**
* **Type Safety:** Prevents runtime errors from type mismatches during card ID comparisons
* **Consistency:** Ensures all card ID comparisons use the same string format
* **Reliability:** Makes the MCQ-to-card mapping more robust across different card ID formats
* **Maintainability:** Clear type definitions make the code easier to understand and maintain

**Impact Analysis:**
* **Backward Compatibility:** All existing functionality preserved, just with better type safety
* **Performance:** Minimal impact - string conversion is very fast
* **Reliability:** Significantly improved - eliminates potential comparison failures due to type mismatches
* **Developer Experience:** Better TypeScript support and clearer error messages
