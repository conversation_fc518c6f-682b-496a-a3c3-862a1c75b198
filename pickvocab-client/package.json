{"name": "pickvocab-client", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@floating-ui/dom": "^1.6.13", "@nuxtjs/robots": "^4.1.11", "@nuxtjs/sitemap": "^6.1.5", "@pinia/nuxt": "0.5.0", "@tabler/icons-vue": "^3.17.0", "@tanstack/vue-query": "^5.80.6", "@tiptap/extension-highlight": "^2.10.3", "@tiptap/extension-placeholder": "^2.10.3", "@vueuse/core": "^11.3.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dexie": "^4.0.0", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-vue": "^8.6.0", "epubjs": "^0.3.93", "flowbite": "^2.5.2", "flowbite-vue": "0.1.3", "is-mobile": "^5.0.0", "locale-codes": "^1.3.1", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.469.0", "marked": "^13.0.3", "mdast": "^3.0.0", "nuxt": "^3.13.2", "nuxt-gtag": "^3.0.1", "nuxt-tiptap-editor": "^2.1.1", "pickvocab-dictionary": "workspace:*", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.1.2", "radix-vue": "^1.9.12", "reka-ui": "^2.3.1", "remark-parse": "^11.0.0", "sentence-splitter": "^5.0.0", "shadcn-nuxt": "2.0.0", "slugify": "^1.6.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tiptap-markdown": "^0.8.10", "tributejs": "^5.1.3", "unified": "^11.0.5", "vue": "latest", "vue-router": "latest", "vue-simple-context-menu": "^4.1.0", "yaml": "^2.5.0"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.12.2", "@tailwindcss/vite": "^4.1.3", "@types/lodash-es": "^4.17.12", "@types/mdast": "^4.0.4", "@types/wicg-file-system-access": "^2023.10.6", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "tailwindcss": "^3.4.17"}}