<script setup lang="ts">
// import { IconBook2 } from '@tabler/icons-vue';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';
import type { Modal } from 'flowbite';
import SubmitButton from '../../ui/SubmitButton.vue';
import { useCreateDeck } from '~/composables/useDeckMutations';

const store = useAppStore();
const router = useRouter();
const { isShowAddDeckModal: isShowAddCardListModal } = storeToRefs(store);

// TanStack Query mutation for creating deck
const { mutate: createDeckMutation, isPending: isCreatingDeck } = useCreateDeck();

let modal: Modal;
const name = ref('');
const description = ref('');

// Use mutation loading state instead of manual state
const isSubmitting = computed(() => isCreatingDeck.value);

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $modelEl = document.getElementById('add-cardlist-modal');
    modal = new Modal($modelEl);

    modal.updateOnShow(() => {
      name.value = '';
      description.value = '';
    });

    modal.updateOnHide(() => {
      store.hideAddDeckModal();
    });
  });
});

watch(isShowAddCardListModal, (value) => {
  if (value === true) {
    modal.show();
  } else {
    modal.hide();
  }
});

async function addDeck() {
  try {
    const baseDeck: BaseDeck = {
      name: name.value,
      description: description.value,
      cards: []
    }
    
    // Use TanStack Query mutation instead of direct store call
    createDeckMutation(baseDeck, {
      onSuccess: (deck) => {
        modal.hide();
        router.push({ name: 'app-notebooks-slug-id', params: { id: deck.id } });
      },
      onError: (error) => {
        console.error('Error adding deck:', error);
        // Optional: Show error message to user
      }
    });
  } catch (error) {
    console.error('Error adding deck:', error);
    // Optional: Show error message to user
  }
}
</script>

<template>
  <!-- Main modal -->
  <div id="add-cardlist-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <icon-book-2 class="inline-block w-5 h-5 me-3"></icon-book-2>
            <span>Create notebook</span>
          </h3>
          <button type="button" @click="store.hideAddDeckModal()"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <form class="p-4 md:p-5">
          <div class="grid gap-4 mb-4 grid-cols-2">
            <div class="col-span-2">
              <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Name</label>
              <input type="text" name="name" id="name"
                class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                placeholder="ex: Discussion and debate" required v-model="name">
            </div>

            <div class="col-span-2">
              <label for="description"
                class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Description</label>
              <div class="flex mb-2">
                <textarea id="description" rows="5"
                  class="block p-2.5 w-full text-sm text-gray-600 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  v-model="description"
                  placeholder="List of common vocabularies and phrases used in discussion and debate context"
                  ></textarea>
              </div>
            </div>
          </div>
          <div class="flex items-center mt-12">
            <SubmitButton 
              text="Add" 
              :isLoading="isSubmitting" 
              @click="addDeck" 
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
