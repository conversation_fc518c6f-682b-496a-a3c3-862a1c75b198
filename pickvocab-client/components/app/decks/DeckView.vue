<script setup lang="ts">
import { FwbPagination } from 'flowbite-vue';
import { Button } from '~/components/ui/button';
// import { IconPlus } from '@tabler/icons-vue';
// @ts-ignore
import IconPlus from '@tabler/icons-vue/dist/esm/icons/IconPlus.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
import { isMobile } from 'is-mobile';
import DeckViewAddModal from '~/components/app/decks/DeckViewAddModal.vue';
import DefinitionCardEntry from '~/components/app/cards/DefinitionCardEntry.vue';
import { CardType } from '~/utils/card';
import ContextCardEntry from '../cards/ContextCardEntry.vue';
import DangerAlert from '~/components/app/utils/DangerAlert.vue';
import { useDeckQueryWithCardCachePopulation, useInvalidateDeckQuery } from '~/composables/useDeckQuery';
import { useRemoveCardFromDeck } from '~/composables/useDeckMutations';
import Spinner from '~/components/app/utils/Spinner.vue';
import { useValidatedRouteId, ROUTE_PATTERNS } from '~/utils/routeValidation';

const props = withDefaults(defineProps<{
  isDemoPage?: boolean
}>(), { isDemoPage: false });

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const isShowAddCardModal = ref(false);

const currentPage = ref(1);
const deckId = useValidatedRouteId(computed(() => route), ROUTE_PATTERNS.DECKS);

// Replace initDeck with query (using TanStack Query v5 API)
const { data: deck, isPending, isFetching, error, refetch: refetchDeck } = useDeckQueryWithCardCachePopulation(
  computed(() => deckId.value || 0), 
  currentPage
)
const { invalidateDeck } = useInvalidateDeckQuery()
const { mutate: removeCardFromDeckMutation } = useRemoveCardFromDeck()

// Computed properties for template
const totalPages = computed(() => {
  const totalCards = deck.value ? deck.value.totalCards || deck.value.cards.length : 0
  return deck.value ? Math.ceil(totalCards / 20) : 0
})

// V5 loading states: isPending = initial loading
// Note: isLoading not needed since we use isPending directly in template

useSeoMeta({
  title: computed(() => deck.value ? `${deck.value.name} - Notebooks | Pickvocab` : 'Notebooks | Pickvocab'),
});

// Update URL slug when deck data changes
watch(() => deck.value, (newDeck) => {
  if (newDeck) {
    const newRoute = router.resolve({
      params: { slug: slugifyText(newDeck.name), ...route.params },
      query: route.query
    });
    window.history.replaceState('', '', newRoute.fullPath);
  }
}, { immediate: true })

// Note: Removed manual refetch watcher - TanStack Query automatically handles refetching 
// when deckId changes due to reactive query keys

// Watch for page changes in URL and sync to local state
watch(() => route.query.page, (value) => {
  if (Array.isArray(value)) throw new Error('Unexpected');
  currentPage.value = value ? Number(value) : 1;
}, { immediate: true });

// Watch for local page changes and update URL
// Note: Removed manual refetch - TanStack Query automatically refetches when currentPage changes
watch(currentPage, (newPage) => {
  // Update URL
  if (newPage === 1) {
    router.replace({ query: { page: undefined } });
  } else {
    router.push({ query: { page: newPage } });
  }
});

// Long press logic for mobile card deletion
const longPressedCardId = ref<CardId | null>(null);
let longPressTimeout: ReturnType<typeof setTimeout> | null = null;

const handleCardTouchStart = (cardId: CardId) => {
  if (!isMobile()) return;
  if (longPressTimeout) {
    clearTimeout(longPressTimeout);
  }
  longPressTimeout = setTimeout(() => {
    longPressedCardId.value = cardId;
  }, 600); // 600ms for long press
};

const handleCardTouchEnd = () => {
  if (!isMobile()) return;
  if (longPressTimeout) {
    clearTimeout(longPressTimeout);
    longPressTimeout = null;
  }
};

const handleGlobalCardTouch = (e: TouchEvent) => {
  if (!isMobile()) return;
  // If the tap is outside any card item that might have an active remove button, hide it.
  if (!(e.target as HTMLElement).closest('.card-item-interactive-area')) {
    if (longPressedCardId.value && !(e.target as HTMLElement).closest('.remove-button-class')) {
      longPressedCardId.value = null;
    }
  }
};

function handleRemoveCardFromDeck(cardId: CardId) {
  if (window.confirm('Are you sure you want to remove this card from the notebook?')) {
    try {
      // Use optimistic mutation for immediate UI feedback
      removeCardFromDeckMutation({ deckId: deck.value!.id, cardId: cardId });
      longPressedCardId.value = null; // Reset after action
    } catch (e) {
      console.error('Failed to remove card from deck:', e);
      alert('Failed to remove card. See console for details.');
    }
  } else {
    // If user cancels, ensure button is hidden on mobile if it was shown by long press
    if (longPressedCardId.value === cardId) {
      longPressedCardId.value = null;
    }
  }
}

// Removed reload function - optimistic updates handle the UI updates
// and the mutation's onSuccess will trigger background refresh

function goToCard(cardId: CardId) {
  // If a remove button was shown via long press, hide it before navigating
  if (longPressedCardId.value) {
    longPressedCardId.value = null;
    // Add a small delay to ensure the state updates before navigation, preventing click bleed-through
    setTimeout(() => {
      if (props.isDemoPage) {
        router.push({ name: 'demo-cards-slug-id', params: { id: cardId } });
      } else {
        router.push({ name: 'app-cards-slug-id', params: { id: cardId } });
      }
    }, 50); 
    return;
  }

  if (props.isDemoPage) {
    router.push({ name: 'demo-cards-slug-id', params: { id: cardId } });
  } else {
    router.push({ name: 'app-cards-slug-id', params: { id: cardId } });
  }
}

// Lifecycle hooks for global touch handling
onMounted(() => {
  if (isMobile()) {
    document.addEventListener('touchstart', handleGlobalCardTouch, { passive: true });
  }
});

onBeforeUnmount(() => {
  if (isMobile()) {
    document.removeEventListener('touchstart', handleGlobalCardTouch);
  }
  if (longPressTimeout) {
    clearTimeout(longPressTimeout);
  }
});

</script>

<template>
  <div class="w-full h-full box-border bg-gray-50">
    <!-- Background fetch indicator (v5: show when fetching but not initial loading) -->
    <div 
      v-if="isFetching && !isPending" 
      class="fixed top-20 right-6 z-50 transition-opacity duration-200 flex items-center space-x-2"
      aria-label="Updating deck data in background"
    >
      <Spinner :show-message="false" :size="'4'" class="w-4 h-4 text-gray-400" />
      <span class="text-xs text-gray-400 font-normal">Syncing...</span>
    </div>

    <div class="sm:ml-64 mt-14 bg-gray-50">
      <div v-if="deck">
        <div class="py-10 px-4 lg:px-32 xl:px-44 2xl:px-64">
          <div class="flex items-center py-2 px-2 sm:px-0">
            <p class="text-3xl text-gray-700 font-semibold">{{ deck.name }}</p>
            <span v-if="deck.isDemo"
              class="ml-2 bg-yellow-100 text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded border border-yellow-300">Public</span>
            <Button 
              v-if="deck.owner === authStore.currentUser?.id" 
              @click="isShowAddCardModal = true" 
              size="sm" 
              class="bg-blue-600 hover:bg-blue-700 text-white px-2 sm:px-3 py-1.5 text-sm ml-auto"
            >
              <icon-plus stroke-width="1.5" class="w-3 h-3"></icon-plus>
              <span class="hidden sm:inline">Add Card</span>
            </Button>
          </div>

          <p class="text-base text-gray-500 mt-4" v-if="deck.description">
            {{ deck.description }}
          </p>
        </div>

        <div v-for="card in deck.cards" class="px-4 lg:px-32 xl:px-44 2xl:px-64">
          <div class="relative group card-item-interactive-area mb-4"
            @touchstart="handleCardTouchStart(card.id)" 
            @touchend="handleCardTouchEnd"
            @touchmove="handleCardTouchEnd">
            <DefinitionCardEntry v-if="card.cardType === CardType.DefinitionCard" :card="card" 
              @click="goToCard(card.id)" class="cursor-pointer"></DefinitionCardEntry>
            <ContextCardEntry v-if="card.cardType === CardType.ContextCard" :card="card" 
              @click="goToCard(card.id)" class="cursor-pointer"></ContextCardEntry>
            <button
              v-if="longPressedCardId === card.id || !isMobile()"
              @click.stop="handleRemoveCardFromDeck(card.id)"
              class="remove-button-class absolute top-3 right-3 z-20
                     opacity-100 sm:opacity-0 sm:group-hover:opacity-100
                     transition-opacity duration-200 ease-in-out 
                     bg-white/90 hover:bg-red-500 text-red-600 hover:text-white 
                     rounded-full p-1.5 shadow-md hover:shadow-lg"
              aria-label="Remove card from deck"
            >
              <IconX class="w-4 h-4" />
            </button>
          </div>
        </div>

        <fwb-pagination class="mt-4 py-8 flex justify-center items-center" v-model="currentPage"
          :total-pages="totalPages"></fwb-pagination>

        <DeckViewAddModal v-if="deck !== undefined" :deck="deck" v-model:is-show-modal="isShowAddCardModal"></DeckViewAddModal>
      </div>
      <!-- Update error handling -->
      <div v-else-if="error" class="py-10 pl-10 pr-10">
        <p class="text-red-500">Error loading deck: {{ error.message }}</p>
        <button @click="deckId && invalidateDeck(deckId)" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
          Retry
        </button>
      </div>

      <!-- Update empty state check (v5: use isPending for initial loading) -->
      <div v-else-if="!isPending" class="py-10 pl-10 pr-10">
        <DangerAlert
          :label="!authStore.user ? 'Sign in to see this page' : 'Notebook not found'"
          :primary-btn-label="!authStore.user ? 'Sign in' : undefined"
          @primary-btn-click="!authStore.user ? authStore.showLoginModal() : undefined"
        />
      </div>

      <!-- Initial loading state (v5: use isPending for initial loading) -->
      <div v-else class="py-10 pl-10 pr-10 flex flex-col h-full items-center justify-center">
        <Spinner />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
