<script setup lang="ts">
// import { IconBook2, IconPlus } from '@tabler/icons-vue';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';
// @ts-ignore
import IconPlus from '@tabler/icons-vue/dist/esm/icons/IconPlus.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
import { FwbButton, FwbPagination } from 'flowbite-vue';
import { Button } from '~/components/ui/button';
import DeckEntry from '~/components/app/decks/DeckEntry.vue';
import Spinner from '~/components/app/utils/Spinner.vue';
// import { type Deck, type DeckId } from '~/stores/app'; // Removed this line
import { ref, onMounted, onBeforeUnmount, type Ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAppStore } from '~/stores/app';
import { useAuthStore } from '~/stores/auth';
import { useDecksQuery, useInvalidateDecksQuery } from '~/composables/useDecksQuery';
import { useDeleteDeck } from '~/composables/useDeckMutations';
import isMobile from 'is-mobile';

const props = withDefaults(defineProps<{
  isDemoPage?: boolean
}>(), { isDemoPage: false });

const router = useRouter();
const route = useRoute(); // route might not be used, but good to have if pagination/filtering comes later

const store = useAppStore();
const authStore = useAuthStore();

const longPressedDeckId = ref<DeckId | null>(null);
const currentPage = ref(1);

// Replace fetchDecks with query (using TanStack Query v5 API)
const { data: queryData, isPending, isFetching, error, refetch: refetchDecks } = useDecksQuery(currentPage, props.isDemoPage)
const { invalidateAllDecks, invalidateDecks } = useInvalidateDecksQuery()
const { mutate: deleteDeckMutation } = useDeleteDeck()

// Computed properties for template
const renderedDecks = computed(() => queryData.value?.results || [])
const totalPages = computed(() => {
  if (!queryData.value?.count) return 0
  return Math.ceil(queryData.value.count / 20) // 20 items per page
})

// V5 loading states: isPending = initial loading, isLoading = isPending && isFetching
const isLoading = computed(() => isPending.value && isFetching.value)

// Watch for route changes to trigger background fetch when navigating to this page
watch(() => route.path, (newPath, oldPath) => {
  // Check if we're navigating TO this decks page (not away from it)
  const isDecksPage = newPath?.includes('/notebooks') && !newPath?.includes('/notebooks/');
  const wasOnDecksPage = oldPath?.includes('/notebooks') && !oldPath?.includes('/notebooks/');
  
  // Note: Removed manual refetch on navigation - TanStack Query automatically handles data freshness
}, { immediate: false })

onMounted(async () => {
  // Note: Removed mount refetch - TanStack Query handles initial data fetching automatically
  
  // Watch for route page parameter changes
  watch(() => route.query.page, async (value) => {
    if (Array.isArray(value)) throw new Error('Unexpected');
    currentPage.value = value ? Number(value) : 1;
  }, { immediate: true });

  // Update URL when page changes
  watch(currentPage, (newPage) => {
    if (newPage === 1) {
      router.replace({ query: { page: undefined } });
    } else {
      router.push({ query: { page: newPage } });
    }
  });
  
  if (isMobile()) {
    document.addEventListener('touchstart', handleGlobalDeckTouch, { passive: true });
  }
});

onBeforeUnmount(() => {
  if (isMobile()) {
    document.removeEventListener('touchstart', handleGlobalDeckTouch);
  }
  clearTimeout(longPressTimer); // Clear timer if component is unmounted
});

let longPressTimer: any;
const LONG_PRESS_DURATION = 600; // ms

function handleDeckTouchStart(deckId: DeckId) {
  if (!isMobile()) return;
  clearTimeout(longPressTimer); // Clear any existing timer
  longPressTimer = setTimeout(() => {
    longPressedDeckId.value = deckId;
  }, LONG_PRESS_DURATION);
}

function handleDeckTouchEnd() {
  if (!isMobile()) return;
  clearTimeout(longPressTimer);
}

function handleGlobalDeckTouch(event: TouchEvent) {
  if (!isMobile()) return;
  // Check if the touch is outside any .deck-item-interactive-area
  // And also not on a remove button itself
  const target = event.target as HTMLElement;
  if (!target.closest('.deck-item-interactive-area') && !target.closest('.remove-button-class')) {
    if (longPressedDeckId.value !== null) {
      longPressedDeckId.value = null;
    }
  }
}

async function handleRemoveDeck(deckId: DeckId) {
  if (window.confirm('Are you sure you want to remove this notebook?')) {
    try {
      // Use optimistic mutation for immediate UI feedback
      deleteDeckMutation(deckId);
      longPressedDeckId.value = null; // Hide button after removal
    } catch (e) {
      console.error('Failed to remove deck:', e);
      alert('Failed to remove deck. See console for details.');
    }
  } else {
    // If user cancels, ensure button is hidden on mobile if it was shown by long press
    if (isMobile() && longPressedDeckId.value === deckId) {
      longPressedDeckId.value = null;
    }
  }
}



function goToDeck(deckId: DeckId) {
  if (longPressedDeckId.value) {
    longPressedDeckId.value = null;
    setTimeout(() => {
      if (props.isDemoPage) {
        router.push({ name: 'demo-notebooks-slug-id', params: { id: deckId } });
      } else {
        router.push({ name: 'app-notebooks-slug-id', params: { id: deckId } });
      }
    }, 50); // Small delay before navigation
    return;
  }
  // Original navigation logic
  if (props.isDemoPage) {
    router.push({ name: 'demo-notebooks-slug-id', params: { id: deckId } });
  } else {
    router.push({ name: 'app-notebooks-slug-id', params: { id: deckId } });
  }
}
</script>

<template>
  <div class="w-full h-full box-border bg-gray-50">
    <!-- Background fetch indicator (v5: show when fetching but not initial loading) -->
    <div 
      v-if="isFetching && !isPending"
      class="fixed top-20 right-6 z-50 transition-opacity duration-200 flex items-center space-x-2"
      aria-label="Updating data in background"
    >
      <Spinner :show-message="false" :size="'4'" class="w-4 h-4 text-gray-400" />
      <span class="text-xs text-gray-400 font-normal">Syncing...</span>
    </div>

    <div v-if="renderedDecks.length > 0" class="sm:ml-64 mt-14 bg-gray-50">
      <div class="py-10 px-4 lg:px-32 xl:px-44 2xl:px-64">
        <div class="mb-8 flex items-center justify-between px-2 sm:px-0">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">Notebooks</h1>
          </div>
          <Button @click="store.showAddDeckModal()" size="sm" class="bg-blue-600 hover:bg-blue-700 text-white px-2 sm:px-3 py-1.5 text-sm">
            <icon-plus stroke-width="1.5" class="w-3 h-3"></icon-plus>
            <span class="hidden sm:inline">Create</span>
          </Button>
        </div>
        <div v-for="deck in renderedDecks" :key="deck.id" class="relative group deck-item-interactive-area mb-4"
          @touchstart="handleDeckTouchStart(deck.id)"
          @touchend="handleDeckTouchEnd"
          @touchmove="handleDeckTouchEnd">
          <button
            v-if="longPressedDeckId === deck.id || !isMobile()"
            @click.stop="handleRemoveDeck(deck.id)"
            class="remove-button-class absolute top-3 right-3 z-20
                   opacity-100 sm:opacity-0 sm:group-hover:opacity-100
                   transition-opacity duration-200 ease-in-out
                   bg-white/90 hover:bg-red-500 text-red-600 hover:text-white
                   rounded-full p-1.5 shadow-md hover:shadow-lg"
            aria-label="Remove deck"
          >
            <IconX class="w-4 h-4" />
          </button>
          <DeckEntry :deck="deck" :public="deck.isDemo" class="cursor-pointer"
            @click="goToDeck(deck.id)" />
        </div>
        
        <!-- Pagination -->
        <fwb-pagination 
          v-if="totalPages > 1"
          class="mt-4 py-8 flex justify-center items-center" 
          v-model="currentPage"
          :total-pages="totalPages">
        </fwb-pagination>
      </div>
      <!-- vue-simple-context-menu removed -->
    </div>

    <!-- Update error handling -->
    <div v-else-if="error" class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <p class="text-red-500">Error loading decks: {{ error.message }}</p>
      <button @click="invalidateDecks(props.isDemoPage)" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
        Retry
      </button>
    </div>

    <!-- Update empty state check (v5: use isPending for initial loading) -->
    <div v-else-if="!isPending" class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <icon-book-2 stroke-width="1.5" class="w-8 h-8 text-gray-500"></icon-book-2>
      <p class="mt-4 text-gray-500">Create your first deck</p>
      <div class="flex mt-4 space-x-2">
        <fwb-button @click="store.showAddDeckModal()">
          <div class="flex items-center">
            <icon-plus stroke-width="1.5" class="w-4 h-4"></icon-plus>
            <span class="ml-1">Create new deck</span>
          </div>
        </fwb-button>
      </div>
    </div>

    <!-- Initial loading state (v5: use isPending for initial loading) -->
    <div v-else class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <Spinner />
    </div>
  </div>
</template>

<style scoped></style>