<script setup lang="ts">
import TiptapEditor from '~/components/editor/TiptapEditor.vue';
import MarkdownRenderer from '~/components/app/utils/MarkdownRenderer.vue';
import { type WordInContextEntry } from 'pickvocab-dictionary';
import { stylesForPartOfSpeech } from '~/utils/utils';

const props = withDefaults(
  defineProps<{
    wordEntry: WordInContextEntry;
    showContext?: boolean;
  }>(),
  {
    showContext: false,
  }
);

const emit = defineEmits(['save']);
</script>

<template>
  <div v-if="wordEntry.definition">
    <div v-if="showContext">
      <blockquote class="text-gray-600 text-sm">
        <TiptapEditor
          :text="wordEntry.context"
          :selected-text="wordEntry.word"
          :offset="wordEntry.offset"
          :css-classes="'tiptap prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl focus:outline-none w-full max-h-[300px] overflow-auto !text-gray-600 !bg-gray-50 p-4'"
          :show-options="false"
          :show-bubble-menu="false"
          :editable="false"
        />
      </blockquote>
      <!-- <blockquote class="px-4 border-l-4 border-gray-200 border-solid italic text-gray-600 text-sm" v-html="getHighlightedContext(wordEntry)"></blockquote> -->
      <!-- <p class="text-gray-600 text-sm">The fact that the battery life is on par with the macbook air 15 while the vivobook has a 120Hz screen and two fans is plain bonkers for a windows machine</p> -->
    </div>

    <div class="flex mt-8 items-centers">
      <p class="text-base text-gray-600">
        <span
          class="inline-block text-sm border rounded-xl px-2 align-middle"
          :class="stylesForPartOfSpeech(wordEntry.definition.partOfSpeech)"
        >
          {{ wordEntry.definition.partOfSpeech }}
        </span>
        <span class="ml-1">{{ wordEntry.definition.definition }}</span>
      </p>
      <div v-if="!showContext" @click="emit('save')" class="ml-1 text-gray-700">
        <div class="p-1 hover:rounded hover:bg-gray-100">
          <icon-bookmark
            class="w-4 h-4 cursor-pointer flex-shrink-0"
          ></icon-bookmark>
        </div>
      </div>
    </div>

    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Explanation</p>
      <div class="text-base text-gray-600 mt-2">
        <MarkdownRenderer :source="wordEntry.definition.explanation" />
      </div>
    </div>

    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Examples</p>
      <ol class="list-decimal list-inside text-base text-gray-600">
        <li v-for="example in wordEntry.definition.examples" class="p-1 mt-2">
          <blockquote
            class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic"
          >
            <MarkdownRenderer :source="example.example" />
          </blockquote>
          <div class="mt-4">
            <MarkdownRenderer :source="example.explanation" />
          </div>
        </li>
      </ol>
    </div>

    <div class="mt-8">
      <div class="flex items-center">
        <p class="text-xl text-gray-700 font-semibold">Synonyms</p>
      </div>
      <ol class="text-base text-gray-600 mt-4">
        <li v-for="synonym in wordEntry.definition.synonyms" class="mt-4">
          <NuxtLink
            :to="{ name: 'app-dictionary', query: { word: synonym.synonym } }"
            target="_blank"
            rel="nofollow"
            class="font-semibold underline text-blue-800"
            >{{ synonym.synonym }}</NuxtLink
          >
          <blockquote
            class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic"
          >
            {{ synonym.example }}
          </blockquote>
          <div class="mt-4">
            <MarkdownRenderer :source="synonym.explanation" />
          </div>
        </li>
      </ol>
    </div>

    <div class="mt-8 border-t">
      <NuxtLink
        :to="{ name: 'app-dictionary', query: { word: wordEntry.word } }"
        target="_blank"
        rel="nofollow"
        class="mt-4 font-semibold text-blue-800 flex items-center hover:underline"
      >
        <span>See other definitions of "{{ wordEntry.word }}"</span>
        <icon-arrow-right class="w-4 h-4 ml-1"></icon-arrow-right>
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped></style>
