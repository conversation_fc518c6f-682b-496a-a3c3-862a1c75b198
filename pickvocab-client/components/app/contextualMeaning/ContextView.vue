<script setup lang="ts">
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
// @ts-ignore
import IconDotsVertical from '@tabler/icons-vue/dist/esm/icons/IconDotsVertical.mjs';
// @ts-ignore
import IconReload from '@tabler/icons-vue/dist/esm/icons/IconReload.mjs';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
import TiptapEditor from '~/components/editor/TiptapEditor.vue';
import { Dictionary, type DictionarySource, type WordInContextEntry } from 'pickvocab-dictionary';
import { FwbToggle } from "flowbite-vue";
import Spinner from '~/components/app/utils/Spinner.vue';
import { RemoteWordInContextApi } from '~/api/wordInContext';
import Dropdown from '~/components/app/utils/Dropdown.vue';
import { FwbTooltip } from 'flowbite-vue';
import DictionaryWordViewErrorAlert from '../dictionary/DictionaryWordViewErrorAlert.vue';
import ExplanationView from './ExplanationView.vue';
import { reduceContext } from '~/utils/contextCard';
import type { BaseContextCard } from '~/utils/card';
import { useCreateContextCard } from '~/composables/useCardMutations';

const store = useAppStore();
const llmStore = useLLMStore();
const route = useRoute();
const router = useRouter();

// TanStack Query mutations
const { mutate: createContextCard } = useCreateContextCard();
const wordEntry = ref<WordInContextEntry | undefined>(undefined);
const api = new RemoteWordInContextApi();

const word = ref<string | undefined>();
const context = ref<string | undefined>();
const offset = ref<number | undefined>();
const isLoading = ref(false);
const selected = ref<{ selectedText: string, offset: number, text: string } | undefined>(undefined);
const errorMessage = ref('');

const selectedSimpleViewLanguage = ref<string | undefined>('English');

watch(selectedSimpleViewLanguage, () => {
  simpleLookupForLanguage(selectedSimpleViewLanguage.value);
});

const reducedContext = computed(() => {
  if (!word.value || !context.value || offset.value === undefined) {
    return { text: '', selectedText: '', offset: 0 };
  }
  return reduceContext(context.value, word.value, offset.value, 3);
});

const dictionary = computed(() => {
  let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
  if (llmStore.activeUserModel) {
    sources = [llmStore.createDictionarySource(llmStore.activeUserModel), ...sources];
  }
  const dictionary = new Dictionary(sources);
  return dictionary;
});

const title = computed(() => {
  return wordEntry.value ? `${wordEntry.value.word} - Contextual Lookup | Pickvocab` : 'Contextual Lookup | Pickvocab';
});

const description = computed(() => {
  return wordEntry.value ? `${wordEntry.value.definition?.definition || wordEntry.value.definitionShort?.explanation}. Learn more.` : 'Learn more.';
});

const llmModel = computed(() => {
  return wordEntry.value ? llmStore.getModelById(wordEntry.value?.llm_model) : undefined;
});

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description,
  twitterTitle: title,
  twitterDescription: description,
});

onMounted(async () => {
  watch(() => route.params.id, async () => {
    if (route.params.id !== undefined && typeof route.params.id === 'string') {
      const paramId = Number(route.params.id);
      
      // Skip if we already have this entry loaded
      if (wordEntry.value?.id === paramId) return;
      
      // Skip API call for temporary IDs (negative numbers)
      if (paramId < 0) return;
      
      let entry = await api.get(paramId);

      if (entry) {
        word.value = entry.word;
        context.value = entry.context;
        offset.value = entry.offset;
        if (entry.definition === null || entry.definition === undefined) {
          isLoading.value = true;
          entry = {
            ...entry,
            ...await dictionary.value.getMeaningInContext(word.value, context.value, offset.value)
          };
          wordEntry.value = await api.put(entry);
          isLoading.value = false;
        } else {
          wordEntry.value = entry;
        }
      }
    }
  }, { immediate: true });
});

watch(() => store.isShowWordContextDetailed, () => {
  if (word.value === undefined || context.value === undefined || offset.value === undefined) return;
  if (store.isShowWordContextDetailed) {
    if (wordEntry.value?.definition) return;
    refresh();
  } else {
    if (wordEntry.value?.definitionShort) return;
    refresh();
  }
});

async function handleLookup(event: { text: string, offset: number, selectedText: string }) {
  try {
    word.value = event.selectedText;
    context.value = event.text;
    offset.value = event.offset;

    wordEntry.value = undefined;
    isLoading.value = true;
    
    // Use reducedContext for lookup
    const reduced = reduceContext(event.text, event.selectedText, event.offset, 3);
    const base = store.isShowWordContextDetailed ?
      await dictionary.value.getMeaningInContext(reduced.selectedText, reduced.text, reduced.offset)
      : await dictionary.value.getMeaningInContextShort(reduced.selectedText, reduced.text, reduced.offset);
    
    // Create a temporary local wordEntry immediately
    const tempId = -Date.now(); // Negative timestamp as temporary ID
    wordEntry.value = {
      ...base,
      id: tempId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as WordInContextEntry;
    
    isLoading.value = false;
    selectedSimpleViewLanguage.value = 'English';

    // Navigate with temporary ID first
    router.push({
      name: 'app-contextual-meaning-id',
      params: {
        id: tempId
      }
    });

    // Create on server in the background
    api.create(base).then(serverEntry => {
      // Update with real server data
      wordEntry.value = serverEntry;
      
      // Update route with real ID if we're still on the temp route
      if (route.params.id === String(tempId)) {
        router.replace({
          name: 'app-contextual-meaning-id',
          params: {
            id: serverEntry.id
          }
        });
      }
    }).catch(err => {
      console.error('Failed to save to server:', err);
      // Optionally show a non-blocking notification
      // The user can still see and use the definition
    });
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
    isLoading.value = false;
  }
}

async function handleSelect(event: { selectedText: string, offset: number, text: string }) {
  selected.value = event;
}

async function simpleLookupForLanguage(language = 'English') {
  if (language === 'English') {
    if (wordEntry.value?.definitionShort) return;
  } else {
    if (wordEntry.value?.definitionShort?.languages?.[language] && wordEntry.value.definitionShort.languages[language].explanation) return;
  }
  refresh(language);
}

async function refresh(language = 'English') {
  try {
    const oldEntry = wordEntry.value!;
    if (!oldEntry || !oldEntry.word || !oldEntry.context || oldEntry.offset === undefined) {
      console.error('Cannot refresh: missing word entry data');
      return;
    }
    
    wordEntry.value = undefined;
    isLoading.value = true;

    let base;
    if (language === 'English') {
      // Use reducedContext for refresh
      const reduced = reduceContext(oldEntry.context, oldEntry.word, oldEntry.offset, 3);
      base = store.isShowWordContextDetailed ? await dictionary.value.getMeaningInContext(
        reduced.selectedText,
        reduced.text,
        reduced.offset,
      ) : await dictionary.value.getMeaningInContextShort(
        reduced.selectedText,
        reduced.text,
        reduced.offset,
      );
    } else {
      if (store.isShowWordContextDetailed) throw new Error('Unsupported');
      base = await dictionary.value.getMeaningInContextShortForLanguage(oldEntry, language);
    }
    
    // Create updated entry and show immediately
    const newEntry = {
      ...oldEntry,
      ...base,
    };
    wordEntry.value = newEntry;
    isLoading.value = false;

    // Save to server in the background
    api.put(newEntry).then(serverEntry => {
      // Update with server response
      wordEntry.value = serverEntry;
    }).catch(err => {
      console.error('Failed to save refresh to server:', err);
      // Keep the local version, user can still see the updated definition
    });
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
    isLoading.value = false;
  }
}

async function addCard(wordEntry: WordInContextEntry, callback?: () => void) {
  const baseCard: BaseContextCard = {
    wordInContext: wordEntry
  };
  
  createContextCard(baseCard, {
    onSuccess: (card) => {
      router.push({ name: 'app-cards-slug-id', params: { id: card.id } });
      if (callback) callback();
    },
    onError: (error) => {
      console.error('Error creating card:', error);
      if (callback) callback();
    }
  });
}
</script>

<template>
  <div
    class="sm:ml-64 pt-24 pb-10 px-4 xl:px-24 xl:pr-32 2xl:px-24 2xl:pr-48 h-full flex flex-col bg-gray-50 overflow-auto">
    <div>
      <TiptapEditor 
        :text="context"
        :selected-text="word"
        :offset="offset"
        @highlight="handleLookup"
        :css-classes="'tiptap !text-base md:!text-sm prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl m-5 focus:outline-none w-full min-h-[150px] max-h-[400px] overflow-auto border border-gray-200 rounded-md p-4'"
        @select="handleSelect" />
      <div v-if="!selected?.selectedText" class="mt-2 text-xs text-gray-500">Highlight word or phrase to see its exact
        meaning
        in the passage (Ctrl/Cmd + Shift + H)</div>
      <button v-else @click="handleLookup(selected)"
        class="mt-2 text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-2 py-1 text-center me-2 mb-2 flex items-center">
        <icon-search class="w-4 mr-1"></icon-search>
        <span>Lookup</span>
      </button>
    </div>

    <div v-if="errorMessage" class="mt-8">
      <DictionaryWordViewErrorAlert @retry="refresh()" @setup="store.showAPIKeyModal()" :message="errorMessage"
        :is-active-user-model="llmStore.activeUserModel ? true : false"></DictionaryWordViewErrorAlert>
    </div>

    <ExplanationView
      :word="word"
      :word-entry="wordEntry"
      :llm-model="llmModel"
      :is-loading="isLoading"
      v-model:is-detailed="store.isShowWordContextDetailed"
      v-model:selected-simple-view-language="selectedSimpleViewLanguage"
      @add-card="(wordEntry, callback) => addCard(wordEntry, callback)"
      @refresh="(language) => refresh(language)"
      @simple-lookup-for-language="(language) => simpleLookupForLanguage(language)"
      class="mt-8"
    />
  </div>
</template>
