<script setup lang="ts">
// import { IconCards, IconX } from '@tabler/icons-vue';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
import type { Dropdown, Modal } from 'flowbite';
import type { PartOfSpeech } from 'pickvocab-dictionary';
import SubmitButton from '../../ui/SubmitButton.vue';
import { stylesForPartOfSpeech } from '~/utils/utils';
import { useCreateDefinitionCard } from '~/composables/useCardMutations';
import { useAddCardToDeck } from '~/composables/useDeckMutations';
import { useInfiniteDecksSearchQueryWithAbort } from '~/composables/useInfiniteDecksSearchQuery';
import { useIntersectionObserver } from '@vueuse/core';

const store = useAppStore();
const router = useRouter();
const { isShowAddCardModal, currentAddWord } = storeToRefs(store);

// TanStack Query mutations for proper cache management
const { mutate: createDefinitionCardMutation, isPending: isCreatingDefinitionCard } = useCreateDefinitionCard();
const addCardToDeckMutation = useAddCardToDeck();
const pickDefinitionToggle = ref(false);

const word = ref('');
const examples: Ref<string[]> = ref(['']);
const definition = ref('');
const defIdx = ref(0);
const partOfSpeech = ref('noun');
const isShowAddDecks = ref(false);
const selectedDecks = ref<Deck[]>([]);
const deckSelectedIdx = ref(0);
const searchDeckText = ref('');
const deckRefs = ref([]);
const isSubmitting = ref(false);

// Infinite scroll for deck search
const { 
  isPending: isDecksLoading, 
  isFetching: isDecksRefreshing,
  hasNextPage, 
  fetchNextPage, 
  isFetchingNextPage,
  error: decksError,
  allDecks,
  totalCount,
  cancelCurrentRequest 
} = useInfiniteDecksSearchQueryWithAbort(searchDeckText);

// Intersection observer for infinite scroll
const loadMoreRef = ref<HTMLElement | null>(null);
const scrollContainerRef = ref<HTMLElement | null>(null);

const { stop: stopIntersectionObserver } = useIntersectionObserver(
  loadMoreRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && hasNextPage.value && !isFetchingNextPage.value) {
      fetchNextPage();
    }
  },
  { 
    threshold: 0.1,
    root: scrollContainerRef
  }
);

// Use allDecks as suggestedDecks for backward compatibility
const suggestedDecks = computed(() => allDecks.value);

// Computed loading state that combines manual and mutation loading
const isLoading = computed(() => isSubmitting.value || isCreatingDefinitionCard.value || addCardToDeckMutation.isPending.value);

let modal: Modal;
let partOfSpeechDropdown: Dropdown;

onMounted(() => {
  useFlowbite(({ Modal, Dropdown }) => {
    const $modelEl = document.getElementById('add-word-modal');
    modal = new Modal($modelEl);

    modal.updateOnShow(async () => {
      pickDefinitionToggle.value = false;
      defIdx.value = store.currentDefIdx;
      examples.value = [''];
      word.value = currentAddWord.value?.word || '';
      definition.value = currentAddWord.value?.definitions[defIdx.value].definition || '';
      partOfSpeech.value = currentAddWord.value?.definitions[defIdx.value].partOfSpeech || 'noun';
      isShowAddDecks.value = false;
      searchDeckText.value = '';
      selectedDecks.value = [];
      deckSelectedIdx.value = 0;
    });

    modal.updateOnHide(() => {
      store.hideAddCardModal();
      isShowAddDecks.value = false;
      // Cancel any ongoing requests when modal closes
      cancelCurrentRequest();
      // Stop intersection observer
      stopIntersectionObserver();
    });

    const $partOfSpeechDropdownMenu = document.getElementById('part-of-speech-dropdown-menu');
    const $partOfSpeechDropdownBtn = document.getElementById('part-of-speech-dropdown-btn');
    partOfSpeechDropdown = new Dropdown($partOfSpeechDropdownMenu, $partOfSpeechDropdownBtn, {
      offsetSkidding: 18
    });
  });
});

watch(isShowAddCardModal, (value) => {
  if (value === true) {
    // if (get(currentAddWord.value, 'definitions[0].examples[0]')) {
    //   currentExamples.value = [currentAddWord.value!.definitions[0].examples![0]];
    // } else {
    //   currentExamples.value = [''];
    // }
    modal.show();
  } else {
    modal.hide();
  }
});

watch(currentAddWord, () => {
  word.value = currentAddWord.value?.word || '';
});

watch(defIdx, () => {
  definition.value = currentAddWord.value?.definitions[defIdx.value].definition || '';
  partOfSpeech.value = currentAddWord.value?.definitions[defIdx.value].partOfSpeech || 'noun';
});

// Watch for changes in suggested decks to update selected index
watch(() => suggestedDecks.value.length, (newLength) => {
  if (deckSelectedIdx.value >= newLength && newLength > 0) {
    deckSelectedIdx.value = 0;
  }
});

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'ArrowUp') {
    e.stopPropagation();
    e.preventDefault();
    deckSelectedIdx.value = Math.max(0, deckSelectedIdx.value - 1);
  } else if (e.key === 'ArrowDown') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDecks.value.length > 0) {
      deckSelectedIdx.value = Math.min(suggestedDecks.value.length - 1, deckSelectedIdx.value + 1);
    }
  } else if (e.key === 'Enter') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDecks.value.length > 0) {
      addSelectedDeck(deckSelectedIdx.value);
      searchDeckText.value = '';
    }
  }
}

function addSelectedDeck(idx: number) { // suggestedDecks idx
  const selectedDeckIdx = selectedDecks.value.findIndex((deck) => deck.id === suggestedDecks.value[idx].id);
  if (selectedDeckIdx !== -1) {
    removeDeck(selectedDeckIdx);
    return;
  }
  selectedDecks.value.push(suggestedDecks.value[idx]);
}

function removeDeck(idx: number) { // selectedDeck idx
  if (idx >= 0) {
    selectedDecks.value.splice(idx, 1);
  }
}

function isInSelectedDecks(deck: Deck) {
  return selectedDecks.value.find((d) => d.id === deck.id);
}

watch(isShowAddDecks, () => {
  if (isShowAddDecks.value) {
    window.addEventListener('keydown', handleKeyPress, true);
  } else {
    window.removeEventListener('keydown', handleKeyPress, true);
  }
})

watch(deckSelectedIdx, (value) => {
  (deckRefs.value[value] as any).scrollIntoView();
});

function addMoreExample() {
  // const idx = currentExamples.value.length;
  // if (get(currentAddWord.value, `definitions[0].examples[${idx}]`)) {
  //   currentExamples.value.push(currentAddWord.value!.definitions[0].examples![idx]);
  // } else {
  //   currentExamples.value.push('');
  // }
  examples.value.push('');
}

function togglePickDefinition() {
  pickDefinitionToggle.value = !pickDefinitionToggle.value;
}

async function addCard() {
  isSubmitting.value = true;
  
  try {
    const baseCard: BaseDefinitionCard = {
      word: word.value,
      definition: {
        definition: definition.value ? definition.value : undefined,
        examples: examples.value
          .concat(currentAddWord.value?.definitions[defIdx.value].examples || [])
          .filter((e) => e), // filter empty value ''
        languages: currentAddWord.value?.definitions[defIdx.value].languages,
        context: currentAddWord.value?.definitions[defIdx.value].context,
        partOfSpeech: partOfSpeech.value
      },
      // @ts-ignore
      referenceWord: toRaw(currentAddWord.value),
      refDefIdx: defIdx.value,
    };
    
    // Use TanStack Query mutation for proper cache management
    const card = await new Promise<DefinitionCard>((resolve, reject) => {
      createDefinitionCardMutation(baseCard, {
        onSuccess: (createdCard) => resolve(createdCard),
        onError: (error) => reject(error)
      });
    });
    
    // Add card to selected decks using mutateAsync for parallel execution
    if (selectedDecks.value.length > 0) {
      const addPromises = selectedDecks.value.map((deck) => {
        return addCardToDeckMutation
          .mutateAsync({ deckId: deck.id, cardId: card.id })
          .catch((error: any) => {
            console.error(`Failed to add card to deck ${deck.name}:`, error);
            throw error; // Re-throw to be caught by Promise.allSettled
          });
      });
      
      // Use Promise.allSettled to handle partial failures gracefully
      const results = await Promise.allSettled(addPromises);
      
      const failed = results.filter(result => result.status === 'rejected').length;
      
      if (failed > 0) {
        console.warn(`${failed} deck addition(s) failed, but proceeding with navigation`);
      }
    }
    modal.hide();
    router.push({ name: 'app-cards-slug-id', params: { id: card.id } });
  } catch (error) {
    console.error('Error adding card:', error);
    // Optional: Show error message to user
  } finally {
    isSubmitting.value = false;
  }
}

function setPartOfSpeech(value: PartOfSpeech) {
  partOfSpeech.value = value;
  partOfSpeechDropdown.hide();
}

</script>

<template>
  <!-- Main modal -->
  <div id="add-word-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <icon-cards stroke-width="2.5" class="inline-block w-5 h-5 me-3"></icon-cards>
            <span>Create card</span>
          </h3>
          <button type="button" @click="store.hideAddCardModal()"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <form class="p-4 md:p-5">
          <div class="grid gap-4 mb-4 grid-cols-2">
            <div class="col-span-2">
              <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Word/Phrase</label>
              <input type="text"
                class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                placeholder="ex: Go down rabbit holes" required v-model="word" :disabled="currentAddWord !== undefined">
            </div>

            <div class="flex">
              <button id="part-of-speech-dropdown-btn"
                class="border border-gray-300 text-xs text-gray-600 bg-gray-50 hover:bg-gray-100 focus:outline-none font-medium rounded-lg px-2.5 py-2.5 text-center inline-flex items-center"
                type="button">
                <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                  :class="stylesForPartOfSpeech(partOfSpeech)">{{ partOfSpeech }}</span>
                <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                  viewBox="0 0 10 6">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 4 4 4-4" />
                </svg>
              </button>

              <!-- Dropdown menu -->
              <div id="part-of-speech-dropdown-menu"
                class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                  <li @click="setPartOfSpeech('noun')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('noun')">
                        noun
                      </span>
                    </div>
                  </li>
                  <li @click="setPartOfSpeech('verb')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('verb')">
                        verb
                      </span>
                    </div>
                  </li>
                  <li @click="setPartOfSpeech('adjective')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('adjective')">
                        adjective
                      </span>
                    </div>
                  </li>
                  <li @click="setPartOfSpeech('phrase')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('phrase')">
                        phrase
                      </span>
                    </div>
                  </li>
                  <li @click="setPartOfSpeech('adverb')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('adverb')">
                        adverb
                      </span>
                    </div>
                  </li>
                  <li @click="setPartOfSpeech('preposition')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('preposition')">
                        preposition
                      </span>
                    </div>
                  </li>
                  <li @click="setPartOfSpeech('conjunction')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('conjunction')">
                        conjunction
                      </span>
                    </div>
                  </li>
                  <li @click="setPartOfSpeech('others')">
                    <div class="block px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                        :class="stylesForPartOfSpeech('others')">
                        others
                      </span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>

            <!-- <div class="col-span-2">
              <label for="description"
                class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Example(s)</label>
              <template v-if="currentExamples.length > 0">
                <template v-for="(_, idx) in currentExamples">
                  <div class="flex mb-2">
                    <textarea id="description" rows="3"
                      class="block p-2.5 w-full text-sm text-gray-600 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                      v-model="currentExamples[idx]"></textarea>
                  </div>
                </template>
</template>
<template v-else>
                <div class="flex mb-2">
                  <textarea id="description" rows="3"
                    class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    v-model="currentExamples[0]"
                    placeholder="Have the courage to go down rabbit holes that are deeply meaningful to you, not just trendy topics."></textarea>
                </div>
              </template>
<button type="button" class="flex items-center" @click="addMoreExample()">
  <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
    viewBox="0 0 24 24">
    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5" />
  </svg>
  <span class="text-xs text-blue-700">Add more examples</span>
</button>
</div> -->
            <div class="col-span-2">
              <label for="description"
                class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Definition</label>
              <div class="flex mb-2">
                <textarea id="description" rows="5"
                  class="block p-2.5 w-full text-sm text-gray-600 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  v-model="definition"
                  placeholder='Metaphorically, "going down the rabbit holes" means delving deeply into a subject or exploring a complex topic or idea in great detail. It implies a willingness to explore the unknown or unconventional paths of inquiry, often leading to unexpected discoveries or insights.'></textarea>
              </div>
              <div v-if="currentAddWord !== undefined">
                <button class="flex items-center" v-if="!pickDefinitionToggle" @click="togglePickDefinition()">
                  <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m9 5 7 7-7 7" />
                  </svg>
                  <span class="text-xs text-blue-700">Pick other definitions</span>
                </button>
                <button class="flex items-center" v-else @click="togglePickDefinition()">
                  <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m19 9-7 7-7-7" />
                  </svg>
                  <span class="text-xs text-blue-700">Pick other definitions</span>
                </button>
                <template v-if="pickDefinitionToggle">
                  <ul class="my-4 space-y-3">
                    <li v-for="(definition, idx) in currentAddWord?.definitions">
                      <div
                        class="flex items-center p-3 text-sm text-gray-600 rounded-lg border-2 hover:bg-gray-50 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white cursor-pointer"
                        :class="{ 'border-blue-600': idx === defIdx, 'border-gray-200': idx !== defIdx }"
                        @click="defIdx = idx">
                        <span class="flex-1">{{ definition.definition }}</span>
                      </div>
                    </li>
                  </ul>
                </template>
              </div>
            </div>
          </div>
          <div class="col-span-2">
            <label for="description" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Your own
              examples
              (optional)</label>
            <template v-if="examples.length > 0">
              <template v-for="(_, idx) in examples">
                <div class="flex mb-2">
                  <textarea id="description" rows="3"
                    class="block p-2.5 w-full text-sm text-gray-600 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    v-model="examples[idx]"></textarea>
                </div>
              </template>
            </template>
            <template v-else>
              <div class="flex mb-2">
                <textarea id="description" rows="3"
                  class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  v-model="examples[0]"
                  placeholder="Have the courage to go down rabbit holes that are deeply meaningful to you, not just trendy topics."></textarea>
              </div>
            </template>
            <button type="button" class="flex items-center" @click="addMoreExample()">
              <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 12h14m-7 7V5" />
              </svg>
              <span class="text-xs text-blue-700">Add more examples</span>
            </button>
            <button type="button" class="mt-1 flex items-center" v-if="!isShowAddDecks" @click="isShowAddDecks = true">
              <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 12h14m-7 7V5" />
              </svg>
              <span class="text-xs text-blue-700">Add to notebooks</span>
            </button>
            <div class="col-span-2 mt-4" v-if="isShowAddDecks">
              <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Notebooks</label>
              <input type="text"
                class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                required v-model="searchDeckText">

              <div class="flex flex-wrap items-center mt-2">
                <div v-for="(deck, idx) in selectedDecks"
                  class="flex items-center bg-gray-50 py-1 px-2 rounded border border-gray-300 text-gray-600 text-sm mr-2 mt-1">
                  <span class="mr-3">{{ deck.name }}</span>
                  <icon-x @click="removeDeck(idx)" class="inline-block w-3 h-3 cursor-pointer"></icon-x>
                </div>
              </div>

              <div class="pb-4">
                <div ref="scrollContainerRef" class="mt-4 max-h-[360px] overflow-y-auto">
                  <div class="pr-4">
                    <div class="flex items-center justify-between mb-2">
                      <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Your notebooks</p>
                      <!-- Background sync indicator -->
                      <div 
                        v-if="isDecksRefreshing && !isDecksLoading"
                        class="flex items-center space-x-2"
                        aria-label="Syncing notebooks"
                      >
                        <div class="w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        <span class="text-xs text-gray-400">Syncing...</span>
                      </div>
                    </div>
                    
                    <!-- Error state -->
                    <div v-if="decksError" class="p-4 text-center">
                      <p class="text-red-500 text-sm mb-2">Error loading notebooks: {{ decksError.message }}</p>
                      <button 
                        @click="() => { searchDeckText = searchDeckText + ' ' }" 
                        class="text-xs px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                      >
                        Retry
                      </button>
                    </div>
                    
                    <!-- Deck list -->
                    <ul v-else class="my-4 space-y-3">
                      <!-- Initial loading state -->
                      <li v-if="isDecksLoading && suggestedDecks.length === 0" class="p-4 text-center">
                        <div class="flex items-center justify-center space-x-2">
                          <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                          <span class="text-sm text-gray-500">Loading notebooks...</span>
                        </div>
                      </li>
                      
                      <!-- Deck items -->
                      <li v-for="(deck, idx) in suggestedDecks" 
                          :key="`${deck.id}-${deck.name}`"
                          ref="deckRefs"
                          class="cursor-pointer p-3 text-sm font-bold text-gray-600 border-2 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                          :class="{
                            'bg-gray-200': idx === deckSelectedIdx,
                            'bg-gray-50': idx !== deckSelectedIdx,
                            'border-blue-600': isInSelectedDecks(deck),
                          }"
                          @click="addSelectedDeck(idx)">
                        <div class="flex items-center ms-3">
                          <span class="">{{ deck.name }}</span>
                        </div>
                        <p class="ms-3 font-normal text-gray-500" v-if="deck.description">{{ deck.description }}
                        </p>
                      </li>
                      
                      <!-- Infinite scroll trigger and load more -->
                      <li v-if="hasNextPage" 
                          ref="loadMoreRef" 
                          class="p-4 text-center">
                        <div v-if="isFetchingNextPage" class="flex items-center justify-center space-x-2">
                          <div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                          <span class="text-sm text-gray-500">Loading more...</span>
                        </div>
                        <button v-else 
                                @click="fetchNextPage()"
                                class="text-sm px-4 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors">
                          <span v-if="searchDeckText.trim()">
                            Load More ({{ totalCount - allDecks.length }} remaining)
                          </span>
                          <span v-else>
                            Load More Notebooks
                          </span>
                        </button>
                      </li>
                      
                      <!-- No results state -->
                      <li v-if="!isDecksLoading && suggestedDecks.length === 0 && searchDeckText.trim()" 
                          class="p-4 text-center">
                        <p class="text-sm text-gray-500">No notebooks found for "{{ searchDeckText }}"</p>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center mt-12">
            <SubmitButton 
              text="Add" 
              :isLoading="isLoading" 
              @click="addCard" 
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
