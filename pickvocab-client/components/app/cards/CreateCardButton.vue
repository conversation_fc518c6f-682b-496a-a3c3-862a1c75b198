<script setup lang="ts">
// @ts-ignore
import IconPlus from '@tabler/icons-vue/dist/esm/icons/IconPlus.mjs';
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
import { Button } from '~/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';

const store = useAppStore();
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button 
        size="sm" 
        class="bg-blue-600 hover:bg-blue-700 text-white px-2 sm:px-3 py-1.5 text-sm"
      >
        <icon-plus stroke-width="1.5" class="w-3 h-3"></icon-plus>
        <span class="hidden sm:inline">Create</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem @click="store.showSearchModal()">
        <icon-search stroke-width="1.5" class="w-4 h-4 mr-2"></icon-search>
        Create from Lookup
      </DropdownMenuItem>
      <DropdownMenuItem @click="store.showAddCardModal()">
        <icon-plus stroke-width="1.5" class="w-4 h-4 mr-2"></icon-plus>
        Create manually
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>