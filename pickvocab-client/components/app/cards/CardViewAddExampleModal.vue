<script setup lang="ts">
// import { IconPencilPlus } from '@tabler/icons-vue';
// @ts-ignore
import IconPencilPlus from '@tabler/icons-vue/dist/esm/icons/IconPencilPlus.mjs';
import type { Modal } from 'flowbite';
import SubmitButton from '~/components/ui/SubmitButton.vue';
import { useUpdateDefinitionCard } from '~/composables/useCardMutations';

const show = defineModel<boolean>('show', { default: false });
const props = defineProps<{ card: DefinitionCard }>();
const examples: Ref<string[]> = ref(['']);

let modal: Modal;

const { mutate: updateDefinitionCard, isPending: isUpdating } = useUpdateDefinitionCard();

// Use mutation loading state instead of manual loading state
const isLoading = computed(() => isUpdating.value);

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $modelEl = document.getElementById('card-view-add-example-modal');
    modal = new Modal($modelEl);
  
    // modal.updateOnShow(async () => {});
    modal.updateOnHide(() => {
      show.value = false;
    });
  });
});

function getUserExamples(card: DefinitionCard): string[] {
  const wordExampes = card.refDefIdx !== undefined ? card.referenceWord?.definitions[card.refDefIdx].examples || [] : [];
  const cardExamples = card.definition.examples || [];

  const userExamples = [];
  for (let i = 0; i < cardExamples.length; i += 1) {
    if (cardExamples[i] !== wordExampes[0]) {
      userExamples.push(cardExamples[i]);
    } else {
      break;
    }
  }
  return userExamples;
}

watch(show, (value) => {
  if (value === true) {
    examples.value = getUserExamples(props.card);
    if (examples.value.length === 0) {
      examples.value = ['']; // auto create new empty example input
    }

    modal.show();
  } else {
    modal.hide();
  }
});

function addMoreExample() {
  examples.value.push('');
}

async function save() {
  try {
    const wordExampes = props.card.refDefIdx !== undefined ? props.card.referenceWord?.definitions[props.card.refDefIdx].examples || [] : [];
    
    // Create updated card with new examples
    const updatedCard: DefinitionCard = {
      ...toRaw(props.card),
      definition: {
        ...props.card.definition,
        examples: [...examples.value.filter(Boolean), ...wordExampes]
      }
    };
    
    await updateDefinitionCard(updatedCard);
    show.value = false;
  } catch (error) {
    console.error('Failed to update card examples:', error);
    // Error handling is done by the mutation, but we could show a toast here
  }
}

</script>

<template>
  <!-- Main modal -->
  <div id="card-view-add-example-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <icon-pencil-plus stroke-width="2.5" class="inline-block w-5 h-5 me-3"></icon-pencil-plus>
            <span>Add examples</span>
          </h3>
          <button type="button" @click="show = false"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <form class="p-4 md:p-5">
          <div class="col-span-2">
            <label for="description" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Your own
              examples
              (optional)</label>
            <template v-if="examples.length > 0">
              <template v-for="(_, idx) in examples">
                <div class="flex mb-2">
                  <textarea id="description" rows="3"
                    class="block p-2.5 w-full text-sm text-gray-600 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    v-model="examples[idx]"></textarea>
                </div>
              </template>
            </template>
            <button type="button" class="flex items-center" @click="addMoreExample()">
              <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 12h14m-7 7V5" />
              </svg>
              <span class="text-xs text-blue-700">Add more examples</span>
            </button>
          </div>
          <div class="flex items-center mt-12">
            <SubmitButton
              :isLoading="isLoading"
              text="Save"
              loading-text="Saving..."
              @click="save()"
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
