<script setup lang="ts">
// import { IconInfoCircleFilled, IconScale, IconDotsVertical } from '@tabler/icons-vue';
// @ts-ignore
import IconInfoCircleFilled from '@tabler/icons-vue/dist/esm/icons/IconInfoCircleFilled.mjs';
// @ts-ignore
import IconScale from '@tabler/icons-vue/dist/esm/icons/IconScale.mjs';
// @ts-ignore
import IconDotsVertical from '@tabler/icons-vue/dist/esm/icons/IconDotsVertical.mjs';
import { FwbTooltip, FwbSelect } from 'flowbite-vue';
import type { DefinitionDetails } from 'pickvocab-dictionary';
import Dropdown from '../utils/Dropdown.vue';
import { stylesForPartOfSpeech } from '~/utils/utils';

const props = defineProps<{
  card: DefinitionCard,
  llmModel?: LLMModel,
  llmIcon?: string,
}>();

const router = useRouter();

const selectedLanguage = ref<string | undefined>('English');

function renderDefinitionForLanguage (definition: Partial<DefinitionDetails>, language: string) {
  return definition.languages![language].word ?
    `${definition.languages![language].word} - ${definition.languages![language].definition}`
    : definition.languages![language].definition;
}

function compareWords(card: DefinitionCard) {
  if (card.refDefIdx === undefined) return;
  router.push({
    name: 'app-ask',
    query: {
      compare: [card.word]
        .concat(card.referenceWord?.definitions[card.refDefIdx].synonyms?.map(s => s.synonym) ?? [])
        .map((w) => slugifyText(w)).join(',')
    }
  })
}
</script>

<template>
  <div>
    <div class="flex items-center py-2 border-b">
      <p class="text-3xl text-gray-700 font-semibold">{{ card.word }}</p>

      <FwbTooltip class="ml-2 hidden sm:block" theme="dark">
        <template #trigger>
          <icon-info-circle-filled
            class="inline-block text-gray-400 w-5 h-5 cursor-pointer hover:text-gray-600"></icon-info-circle-filled>
        </template>
        <template #content>
          <div v-if="card" class="flex items-center px-3 py-2 border font-medium text-sm rounded-lg shadow-sm bg-white">
            <span v-if="llmModel" class="flex items-center">
              <img :src="llmIcon" alt="llm-model-img" class="w-4 h-4">
              <span class="ml-1">{{ llmModel.name }}&nbsp;</span>
            </span>
            <span v-if="card.createdAt">{{ new Date(card.createdAt).toLocaleString() }}</span>
          </div>
        </template>
      </FwbTooltip>

      <div class="ml-auto" v-if="$slots.cardSettings">
        <slot name="cardSettings"></slot>
      </div>
    </div>

    <div class="mt-8">
      <fwb-select
        class="sm:w-48"
        v-model="selectedLanguage"
        :options="Object.keys(card.definition.languages || {}).map((language) => ({
          value: language,
          name: `English - ${language}`
        })).concat({
          value: 'English',
          name: 'English - English'
        })"
        placeholder="Select Language"
      />
    </div>

    <div v-if="card !== undefined">
      <p class="text-base text-gray-600 mt-8" v-if="card.definition.definition">
        <span class="inline-block text-sm border rounded-xl px-2 align-middle"
          :class="stylesForPartOfSpeech(card.definition.partOfSpeech)"
          v-if="card.definition.partOfSpeech !== undefined">
          {{ card.definition.partOfSpeech }}
        </span>
        <span class="ml-1" v-if="!selectedLanguage || selectedLanguage === 'English'">{{ card.definition.definition }}</span>
        <span class="ml-1"
          v-else-if="card.definition.languages && card.definition.languages[selectedLanguage] && card.definition.languages[selectedLanguage].definition">{{
            renderDefinitionForLanguage(card.definition, selectedLanguage) }}</span>
      </p>
      <div class="mt-8"
        v-if="card.definition.context">
        <p class="text-xl text-gray-700 font-semibold">Usage context</p>
        <p class="text-base text-gray-600 mt-2" v-if="!selectedLanguage || selectedLanguage === 'English'">{{
          card.definition.context }}</p>
        <p class="text-base text-gray-600 mt-2"
        v-else-if="card.definition.languages && card.definition.languages[selectedLanguage] && card.definition.languages[selectedLanguage].definition">
          {{ card.definition.languages[selectedLanguage].context }}</p>
      </div>
      <div class="mt-8" v-if="card.definition.examples && card.definition.examples?.length > 0">
        <p class="text-xl text-gray-700 font-semibold">Examples</p>
        <ol class="list-decimal list-inside text-base text-gray-600">
          <li v-for="example in card.definition.examples" class="p-1 mt-2">
            <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ example }}</blockquote>
          </li>
        </ol>
      </div>
      <div class="mt-8"
        v-if="card.referenceWord !== undefined && card.refDefIdx !== undefined && card.referenceWord.definitions[card.refDefIdx].synonyms !== undefined">
        <div class="flex items-center">
            <p class="text-xl text-gray-700 font-semibold">Synonyms</p>
            <Dropdown :offsetDistance="-10" :offsetSkidding="-20">
              <template #trigger>
                <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
                  <icon-dots-vertical class="w-4 h-4"></icon-dots-vertical>
                </div>
              </template>
              <template #body>
                <div
                  class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600">
                  <ul class="py-1" role="none">
                    <li>
                      <div @click="compareWords(card)"
                        class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 cursor-pointer"
                        role="menuitem">
                        <icon-scale class="inline-block w-4 h-4"></icon-scale>
                        <span class="ml-2">Compare words</span>
                      </div>
                    </li>
                  </ul>
                </div>
              </template>
            </Dropdown>
          </div>
        <ol class="text-base text-gray-600 mt-4">
          <li v-for="synonym in card.referenceWord.definitions[card.refDefIdx].synonyms" class="mt-4">
            <NuxtLink :to="{ name: 'app-dictionary', query: { word: synonym.synonym } }"
              rel="nofollow"
              class="font-semibold underline text-blue-800">{{ synonym.synonym }}</NuxtLink>
            <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ synonym.example }}</blockquote>
          </li>
        </ol>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
