<script setup lang="ts">
// import { IconCards, IconPlus } from '@tabler/icons-vue';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
// @ts-ignore
import IconPlus from '@tabler/icons-vue/dist/esm/icons/IconPlus.mjs';
import { FwbPagination, FwbButton } from 'flowbite-vue';
import CreateCardButton from './CreateCardButton.vue';
// @ts-ignore
// import VueSimpleContextMenu from 'vue-simple-context-menu'; // REMOVE
import DefinitionCardEntry from '~/components/app/cards/DefinitionCardEntry.vue';
import ContextCardEntry from './ContextCardEntry.vue';
import Spinner from '~/components/app/utils/Spinner.vue';
import { CardType, type CardId } from '~/utils/card';
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'; // Added onBeforeUnmount, watch, computed
import { useRoute, useRouter } from 'vue-router'; // Added useRoute, useRouter
import { useAppStore } from '~/stores/app'; // Added useAppStore
import { useCardsQuery, useInvalidateCardsQuery } from '~/composables/useCardsQuery';
import { useInvalidateCardQuery } from '~/composables/useCardQuery';
import { useDeleteCard } from '~/composables/useCardMutations';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs'; // ADD
import { isMobile } from 'is-mobile'; // ADD

const props = withDefaults(defineProps<{
  isDemoPage?: boolean
}>(), { isDemoPage: false });

const router = useRouter();
const route = useRoute();
const store = useAppStore();

const currentPage = ref(1);

// Replace fetchCards with query (using v5 API)
const { data: queryData, isPending, isFetching, error, refetch: refetchCards } = useCardsQuery(currentPage, props.isDemoPage)
const { invalidateCardsPage } = useInvalidateCardsQuery()
const { setCardData } = useInvalidateCardQuery()
const { mutate: deleteCardMutation } = useDeleteCard()

// Computed properties for template
const renderedCards = computed(() => queryData.value?.result || [])
const totalPages = computed(() => queryData.value?.totalPages || 0)

// Cache population: populate individual card cache when list data loads
watch(queryData, (newData) => {
  if (newData?.result) {
    newData.result.forEach(card => {
      setCardData(typeof card.id === 'string' ? parseInt(card.id) : card.id, card)
    })
  }
}, { immediate: true })

// Watch for route changes to trigger background fetch when navigating to this page
watch(() => route.path, (newPath, oldPath) => {
  // Check if we're navigating TO this cards page (not away from it)
  const isCardsPage = newPath?.includes('/cards') && !newPath?.includes('/cards/');
  const wasOnCardsPage = oldPath?.includes('/cards') && !oldPath?.includes('/cards/');
  
  if (isCardsPage && !wasOnCardsPage) {
    // Navigated to cards list from elsewhere, trigger background refresh
    refetchCards()
  }
}, { immediate: false })

// REQUIRED: Always refetch on mount to ensure fresh data
onMounted(() => {
  refetchCards() // Ensure fresh data when component mounts
})

// --- NEW LONG PRESS LOGIC ---
const longPressedCardId = ref<CardId | null>(null);
let longPressTimeout: ReturnType<typeof setTimeout> | null = null;

const handleCardTouchStart = (cardId: CardId) => {
  longPressTimeout = setTimeout(() => {
    longPressedCardId.value = cardId;
  }, 600); // 600ms for long press
};

const handleCardTouchEnd = () => {
  if (longPressTimeout) {
    clearTimeout(longPressTimeout);
    longPressTimeout = null;
  }
  // Do not reset longPressedCardId.value here, let global touch or click handle it
};

const handleGlobalCardTouch = (e: TouchEvent) => {
  // If the tap is outside any card item that might have an active remove button, hide it.
  if (!(e.target as HTMLElement).closest('.card-item-interactive-area')) { // card-item-interactive-area is the div containing the card and its remove button
    if (longPressedCardId.value && !(e.target as HTMLElement).closest('.remove-button-class')) { // ensure not clicking the remove button itself
        longPressedCardId.value = null;
    }
  }
};
// --- END NEW LONG PRESS LOGIC ---

onMounted(() => {
  watch(() => route.query.page, async (value) => {
    if (Array.isArray(value)) throw new Error('Unexpected');
    currentPage.value = value ? Number(value) : 1;
  }, { immediate: true });

  watch(currentPage, (newPage) => {
    if (newPage === 1) {
      router.replace({ query: { page: undefined } });
    } else {
      router.push({ query: { page: newPage } });
    }
    
    // Note: Removed manual refetch - TanStack Query automatically refetches when currentPage changes
  });

  // Note: Removed mount refetch - TanStack Query handles initial data fetching automatically
  
  document.addEventListener('touchstart', handleGlobalCardTouch); // ADD
});

onBeforeUnmount(() => { // ADD
  document.removeEventListener('touchstart', handleGlobalCardTouch);
  if (longPressTimeout) {
    clearTimeout(longPressTimeout);
  }
});


// REMOVE OLD CONTEXT MENU LOGIC
// let timer: any;
// let touchDuration = 500;
// function cardTouchStart(event: any, card: Card) {
//   timer = setTimeout(() => openCardContextMenu({ pageX: event.targetTouches[0].pageX, pageY: event.targetTouches[0].pageY }, card), touchDuration);
// }
// function cardTouchEnd() {
//   clearTimeout(timer);
// }
// const cardContextMenu: Ref<any> = ref();
// const cardContextMenuOptions = [
//   {
//     name: 'Delete'
//   }
// ]
// async function cardOptionClick(event: any) {
//   const card: DefinitionCard = event.item;
//   const option: { name: string } = event.option;
//   if (option.name === 'Delete') {
//     await store.deleteGenericCard(card.id);
//     await fetchCards();
//   }
// }
// function openCardContextMenu(event: any, item: any) {
//   cardContextMenu.value.showMenu(event, item);
// }
// END REMOVE OLD CONTEXT MENU LOGIC


// --- NEW REMOVE CARD LOGIC ---
function handleRemoveCard(cardId: CardId) {
  if (window.confirm('Are you sure you want to remove this card?')) {
    try {
      deleteCardMutation(cardId); // Use optimistic delete mutation
      longPressedCardId.value = null; // Reset after action
    } catch (e) {
      console.error('Failed to remove card:', e);
      // TODO: User-facing error (e.g., show a toast notification)
      alert('Failed to remove card. See console for details.'); // Simple alert for now
    }
  } else {
    // Optional: If user cancels, ensure button is hidden on mobile if it was shown by long press
    if (longPressedCardId.value === cardId) {
      longPressedCardId.value = null;
    }
  }
}
// --- END NEW REMOVE CARD LOGIC ---

function goToCard(cardId: CardId) {
  // If a remove button was shown via long press, hide it before navigating
  if (longPressedCardId.value) {
    longPressedCardId.value = null;
    // Add a small delay to ensure the state updates before navigation, preventing click bleed-through
    setTimeout(() => {
      if (props.isDemoPage) {
        router.push({ name: 'demo-cards-slug-id', params: { id: cardId } });
      } else {
        router.push({ name: 'app-cards-slug-id', params: { id: cardId } });
      }
    }, 50); 
    return;
  }

  if (props.isDemoPage) {
    router.push({ name: 'demo-cards-slug-id', params: { id: cardId } });
  } else {
    router.push({ name: 'app-cards-slug-id', params: { id: cardId } });
  }
}

</script>

<template>
  <div class="w-full h-full box-border bg-gray-50">
    <!-- Background fetch indicator (v5: show when fetching but not initial loading) -->
    <div 
      v-if="isFetching && !isPending"
      class="fixed top-20 right-6 z-50 transition-opacity duration-200 flex items-center space-x-2"
      aria-label="Updating data in background"
    >
      <Spinner :show-message="false" :size="'4'" class="w-4 h-4 text-gray-400" />
      <span class="text-xs text-gray-400 font-normal">Syncing...</span>
    </div>

    <div v-if="renderedCards.length > 0">
      <div class="sm:ml-64 mt-14 bg-gray-50">
        <div class="pt-10 px-4 lg:px-32 xl:px-44 2xl:px-64">
          <div class="mb-8 flex items-center justify-between px-2 sm:px-0">
            <div>
              <h1 class="text-2xl font-bold text-gray-800">Cards</h1>
            </div>
            <CreateCardButton />
          </div>
          <!-- Removed Dropdown -->
          <div 
            v-for="card in renderedCards" 
            :key="card.id" 
            class="relative group card-item-interactive-area mb-4"
            @touchstart="handleCardTouchStart(card.id)" 
            @touchend="handleCardTouchEnd"
            @touchmove="handleCardTouchEnd" 
          >
            <DefinitionCardEntry 
              v-if="card.cardType === CardType.DefinitionCard" 
              :card="card" 
              @click="goToCard(card.id)"
              class="cursor-pointer"
            />
            <ContextCardEntry 
              v-if="card.cardType === CardType.ContextCard" 
              :card="card" 
              @click="goToCard(card.id)"
              class="cursor-pointer"
            />
            <button
              v-if="longPressedCardId === card.id || !isMobile()"
              @click.stop="handleRemoveCard(card.id)"
              class="remove-button-class absolute top-3 right-3 z-20 <!-- Increased z-index -->
                     opacity-100 sm:opacity-0 sm:group-hover:opacity-100
                     transition-opacity duration-200 ease-in-out 
                     bg-white/90 hover:bg-red-500 text-red-600 hover:text-white 
                     rounded-full p-1.5 shadow-md hover:shadow-lg"
              aria-label="Remove card"
            >
              <IconX class="w-4 h-4" />
            </button>
          </div>
        </div>

        <fwb-pagination class="mt-4 py-8 flex justify-center items-center" v-model="currentPage"
          :total-pages="totalPages"></fwb-pagination>

        <!-- REMOVE vue-simple-context-menu -->
        <!-- <vue-simple-context-menu element-id="cardContextMenu" :options="cardContextMenuOptions" ref="cardContextMenu"
          @option-clicked="cardOptionClick">
        </vue-simple-context-menu> -->
      </div>
    </div>
    
    <!-- Update error handling -->
    <div v-else-if="error" class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <p class="text-red-500">Error loading cards: {{ error.message }}</p>
      <button @click="invalidateCardsPage(currentPage, props.isDemoPage)" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
        Retry
      </button>
    </div>

    <!-- Update loading state check (v5: use isPending for initial loading) -->
    <div v-else-if="!isPending" class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <icon-cards stroke-width="1.5" class="w-8 h-8 text-gray-500"></icon-cards>
      <p class="mt-4 text-gray-500">Create your first card</p>
      <div class="flex mt-4 space-x-2">
        <fwb-button @click="store.showAddCardModal()" class="">
          <div class="flex items-center">
            <icon-plus stroke-width="1.5" class="w-4 h-4"></icon-plus>
            <span class="ml-1">Create new card</span>
          </div>
        </fwb-button>
        <fwb-button @click="store.showSearchModal()" class="bg-gray-200 hover:bg-gray-300 text-gray-700">From a lookup
          word</fwb-button>
      </div>
    </div>
    
    <div v-else class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full items-center justify-center">
      <Spinner />
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles if needed, but Tailwind should cover most */
.card-item-interactive-area {
  /* Ensure this class is on the div that wraps card and button */
  /* Potentially add transition for smoother appearance if needed, though button itself has transition */
  position: relative;
}
</style>