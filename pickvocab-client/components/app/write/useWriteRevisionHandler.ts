// pickvocab-client/composables/useWriteRevisionHandler.ts
import { ref, computed, type Ref, type ComputedRef, watch } from 'vue'; // Added computed and ComputedRef, and watch
import { CardType, type Card } from '~/utils/card'; // Simplified import
import { useToneStore } from '~/stores/toneStore';

// Import functions from extracted utils module
import { formatCardForPrompt, type FormattedVocabulary } from '~/components/app/write/revisionUtils';

// Import prompt functions from original files
import { useMyVocabularyMarkdownPrompt } from '~/components/app/write/useMyVocabularyMarkdownPrompt';
import { reviseTextSimpleMarkdownPrompt } from '~/components/app/write/reviseTextSimpleMarkdownPrompt';
import { grammarCheckMarkdownPrompt } from '~/components/app/write/grammarCheckMarkdownPrompt';
import { highlightVocabularyMarkdownPrompt, type RevisionHighlightInput } from '~/components/app/write/highlightVocabularyMarkdownPrompt';

// Import the new API abstraction
import { useRevisionApi, type RevisionData, type HighlightedRevisionItem } from '~/components/app/write/useRevisionApi';

export function useWriteRevisionHandler(
  userText: Ref<string>,
  useVocabulary: Ref<boolean>,
  grammarCheck: Ref<boolean>
) {
  const toneStore = useToneStore();
  const isRevising = ref(false); // Tracks overall process (similarity search + LLM)
  const isGeneratingRevision = ref(false); // Tracks only the LLM call part
  const isLoadingVocabularyCards = ref(false); // Tracks loading state for vocabulary cards
  const revisionApi = useRevisionApi(); // Use the new API abstraction

  // Track the current revision trigger type
  const currentTriggerType = ref<'Revise' | 'Refresh' | 'GrammarCheck'>('Revise');

  // --- State for Results ---
  const allRevisionsResult = ref<RevisionData[]>([]); // Holds all revisions from LLM
  const currentRevisionIndex = ref(0); // Index of the currently displayed revision
  const cardCacheById = ref(new Map<string, Card>()); // Store the mapping of card ID to Card
  const revisionError = ref<string | null>(null);
  const vocabularyWasUsedForLastRevision = ref(false); // Track if vocab was used for the current results
  const currentHistoryEntryId = ref<number | null>(null); // ID of the currently saved history entry

  // --- Computed properties for the current revision's data ---
  const currentRevisionData: ComputedRef<RevisionData | undefined> = computed(() => {
    if (allRevisionsResult.value.length > 0 && currentRevisionIndex.value >= 0 && currentRevisionIndex.value < allRevisionsResult.value.length) {
      return allRevisionsResult.value[currentRevisionIndex.value];
    }
    return undefined;
  });

  const revisedTextResult: ComputedRef<string> = computed(() => currentRevisionData.value?.revision || '');
  const originalRevisedTextResult: ComputedRef<string> = computed(() => {
    return currentRevisionData.value?.originalRevision || currentRevisionData.value?.revision || '';
  });
  const llmFeedbackResult: ComputedRef<string> = computed(() => currentRevisionData.value?.feedback || '');
  const learningFocusResult: ComputedRef<string[]> = computed(() => currentRevisionData.value?.learning_focus || []);
  const realCardIdsResult: ComputedRef<string[]> = computed(() => currentRevisionData.value?.real_card_ids || []);

  // Computed property to get the actual Card objects for the current revision
  const usedVocabularyCardsResult: ComputedRef<Card[]> = computed(() => {
    const ids = realCardIdsResult.value;
    const map = cardCacheById.value;

    if (!ids || ids.length === 0 || map.size === 0) {
      return [];
    }

    const cards: Card[] = [];
    for (const cardId of ids) {
      const card = map.get(String(cardId));
      if (card) {
        cards.push(card);
      } else {
        console.warn(`Card ID ${cardId} not found in cardCacheById.`);
      }
    }
    return cards;
  });

  // --- Combined Highlighting and History Update Helpers ---

  /**
   * Prepares input data for the highlighting process by extracting vocabulary words from revisions
   */
  function prepareHighlightingInput(revisions: RevisionData[]): RevisionHighlightInput[] {
    const revisionsToHighlight: RevisionHighlightInput[] = [];

    revisions.forEach((revisionData, index) => {
      // Only use real_card_ids for lookups
      const cardIds = revisionData.real_card_ids;

      if (Array.isArray(cardIds) && cardIds.length > 0) {
        const vocabularyWords = cardIds.map(cardId => {
          const card = cardCacheById.value.get(String(cardId));

          if (!card) {
            console.warn(`Card not found in cardCacheById for ID: ${cardId} in revision index ${index}`);
            return undefined;
          }
          // Extract the primary word/phrase based on card type
          return card.cardType === CardType.DefinitionCard ? card.word : card.wordInContext?.word;
        }).filter((word): word is string => word !== undefined && word.trim() !== '');

        if (vocabularyWords.length > 0) {
          revisionsToHighlight.push({
            originalIndex: index, // The index in the allRevisionsResult array
            revisionText: revisionData.revision,
            vocabularyList: vocabularyWords
          });
        }
      }
    });

    return revisionsToHighlight;
  }

  /**
   * Generates and parses highlighted revisions using LLM
   */
  async function generateAndParseHighlights(prompt: string): Promise<HighlightedRevisionItem[]> {
    try {
      // Use the generateHighlights method from revisionApi with the generated prompt
      const highlightedRevisionsParsed = await revisionApi.generateHighlights(prompt);

      if (highlightedRevisionsParsed.length === 0) {
        console.warn("LLM returned response, but no valid highlighted revisions could be parsed.");
        return []; // Return empty array if nothing valid was parsed
      }

      return highlightedRevisionsParsed;
    } catch (error) {
      console.error('Error during combined highlighting LLM call or parsing:', error);
      throw error; // Re-throw to be handled by caller
    }
  }

  /**
   * Applies highlight results to state, with safety checks to prevent text corruption
   */
  function applyHighlightsToState(
    highlightedItems: HighlightedRevisionItem[],
    revisionsToHighlight: RevisionHighlightInput[]
  ): boolean {
    try {
      // Create a deep copy to modify
      const updatedRevisionsArray = JSON.parse(JSON.stringify(allRevisionsResult.value));

      highlightedItems.forEach((highlightedItem, highlightIndex) => {
        // Map the highlight index to the corresponding revision in revisionsToHighlight
        if (highlightIndex < revisionsToHighlight.length) {
          const targetRevision = revisionsToHighlight[highlightIndex];
          const targetIndex = targetRevision.originalIndex;
          // Find the revision in the *copied* array using the original index
          const revisionToUpdate = updatedRevisionsArray[targetIndex];

          if (revisionToUpdate) {
            // Basic check to prevent major accidental rewrites
            const originalText = revisionToUpdate.revision;
            const highlightedText = highlightedItem.highlightedText;
            const originalWords = targetRevision.vocabularyList;
            const lengthDifference = Math.abs(highlightedText.length - originalText.length);
            const allowedDifference = originalWords.reduce((sum: number, word: string) => sum + word.length, 0) + (originalWords.length * 4) + 50; // Estimate length increase

            if (lengthDifference <= allowedDifference) {
               // Only update the 'revision' property with the highlighted text.
               revisionToUpdate.revision = highlightedText;
            } else {
               console.warn(`Highlighting for revision index ${targetIndex} resulted in significant text change. Skipping update for this revision.`);
            }
          } else {
            console.warn(`Could not find revision with original index ${targetIndex} in the copied array during highlight update.`);
          }
        } else {
          console.warn(`Highlight index ${highlightIndex} exceeds revisionsToHighlight array length ${revisionsToHighlight.length}`);
        }
      });

      // Assign the modified copy back to the original ref
      allRevisionsResult.value = updatedRevisionsArray;
      return true; // Successfully applied
    } catch (e) {
      console.error("Error updating local state with highlighted revisions:", e);
      return false; // Failed to apply
    }
  }

  /**
   * Updates the history entry with the newest highlighted revisions
   */
  async function updateHistoryWithHighlights(): Promise<boolean> {
    if (currentHistoryEntryId.value === null) {
      console.warn("Cannot PATCH history: currentHistoryEntryId is null.");
      return false;
    }

    try {
      // Use the updateHistory method from revisionApi
      await revisionApi.updateHistory(currentHistoryEntryId.value, { revisions: allRevisionsResult.value });
      return true; // Successfully updated
    } catch (error) {
      console.error(`Error PATCHing revision history for ID ${currentHistoryEntryId.value}:`, error);
      return false; // Failed to update
    }
  }

  /**
   * Orchestrates the process of highlighting vocabulary words in revisions and updating history
   */
  async function _fetchAndApplyCombinedHighlighting() {
    // 1. Prepare input data for highlighting
    const currentAllRevisions = allRevisionsResult.value; // Use a stable reference
    const revisionsToHighlight = prepareHighlightingInput(currentAllRevisions);

    if (revisionsToHighlight.length === 0) {
      return; // Nothing to highlight
    }

    // 2. Generate prompt
    const prompt = highlightVocabularyMarkdownPrompt(revisionsToHighlight);
    if (!prompt) {
      console.error("Failed to generate combined highlighting prompt.");
      return;
    }

    try {
      // 3. Generate and parse highlights
      const highlightedRevisionsParsed = await generateAndParseHighlights(prompt);

      if (highlightedRevisionsParsed.length === 0) {
        return; // Nothing to apply
      }

      if (highlightedRevisionsParsed.length !== revisionsToHighlight.length) {
        console.warn(`Highlighting response count mismatch. Expected ${revisionsToHighlight.length}, got ${highlightedRevisionsParsed.length}. Proceeding with matched items.`);
      }

      // 4. Apply highlights to local state
      const appliedSuccessfully = applyHighlightsToState(highlightedRevisionsParsed, revisionsToHighlight);

      // 5. Update history if local state was updated successfully
      if (appliedSuccessfully) {
        await updateHistoryWithHighlights();
      }
    } catch (error) {
      console.error('Error in highlighting process:', error);
      // Error already logged in the individual functions
    }
  }

  // --- Save revision history to API ---
  async function saveRevisionHistory(triggerType: 'Revise' | 'Refresh' | 'GrammarCheck') {
    try {
      if (allRevisionsResult.value.length === 0) {
        console.warn('No revisions to save');
        return;
      }

      // Extract all unique card IDs from all revisions
      const cardIds = new Set<string>();

      // Use directly available real_card_ids from each revision
      const processedRevisions = allRevisionsResult.value.map(revision => {
        // If this revision has real card IDs, add them to the overall set
        if (Array.isArray(revision.real_card_ids)) {
          revision.real_card_ids.forEach(id => cardIds.add(String(id)));
        }

        // Return the revision with consistent structure
        return {
          ...revision,
          learning_focus: revision.learning_focus || [],
          user_vocabularies_used: revision.user_vocabularies_used || [],
          real_card_ids: revision.real_card_ids || []
        };
      });

      const historyEntry = {
        triggerType,
        originalText: userText.value,
        useMyVocabulary: useVocabulary.value,
        selectedTone: toneStore.selectedTone ? JSON.stringify(toneStore.selectedTone) : '',
        revisions: processedRevisions
      };

      // Use the saveHistory method from revisionApi
      const result = await revisionApi.saveHistory(historyEntry, Array.from(cardIds));
      return result;
    } catch (error) {
      console.error('Error saving revision history:', error);
      // Don't throw the error up - we don't want to interrupt the UX flow if history saving fails
    }
  }

  // --- Helper for common operations in the revision process ---
  async function generateAndProcessRevisions(prompt: string, withVocabulary: boolean, indexToIdMap?: Map<string, string>) {
    try {
      // Set flag indicating if vocabulary was used for this revision attempt
      vocabularyWasUsedForLastRevision.value = withVocabulary;
      // Call the LLM function to generate revisions with the index-to-ID map
      await callLLMAndParseResponse(prompt, indexToIdMap);
    } catch (error) {
      console.error('Error during revision generation:', error);
      revisionError.value = `Error generating revision: ${error instanceof Error ? error.message : String(error)}`;
      // Ensure results are cleared if generation fails
      allRevisionsResult.value = [];
      currentRevisionIndex.value = 0;
    } finally {
      // Ensure overall loading state is always reset
      isRevising.value = false;
    }
  }

  // Helper function to process raw LLM revisions
  function processLLMRevisions(
    rawRevisions: RevisionData[],
    indexToIdMap?: Map<string, string>
  ): RevisionData[] {
    // Calculate real_card_ids if needed
    return rawRevisions.map(rev => {
      const userVocabUsed = rev.user_vocabularies_used || [];
      const actualCardIds: string[] = [];

      // Calculate real_card_ids from index references using indexToIdMap
      if (Array.isArray(userVocabUsed) && userVocabUsed.length > 0 && indexToIdMap) {
        userVocabUsed.forEach(indexRef => {
          const cardId = indexToIdMap.get(String(indexRef));
          if (cardId) {
            actualCardIds.push(cardId);
          } else {
            console.warn(`LLM indexRef: ${indexRef} not found in indexToIdMap.`);
          }
        });
      }

      return {
        ...rev,
        learning_focus: rev.learning_focus || [],
        user_vocabularies_used: userVocabUsed,
        real_card_ids: actualCardIds // Add the calculated real_card_ids
      };
    });
  }

  // Helper function to save the initial revision history entry
  async function saveInitialRevisionHistory(): Promise<void> {
    try {
      // Use the currentTriggerType which is set by initiateRevision or refreshRevision
      const savedEntry = await saveRevisionHistory(currentTriggerType.value);
      if (savedEntry && savedEntry.id) {
        currentHistoryEntryId.value = savedEntry.id;
      } else {
        console.warn("saveRevisionHistory did not return a valid entry or ID.");
      }
    } catch (saveError) {
      console.error("Failed to save initial revision history:", saveError);
      // Continue processing even if initial save fails, but log it.
    }
  }

  // --- LLM Call and Parsing Logic (Refactored) ---
  async function callLLMAndParseResponse(prompt: string, indexToIdMap?: Map<string, string>) {
    // Use isGeneratingRevision for loading state
    isGeneratingRevision.value = true;
    revisionError.value = null; // Clear previous errors

    // Always clear results and reset index for a new LLM call
    allRevisionsResult.value = []; // Clear previous revisions
    currentRevisionIndex.value = 0; // Reset index
    currentHistoryEntryId.value = null; // Reset history ID for new revision cycle
    // Don't reset vocabularyWasUsedForLastRevision here, it's set by the caller
    try {
      // Step 1: Call LLM API
      const revisions = await revisionApi.generateRevision(prompt);

      if (revisions.length === 0) {
        // Handle empty/invalid response
        console.error('LLM response parsed, but no valid revisions found.');
        revisionError.value = 'The language model response was invalid or empty.';
      } else {
        // Step 2: Process valid response
        const validRevisions = processLLMRevisions(revisions, indexToIdMap);

        // Step 3: Update state with initial (unhighlighted) results
        const revisionsWithOriginal = validRevisions.map(rev => ({
          ...rev,
          originalRevision: rev.revision
        }));
        allRevisionsResult.value = revisionsWithOriginal; // Store initial, unhighlighted results with original text

        // Step 4: Save initial history entry (fire-and-forget error handling inside)
        await saveInitialRevisionHistory();

        // Step 5: Conditionally trigger background highlighting
        if (vocabularyWasUsedForLastRevision.value) {
          // No await - run in background. It will handle its own errors and PATCH history.
          _fetchAndApplyCombinedHighlighting();
        }
      }
    } catch (err) {
      console.error('Error during LLM interaction:', err);
      revisionError.value = 'Error communicating with LLM: ' + (err instanceof Error ? err.message : String(err));
      // Clear results on error
      allRevisionsResult.value = [];
      currentRevisionIndex.value = 0;
    } finally {
      // Reset the specific loading state used for this call
      isGeneratingRevision.value = false; // Reset the specific loading state
      // Note: isRevising (overall state) is handled by the caller (initiateRevision or poll)
    }
  }

  // --- Revision with similarity search ---
  async function initiateRevisionWithSearch() {
    isRevising.value = true;
    try {
      // Get selected tone details from the store
      const selectedToneObj = toneStore.selectedToneDetails;
      const toneName = selectedToneObj?.name || 'Default';
      const toneDescription = selectedToneObj?.description || 'Default description';
      const selectedToneStyleDescription = `${toneName} - ${toneDescription}`;

      // Start similarity search
      const searchResult = await revisionApi.startSimilaritySearch(userText.value, { limit: 10 });

      if (!searchResult || !searchResult.taskId) {
        console.error('Failed to start similarity search or no task ID received.');
        revisionError.value = 'Failed to initiate similarity search.';
        isRevising.value = false;
        return;
      }

      // Poll for similarity search results
      const fetchedCards = await revisionApi.pollSimilarityResults(searchResult.taskId);

      // Store cards in the cache by ID only, and create a temporary index-to-ID map
      cardCacheById.value.clear();
      const indexToIdMap = new Map<string, string>();
      for (const [idx, card] of fetchedCards.entries()) {
        if (card && card.id) {
          const cardIdStr = String(card.id);
          cardCacheById.value.set(cardIdStr, card);
          indexToIdMap.set(idx.toString(), cardIdStr);
        }
      }

      // Format cards for the prompt
      const userVocabularies: FormattedVocabulary[] = Array.isArray(fetchedCards)
        ? fetchedCards.map((card, idx) => formatCardForPrompt(card, idx))
        : [];

      // Generate prompt with vocabulary
      const prompt = useMyVocabularyMarkdownPrompt(selectedToneStyleDescription, userText.value, userVocabularies);

      // Generate and process revisions with the index-to-ID map
      await generateAndProcessRevisions(prompt, true, indexToIdMap);
    } catch (error) {
      console.error('Error during revision with search:', error);
      revisionError.value = `Error starting revision: ${error instanceof Error ? error.message : String(error)}`;
      isRevising.value = false;
    }
  }

  // --- Direct revision without similarity search ---
  async function initiateRevisionDirectly() {
    isRevising.value = true;
    try {
      // Construct tone description
      const selectedToneObj = toneStore.selectedToneDetails;
      const toneName = selectedToneObj?.name || 'Default';
      const toneDescription = selectedToneObj?.description || 'Default description';
      const selectedToneStyleDescription = `${toneName} - ${toneDescription}`;

      // Generate prompt using the simple revision prompt function
      const prompt = reviseTextSimpleMarkdownPrompt(selectedToneStyleDescription, userText.value);

      // Generate and process revisions (without vocabulary)
      await generateAndProcessRevisions(prompt, false, undefined);
    } catch (error) {
      console.error('Error during direct revision:', error);
      revisionError.value = `Error starting revision: ${error instanceof Error ? error.message : String(error)}`;
      isRevising.value = false;
    }
  }

  // --- Grammar Check Function ---
  async function initiateGrammarCheck() {
    isRevising.value = true;
    try {
      // Generate prompt using the grammar check prompt function
      const prompt = grammarCheckMarkdownPrompt(userText.value);

      // Generate and process revisions (without vocabulary)
      await generateAndProcessRevisions(prompt, false, undefined);
    } catch (error) {
      console.error('Error during grammar check:', error);
      revisionError.value = `Error during grammar check: ${error instanceof Error ? error.message : String(error)}`;
      isRevising.value = false;
    }
  }

  // --- Main Function to Trigger Revision ---
  async function initiateRevision() {
    if (isRevising.value || isGeneratingRevision.value) return; // Prevent concurrent runs
    revisionError.value = null; // Clear previous errors on new attempt
    vocabularyWasUsedForLastRevision.value = false; // Reset flag on new attempt

    // Check if grammar check is enabled first
    if (grammarCheck.value) {
      currentTriggerType.value = 'GrammarCheck';
      await initiateGrammarCheck();
      return;
    }

    // Set trigger type to 'Revise'
    currentTriggerType.value = 'Revise';

    // Choose the appropriate revision path based on useVocabulary setting
    if (useVocabulary.value) {
      await initiateRevisionWithSearch();
    } else {
      await initiateRevisionDirectly();
    }
  }

  // Function to refresh revisions (different trigger type than initial revision)
  async function refreshRevision() {
    // Set trigger type to 'Refresh' before initiating revision
    currentTriggerType.value = 'Refresh';

    // Re-use the initiateRevision function which now handles the different paths
    await initiateRevision();
  }

  // --- Navigation Functions ---
  function nextRevision() {
    if (currentRevisionIndex.value < allRevisionsResult.value.length - 1) {
      currentRevisionIndex.value++;
    }
  }

  function previousRevision() {
    if (currentRevisionIndex.value > 0) {
      currentRevisionIndex.value--;
    }
  }

  // --- Function to Restore from History ---
  function restoreFromHistory(historyEntry: any) {
    // Clear any current results and errors
    revisionError.value = null;

    // Set loading state
    isRevising.value = true;

    // Set text and preferences
    userText.value = historyEntry.originalText;
    useVocabulary.value = historyEntry.useMyVocabulary;
    grammarCheck.value = historyEntry.triggerType === 'GrammarCheck';
    // Restore selected tone from history (parse from string if possible)
    if (historyEntry.selectedTone) {
      try {
        const parsed = typeof historyEntry.selectedTone === 'string'
          ? JSON.parse(historyEntry.selectedTone)
          : historyEntry.selectedTone;
        toneStore.selectTone(parsed);
      } catch {
        // Fallback: if not JSON, treat as legacy string (predefined tone name)
        toneStore.selectTone(
          historyEntry.selectedTone
            ? { type: 'predefined', identifier: historyEntry.selectedTone }
            : null
        );
      }
    }

    // Set the revisions array
    allRevisionsResult.value = historyEntry.revisions || [];
    currentRevisionIndex.value = 0; // Start at the first revision

    // Flag that vocabulary was used if the history entry used it
    vocabularyWasUsedForLastRevision.value = historyEntry.useMyVocabulary;

    // Set trigger type to match history
    currentTriggerType.value = historyEntry.triggerType || 'Revise';

    // Check if we need to fetch cards for the current revision
    const currentRevision = allRevisionsResult.value[0]; // Start at the first revision
    if (currentRevision && currentRevision.real_card_ids && currentRevision.real_card_ids.length > 0) {
      // We have real card IDs, need to fetch the corresponding cards
      fetchCardsForRevision(currentRevision.real_card_ids);
    }

    // Turn off the main loading indicator since we only need to show loading for vocabulary cards
    isRevising.value = false;
  }

  // Function to fetch cards by IDs
  async function fetchCardsForRevision(cardIds: string[]) {
    if (!cardIds || cardIds.length === 0) {
      isLoadingVocabularyCards.value = false;
      return;
    }

    isLoadingVocabularyCards.value = true;

    try {
      // Split cardIds into cached and missing
      const missingIds: string[] = [];
      for (const id of cardIds) {
        if (!cardCacheById.value.has(String(id))) {
          missingIds.push(String(id));
        }
      }

      if (missingIds.length > 0) {
        // Only fetch cards that are not already cached
        const fetchedCards = await revisionApi.fetchCardsByIds(missingIds);

        // Merge new cards into the map, preserving existing entries
        for (const card of fetchedCards) {
          cardCacheById.value.set(String(card.id), card);
        }
      }
    } catch (error) {
      console.error('Error fetching cards for revision history:', error);
      revisionError.value = 'Failed to load vocabulary cards for this revision.';
    } finally {
      isLoadingVocabularyCards.value = false;
    }
  }

  // Watch for changes in the current revision index and fetch cards if needed
  watch(currentRevisionIndex, (newIndex) => {
    if (allRevisionsResult.value.length === 0) return;

    const newRevision = allRevisionsResult.value[newIndex];
    if (newRevision && newRevision.real_card_ids && newRevision.real_card_ids.length > 0) {
      // We're only loading vocabulary cards, not the whole revision
      fetchCardsForRevision(newRevision.real_card_ids);
    }
  });

  // Return reactive state and the functions
  return {
    isRevising,
    isGeneratingRevision, // For initial LLM call
    isLoadingVocabularyCards, // For vocabulary cards loading
    initiateRevision,
    refreshRevision,      // Expose the refresh function for UI
    restoreFromHistory,   // Expose the restore function
    // Computed results based on current index
    revisedTextResult,
    originalRevisedTextResult,
    llmFeedbackResult,
    learningFocusResult,
    usedVocabularyCardsResult,
    // State for navigation
    currentRevisionIndex,
    allRevisionsResult, // Expose the full list (e.g., for getting length)
    // Navigation functions
    nextRevision,
    previousRevision,
    // Error state
    revisionError,
    vocabularyWasUsedForLastRevision, // Expose the new state
  };
}