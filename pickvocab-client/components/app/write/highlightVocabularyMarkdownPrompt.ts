export interface RevisionHighlightInput {
  originalIndex: number; // Keep track of the original index
  revisionText: string;
  vocabularyList: string[];
}

/**
 * Creates a Markdown-based prompt for the LLM to highlight specific vocabulary words 
 * in multiple text revisions using Markdown bolding.
 *
 * @param revisionsToHighlight An array of objects, each containing the original index,
 *                             the text to highlight, and its corresponding vocabulary list.
 * @returns The prompt string.
 */
export function highlightVocabularyMarkdownPrompt(revisionsToHighlight: RevisionHighlightInput[]): string {
  if (!revisionsToHighlight || revisionsToHighlight.length === 0) {
    console.warn('highlightVocabularyMarkdownPrompt called with empty or invalid revisionsToHighlight array.');
    return ''; // Or handle as appropriate
  }

  // Construct the input data block for the prompt
  const inputDataBlock = revisionsToHighlight.map((revision, index) => `
## Revision ${index + 1}

**Text:**
${revision.revisionText}

**Vocabulary to Highlight:**
${revision.vocabularyList.map(word => `- ${word}`).join('\n')}
  `).join('\n');

  return `
<instructions>
You will receive a list of text revisions, each associated with an original index and a list of vocabulary words/phrases used within that specific revision.

Your task is to process **each revision independently**. For each revision, apply Markdown bold tags (\`**word**\`) to all occurrences of the vocabulary words/phrases provided *for that specific revision*. Follow the crucial matching guidelines below.

## Crucial Matching Guidelines (Apply to each revision independently):
- Your primary goal is to identify phrases in the text that *semantically match* the vocabulary items for that revision, even if they aren't character-for-character identical.
- **Handle Variations:** Bold phrases in the text that are variations of the vocabulary items (e.g., tense, pluralization, minor word changes).
- **Word Families:** Include different forms of the same word (e.g., 'analyze', 'analysis', 'analytical').
- **Inflections:** Include different grammatical forms (e.g., 'run' → 'running', 'ran'; 'child' → 'children').
- **Case Insensitivity:** Match vocabulary items regardless of case, but preserve the original casing of the text within the bold tags.
- **Prioritize Meaning:** Prioritize semantic meaning when deciding whether to bold a phrase.
- **Exact Matches:** Still bold exact matches.
- **No Rewriting:** Only add bold tags (\`** **\`). Do *not* modify the text in any other way.
- **Multiple Occurrences:** Bold *all* matching occurrences within the specific revision.
</instructions>

<output_format>
For each revision, provide the output in the following structured format:

# Revision 1

## Highlighted Text
[Full text of the revision with vocabulary words/phrases highlighted using **bold** markdown. Preserve all original formatting, newlines, and indentation.]

# Revision 2

## Highlighted Text
[Full text of the revision with vocabulary words/phrases highlighted using **bold** markdown. Preserve all original formatting, newlines, and indentation.]

# Revision 3

## Highlighted Text
[Full text of the revision with vocabulary words/phrases highlighted using **bold** markdown. Preserve all original formatting, newlines, and indentation.]

</output_format>

<example_output>
**Input:**
## Revision 1

**Text:**
The quick brown fox jumps over the lazy dog. He saw two big dogs later.

**Vocabulary to Highlight:**
- quick brown fox
- lazy dog
- dog

## Revision 2

**Text:**
You get the hang of it, congrats! Learning takes time.

**Vocabulary to Highlight:**
- getting the hang of
- learn

## Revision 3

**Text:**
The researchers analyzed the data thoroughly. Their analytical approach led to a comprehensive analysis.

**Vocabulary to Highlight:**
- analyze

## Revision 4

**Text:**
The child ran quickly, while the other children were running slowly.

**Vocabulary to Highlight:**
- child
- run

**Output:**
# Revision 1

## Highlighted Text
The **quick brown fox** jumps over the **lazy dog**. He saw two big **dogs** later.

# Revision 2

## Highlighted Text
You **get the hang of it**, congrats! **Learning** takes time.

# Revision 3

## Highlighted Text
The researchers **analyzed** the data thoroughly. Their **analytical** approach led to a comprehensive **analysis**.

# Revision 4

## Highlighted Text
The **child** **ran** quickly, while the other **children** were **running** slowly.
</example_output>

Now, process the provided input data based *strictly* on these rules and return the result in the specified Markdown format.

<input>
${inputDataBlock}
</input>
  `;
} 