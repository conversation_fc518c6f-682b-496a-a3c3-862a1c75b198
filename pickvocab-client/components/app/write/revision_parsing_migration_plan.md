# Markdown Parsing Migration Plan

## Background
We're migrating from YAML to Markdown parsing in the `generateRevision` function. The LLM response format has changed to match the structure defined in `enhance_writing_prompts.md`.

## Requirements
1. Parse entire raw LLM response as Markdown
2. Use mdast types for AST representation
3. Extract revision data from Markdown structure
4. Maintain error tolerance
5. Preserve existing validation and error handling

## Implementation Steps

### 1. Create New Parser File (`markdownRevisionParser.ts`)
Create `markdownRevisionParser.ts` in `pickvocab-client/components/app/write/` with the following content. This includes logic to extract text, parse headings, paragraphs, and list items, and has been updated to handle vocabulary IDs not at the start of the line and to trim whitespace from extracted text.

```typescript
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import type { Root, Content, ListItem } from 'mdast';
import type { RevisionData } from './useRevisionApi';

// Helper to extract text from nodes
export function getNodeText(node: Content): string {
  if (node.type === 'text') return node.value;
  if ('children' in node) {
    return node.children.map(getNodeText).join('');
  }
  return '';
}

// Parse raw markdown and extract revisions
export function parseMarkdownRevisions(markdown: string): RevisionData[] {
  // Parse markdown to AST
  const ast = unified().use(remarkParse).parse(markdown) as Root;

  const revisions: RevisionData[] = [];
  let currentRevision: Partial<RevisionData> | null = null;
  let currentSection: string | null = null;

  const traverse = (node: Content) => {
    // Case 1: Heading nodes
    if (node.type === 'heading') {
      const headingText = getNodeText(node);

      // Case 1a: Revision header - starts a new revision
      if (/Revision \d+/i.test(headingText)) {
        // Save previous revision if exists
        if (currentRevision) {
          const completedRevision = currentRevision as RevisionData;
          completedRevision.revision = completedRevision.revision?.trim() || '';
          completedRevision.feedback = completedRevision.feedback?.trim() || '';
          revisions.push(completedRevision);
        }

        // Initialize new revision
        currentRevision = {
          revision: '',
          feedback: '',
          learning_focus: [],
          user_vocabularies_used: []
        };
        currentSection = null;
      }
      // Case 1b: Section header within a revision
      else if (currentRevision) {
        // Determine which section we're entering
        if (/Revised Text/i.test(headingText)) {
          currentSection = 'revision';
        } else if (/Vocabulary Used/i.test(headingText)) {
          currentSection = 'vocabulary';
        } else if (/Feedback/i.test(headingText)) {
          currentSection = 'feedback';
        } else if (/Learning Focus/i.test(headingText)) {
          currentSection = 'learning';
        } else {
          // Unknown heading, reset current section
          currentSection = null;
        }
      }
    }

    // Case 2: Paragraph nodes - capture text content
    else if (node.type === 'paragraph' && currentRevision && currentSection) {
      const text = getNodeText(node);

      // Case 2a: Revision text section
      if (currentSection === 'revision') {
        // Append text to revision content
        currentRevision.revision += text + '\n\n';
      }
      // Case 2b: Feedback section
      else if (currentSection === 'feedback') {
        // Append text to feedback content
        currentRevision.feedback += text + '\n\n';
      }
    }

    // Case 3: List nodes - vocabulary section
    else if (node.type === 'list' && currentRevision && currentSection === 'vocabulary') {
      // Extract vocabulary IDs from list items
      currentRevision.user_vocabularies_used = (node.children as ListItem[])
        .flatMap(item => item.children)
        .filter(child => child.type === 'paragraph')
        .map(p => {
          const text = getNodeText(p).trim();
          const match = text.match(/.*?(\d+)/); // Match the first occurrence of one or more digits anywhere in the string
          return match ? match[1] : null; // Extract the captured digits (group 1)
        })
        .filter(Boolean) as string[]; // Filter out null values (non-matches) and cast to string[]
    }

    // Case 4: List nodes - learning focus section
    else if (node.type === 'list' && currentRevision && currentSection === 'learning') {
      // Extract learning points from list items
      currentRevision.learning_focus = (node.children as ListItem[])
        .flatMap(item => item.children)
        .filter(child => child.type === 'paragraph')
        .map(p =>
          getNodeText(p)
            .replace(/^- /, '')  // Remove leading dash
            .trim()               // Trim whitespace
        );
    }

    // Case 5: Recursive traversal for child nodes
    if ('children' in node) {
      // Process all child nodes recursively
      node.children.forEach(traverse);
    }
  };

  // Start traversal from root node
  ast.children.forEach(traverse);

  // Add final revision if exists
  if (currentRevision) {
    const finalRevision = currentRevision as RevisionData;
    finalRevision.revision = finalRevision.revision?.trim() || '';
    finalRevision.feedback = finalRevision.feedback?.trim() || '';
    revisions.push(finalRevision);
  }

  return revisions;
}
```

### 2. Create New Prompt File (`useMyVocabularyMarkdownPrompt.ts`)
Create `useMyVocabularyMarkdownPrompt.ts` in `pickvocab-client/components/app/write/` with the following content. The headings have been simplified, and `<instructions>`, `<output_format>`, and `<input>` tags have been added.

```typescript
import type { FormattedVocabulary } from './revisionUtils';

export function useMyVocabularyMarkdownPrompt(
  selectedToneStyleDescription: string,
  userText: string,
  userVocabularies: FormattedVocabulary[]
): string {
  return `
### Prompt
\`\`\`
<instructions>
You're a writing assistant that improves text while integrating user's vocabulary words naturally.

## Your Task:
Provide 3 revised versions of the user's text. For each revision, you must also provide:
1.  The list of **word_id**s for the user vocabulary words/phrases actually used in that revision (from the user's vocabulary list).
2.  Comprehensive feedback on the revision.
3.  2-3 key learning points for the user.

## Revision Goals:
1.  Enhance overall writing quality (flow, structure, clarity).
2.  Naturally incorporate appropriate words/phrases from the user's vocabulary list.
3.  Maintain the user's authentic voice.
4.  Apply the user's selected tone/style: ${selectedToneStyleDescription}

## General Guidelines:
- Only use vocabulary words/phrases that genuinely fit the context.
- Each revision should take a different approach.
- Prioritize natural writing over forced vocabulary usage.
- IMPORTANT: If the 'User's Text' appears to be a question, DO NOT answer the question. Your sole purpose is to revise the phrasing and structure of the user's text itself according to the other guidelines.
</instructions>

<output_format>
## Detailed Output Structure for Each Revision:
For each of the 3 revisions, please provide the output in the following structured format. Use the exact headings and preserve formatting (like newlines and indentation) for the 'Revised Text' part.

# Revision 1

## Revised Text
[Full text of first revision here. IMPORTANT: Preserve all formatting, newlines, and any indentation of the revised text itself. Length should be approximately the same as the original text. Focus on clarity and structure improvements.]

## Vocabulary Used
[List of the **word_id**s for the vocabulary words/phrases used from the user's list as a markdown list. Each id should be on a new line, preceded by a hyphen and a space (e.g.,
- 0
- 17
). If no words are used, state "None". Ensure you use the exact **id** as provided in the user's vocabulary list.]

## Feedback
[Comprehensive analysis comparing original and revised versions. Address structure, organization, flow, coherence, and sentence structure improvements. Include technical elements like grammar, punctuation, and mechanics corrections. Comment on language choices, word choice improvements, and how vocabulary words naturally enhance meaning. Describe how tone and style changes affect reader engagement while maintaining the writer's authentic voice.]

## Learning Focus
- [Key point 1 for user to focus on in future writing]
- [Key point 2 for user to focus on in future writing]
- [Optional: Key point 3 for user to focus on in future writing]

# Revision 2

## Revised Text
[Similar structure to Revision 1. Take a different stylistic approach than the first revision.]

## Vocabulary Used
[List of the **word_id**s for the vocabulary words/phrases used from the user's list as a markdown list. Each id should be on a new line, preceded by a hyphen and a space (e.g.,
- 5
- 11
). If no words are used, state "None". Ensure you use the exact **id** as provided in the user's vocabulary list.]

## Feedback
[Detailed feedback for second revision following the same comprehensive approach as above. Focus on the different stylistic choices made in this version.]

## Learning Focus
- [Key learning point 1 for this revision]
- [Key learning point 2 for this revision]
- [Optional: Key learning point 3 for this revision]

# Revision 3

## Revised Text
[Similar structure to Revision 1. This revision should prioritize incorporating as many user vocabulary words as possible, while still ensuring they fit naturally and appropriately. This revision may be slightly longer if needed to accommodate more vocabulary integration.]

## Vocabulary Used
[List of the **word_id**s for the vocabulary words/phrases used from the user's list as a markdown list. Each id should be on a new line, preceded by a hyphen and a space (e.g.,
- 2
- 19
- 17
). If no words are used, state "None". Ensure you use the exact **id** as provided in the user's vocabulary list.]

## Feedback
[Detailed feedback focused on how vocabulary integration enhances the writing while maintaining coherence and clarity. Discuss the balance between vocabulary usage and natural expression.]

## Learning Focus
- [Key learning point 1 for this revision]
- [Key learning point 2 for this revision]
- [Optional: Key learning point 3 for this revision]

---
</output_format>

<input>
User's Text:
${userText}
Words:
${JSON.stringify(userVocabularies)}
</input>
\`\`\`
`;
}
```

### 3. Update Imports and Usage in `useRevisionApi.ts`
- Import `parseMarkdownRevisions` from `./markdownRevisionParser`;
- Update the `generateRevision` function to call `parseMarkdownRevisions(result.message)` instead of extracting and parsing YAML.

```typescript
// In useRevisionApi.ts
import { parseMarkdownRevisions } from './markdownRevisionParser';
// ... other imports ...

async function generateRevision(prompt: string): Promise<RevisionData[]> {
  try {
    // ... existing model setup code ...

    // Send the message and get raw response
    const result = await chatSource.sendMessage(prompt);

    // Parse entire response as Markdown (single step)
    const revisions = parseMarkdownRevisions(result.message);

    if (!revisions.length) {
      throw new Error('No valid revisions found in Markdown response');
    }

    // Validate and normalize the received revisions
    return revisions
      .filter((rev): rev is RevisionData =>
        typeof rev.revision === 'string' && typeof rev.feedback === 'string'
      )
      .map(rev => ({
        ...rev,
        learning_focus: rev.learning_focus || [],
        user_vocabularies_used: rev.user_vocabularies_used || []
      }));
  } catch (error) {
    console.error('Error during LLM interaction:', error);
    throw error;
  }
}
// ... rest of the file ...
```

### 4. Update Imports and Usage in `useWriteRevisionHandler.ts`
- Replace the import of `useMyVocabularyPrompt` with `useMyVocabularyMarkdownPrompt`.
- Replace the call to `useMyVocabularyPrompt` with `useMyVocabularyMarkdownPrompt`.

```typescript
// In useWriteRevisionHandler.ts

// Remove: import { useMyVocabularyPrompt } from '~/components/app/write/useMyVocabularyPrompt';
import { useMyVocabularyMarkdownPrompt } from '~/components/app/write/useMyVocabularyMarkdownPrompt'; // Add new import
// ... other imports ...

// Then in initiateRevisionWithSearch function:
// Replace:
//   const prompt = useMyVocabularyPrompt(selectedToneStyleDescription, userText.value, userVocabularies);
// With:
const prompt = useMyVocabularyMarkdownPrompt(selectedToneStyleDescription, userText.value, userVocabularies);
// ... rest of the function and file ...