import { marked } from 'marked';
import type { HighlightedRevisionItem } from './useRevisionApi';
import { getTextFromMarkedTokens, type MarkedToken } from './markedUtils';

// Parse raw markdown and extract highlighted revisions
export function parseMarkdownHighlights(markdown: string): HighlightedRevisionItem[] {
  // Parse markdown to tokens using standard marked lexer
  const tokens = marked.lexer(markdown);

  const highlightedRevisions: HighlightedRevisionItem[] = [];
  let currentHighlight: { highlightedText: string } | undefined;
  let currentSection: string | undefined;

  const traverse = (token: MarkedToken) => {
    // Case 1: Heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';

      // Case 1a: Revision header - starts a new highlighted revision
      // Very tolerant regex to match any heading containing 'Revision' and a number
      if (/revision.*\d+/i.test(headingText)) {
        // Save previous highlight if exists and is valid
        if (currentHighlight !== undefined && currentHighlight.highlightedText.length > 0) {
          highlightedRevisions.push({ 
            highlightedText: currentHighlight.highlightedText.trim() 
          });
        }

        // Initialize new highlight
        currentHighlight = {
          highlightedText: ''
        };
        currentSection = undefined;
      }
      // Case 1b: Section header within a revision
      else if (currentHighlight !== undefined) {
        // Determine which section we're entering with error tolerance
        if (/highlight/i.test(headingText)) {
          currentSection = 'highlighted';
          return; // Skip further processing of this main section heading
        } else {
          // Unknown heading, reset current section
          currentSection = undefined;
        }
      }
    }

    // Case 2: Capture ALL content for highlighted sections
    else if (currentHighlight !== undefined && currentSection === 'highlighted') {
      let content = '';

      // Handle different token types
      if (token.type === 'paragraph') {
        content = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
      } else if (token.type === 'list') {
        if (token.items && Array.isArray(token.items)) {
          const listItems = token.items
            .map(item => {
              let text = item.text || '';
              if (!text && item.tokens && Array.isArray(item.tokens)) {
                text = getTextFromMarkedTokens(item.tokens);
              }
              return '- ' + text.trim();
            })
            .filter(text => text.length > 2);
          content = listItems.join('\n');
        }
      } else if (token.type === 'code') {
        content = '```' + (token.lang || '') + '\n' + token.text + '\n```';
      } else if (token.type === 'blockquote') {
        content = '> ' + (token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '');
      } else if (token.type === 'hr') {
        content = '---';
      } else if (token.type === 'table') {
        // Basic table handling - you might want to enhance this
        content = '[Table content]';
      } else if (token.text) {
        content = token.text;
      } else if (token.type === 'heading') {
        // Capture subheadings within highlighted sections with markdown formatting
        const headingText = token.tokens ? getTextFromMarkedTokens(token.tokens) : token.text || '';
        content = '#'.repeat(token.depth) + ' ' + headingText;
      }

      // Append content to appropriate section
      if (content) {
        currentHighlight.highlightedText += content + '\n\n';
      }

      // Don't recursively process tokens for highlighted sections
      // to avoid duplication - we've already extracted the content above
      return;
    }

    // Case 3: Recursive traversal for tokens with nested tokens
    if (token.tokens && Array.isArray(token.tokens)) {
      // Process all nested tokens recursively
      token.tokens.forEach(traverse);
    }

    // Case 4: Handle list items (which have items array instead of tokens)
    if (token.type === 'list' && token.items && Array.isArray(token.items)) {
      token.items.forEach(traverse);
    }
  };

  // Start traversal from top-level tokens
  tokens.forEach(traverse);

  // Add final highlight if exists and is valid
  if (currentHighlight !== undefined && currentHighlight.highlightedText.length > 0) {
    highlightedRevisions.push({ 
      highlightedText: currentHighlight.highlightedText.trim() 
    });
  }

  return highlightedRevisions;
} 