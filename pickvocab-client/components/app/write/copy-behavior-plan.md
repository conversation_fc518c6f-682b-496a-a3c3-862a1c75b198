# Plan to Modify Copy Behavior for Writing Assistant

**Objective:** Modify the "Copy Text" functionality in the Writing Assistant so that it copies the original LLM-generated revision text *before* any vocabulary highlighting is applied, while continuing to *display* the highlighted text to the user. This applies to both the dedicated copy button and the copy-on-click behavior of the revised text display area.

## Current Situation:
- The `allRevisionsResult` ref in `useWriteRevisionHandler.ts` stores `RevisionData` objects.
- The `applyHighlightsToState` function in `useWriteRevisionHandler.ts` directly modifies the `revision` property of these `RevisionData` objects with the highlighted text.
- The `copyRevisedText` function in `pages/app/write/index.vue` uses `revisedTextResult` (derived from the `revision` property) from `useWriteRevisionHandler.ts`, thus copying the highlighted version for the dedicated copy button.
- `RevisedTextViewer.vue` has its own internal `handleCopy` function that copies its `revisedText` prop, which is also the highlighted version.

## Proposed Plan:

**Part A: Core Logic for Original Text Storage**

1.  **Modify `RevisionData` Interface:**
    *   **File:** `pickvocab-client/components/app/write/useRevisionApi.ts`
    *   **Action:** Add a new optional property to the `RevisionData` interface.
        ```typescript
        export interface RevisionData {
          revision: string;
          originalRevision?: string; // New property
          user_vocabularies_used?: string[];
          real_card_ids?: string[];
          feedback: string;
          learning_focus?: string[];
        }
        ```

2.  **Populate `originalRevision` in `useWriteRevisionHandler.ts`:**
    *   **File:** `pickvocab-client/components/app/write/useWriteRevisionHandler.ts`
    *   **Function:** `callLLMAndParseResponse`
    *   **Action:** After `validRevisions` are processed and before `allRevisionsResult.value` is set, map `validRevisions` to include the `originalRevision`.
        ```typescript
        // In callLLMAndParseResponse
        const revisionsWithOriginal = validRevisions.map(rev => ({
          ...rev,
          originalRevision: rev.revision
        }));
        allRevisionsResult.value = revisionsWithOriginal;
        ```

3.  **Add `originalRevisedTextResult` Computed Property to `useWriteRevisionHandler.ts`:**
    *   **File:** `pickvocab-client/components/app/write/useWriteRevisionHandler.ts`
    *   **Action:** Create a new computed property.
        ```typescript
        const originalRevisedTextResult: ComputedRef<string> = computed(() => {
          return currentRevisionData.value?.originalRevision || currentRevisionData.value?.revision || '';
        });
        ```
    *   Ensure `originalRevisedTextResult` is returned by the `useWriteRevisionHandler` composable.

**Part B: Updating the Dedicated Copy Button Logic**

4.  **Update Copy Logic in `pickvocab-client/pages/app/write/index.vue` (for dedicated copy button):**
    *   **File:** `pickvocab-client/pages/app/write/index.vue`
    *   **Action:**
        *   Destructure `originalRevisedTextResult` from `useWriteRevisionHandler`.
            ```typescript
            const {
              // ... other properties from useWriteRevisionHandler ...
              originalRevisedTextResult, // Ensure this is destructured
              // ...
            } = useWriteRevisionHandler(userText, useVocabulary, grammarCheck);
            ```
        *   Modify the `copyRevisedText` function (which handles the `@copyRevisedText` event from `OutputSectionHeader.vue` via `OutputSection.vue`) to use `originalRevisedTextResult.value`.
            ```typescript
            const copyRevisedText = () => {
              const textToCopy = originalRevisedTextResult.value;
              navigator.clipboard.writeText(textToCopy)
                .then(() => {
                  console.log('Original revised text copied to clipboard (via button).');
                })
                .catch(err => {
                  console.error('Failed to copy original revised text (via button): ', err);
                });
            };
            ```

**Part C: Updating Copy-on-Click for `RevisedTextViewer.vue`**

5.  **Pass `originalRevisedTextResult` to `OutputSection.vue`:**
    *   **File:** `pickvocab-client/pages/app/write/index.vue`
    *   **Action:** Pass `originalRevisedTextResult` as a new prop named `originalTextForCopy` to the `OutputSection` component.
        ```html
        <OutputSection
          <!-- ... other props ... -->
          :revised-text="revisedTextResult"
          :original-text-for-copy="originalRevisedTextResult" <!-- New prop -->
          @copyRevisedText="copyRevisedText"
          <!-- ... other event handlers ... -->
        />
        ```

6.  **Receive and Pass `originalTextForCopy` in `OutputSection.vue`:**
    *   **File:** `pickvocab-client/components/app/write/OutputSection.vue`
    *   **Action:**
        *   Define the new `originalTextForCopy` prop.
            ```typescript
            // Inside defineProps in OutputSection.vue
            props: {
              // ... other props ...
              revisedText: { type: String, required: true },
              originalTextForCopy: { type: String, required: true }, // New prop
              // ...
            },
            ```
        *   Pass this `props.originalTextForCopy` down to the `RevisedTextViewer` component as a prop, also named `originalTextForCopy`.
            ```html
            <!-- Inside OutputSection.vue template -->
            <RevisedTextViewer
              :revised-text="props.revisedText"
              :original-text-for-copy="props.originalTextForCopy" <!-- Pass down -->
            />
            ```

7.  **Modify `RevisedTextViewer.vue` to use the new prop for its internal copy-on-click:**
    *   **File:** `pickvocab-client/components/app/write/RevisedTextViewer.vue`
    *   **Action:**
        *   Add `originalTextForCopy` to its props definition.
            ```typescript
            // Inside defineProps in RevisedTextViewer.vue
            const props = defineProps<{
              revisedText: string;
              originalTextForCopy: string; // New prop
            }>();
            ```
        *   Update its internal `handleCopy` function to use `props.originalTextForCopy`.
            ```typescript
            async function handleCopy() {
              if (!props.originalTextForCopy) return; // Use the new prop
              try {
                await navigator.clipboard.writeText(props.originalTextForCopy); // Use the new prop
                copied.value = true;
                setTimeout(() => {
                  copied.value = false;
                }, 1200);
              } catch (e) {
                // Handle error
              }
            }
            ```

## Expected Outcome (Updated):
- Users will see the highlighted version of the revised text in the UI.
- When users click the dedicated "Copy Text" button, the original, unhighlighted text will be copied.
- When users click directly on the revised text display area (`RevisedTextViewer`), the original, unhighlighted text will also be copied.