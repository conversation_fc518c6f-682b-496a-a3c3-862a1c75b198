<script setup lang="ts">
import { ref } from 'vue';
import { defineProps } from 'vue';
import TiptapEditor from '~/components/editor/TiptapEditor.vue';

const props = defineProps<{
  revisedText: string;
  originalTextForCopy: string; // New prop
}>();

const copied = ref(false);

async function handleCopy() {
  if (!props.originalTextForCopy) return;
  try {
    await navigator.clipboard.writeText(props.originalTextForCopy);
    copied.value = true;
    setTimeout(() => {
      copied.value = false;
    }, 1200);
  } catch (e) {
    // Optionally handle error
  }
}
</script>

<template>
  <div
    class="relative text-sm bg-white p-4 min-h-[200px] rounded-md border border-gray-200 shadow-inner cursor-pointer group"
    @click="handleCopy"
    :title="copied ? 'Copied!' : 'Click to copy'"
    tabindex="0"
    role="button"
    aria-label="Copy revised text"
  >
    <transition name="fade">
      <div
        v-if="copied"
        class="absolute top-2 right-3 flex items-center space-x-1 bg-emerald-50 border border-emerald-200 text-emerald-700 text-xs font-semibold px-2 py-1 rounded shadow z-10 animate-fade-in"
        style="pointer-events: none;"
      >
        <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"/></svg>
        <span>Copied!</span>
      </div>
    </transition>
    <TiptapEditor
      v-if="revisedText"
      :text="revisedText"
      :editable="false"
      :show-options="false"
      :show-bubble-menu="false"
      :enable-markdown="true"
      :css-classes="'prose max-w-none focus:outline-none min-h-[inherit]'"
    />
    <p v-else class="text-gray-400 italic">Revised text will appear here...</p>
    <div v-if="!copied" class="absolute top-2 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-xs text-gray-400 select-none pointer-events-none">
      Click to copy
    </div>
  </div>
</template>

<style scoped>
.min-h-\[200px\] {
  min-height: 200px;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
.fade-enter-to, .fade-leave-from {
  opacity: 1;
}
.animate-fade-in {
  animation: fadeInScale 0.2s;
}
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>