# Simple Revision Markdown Migration Plan

## Background
We need to migrate the remaining YAML-based revision prompts (`reviseTextSimplePrompt` and `grammarCheckPrompt`) to use Markdown format, consistent with the vocabulary-based revision system that has already been migrated.

## Current State Analysis
- ✅ Markdown parser (`markdownRevisionParser.ts`) is implemented
- ✅ `useRevisionApi.ts` uses `parseMarkdownRevisions` 
- ✅ Vocabulary-based revisions use Markdown format via `useMyVocabularyMarkdownPrompt`
- ❌ `reviseTextSimplePrompt.ts` still outputs YAML format
- ❌ `grammarCheckPrompt.ts` still outputs YAML format

## Migration Goals
1. Convert `reviseTextSimplePrompt` to Markdown format
2. Convert `grammarCheckPrompt` to Markdown format
3. Update `useWriteRevisionHandler.ts` to use new Markdown prompts
4. Maintain consistent structure across all revision types
5. Preserve existing functionality and user experience

## Implementation Steps

### 1. Create `reviseTextSimpleMarkdownPrompt.ts`
Create a new file that outputs Markdown format instead of YAML, following the same structure as `useMyVocabularyMarkdownPrompt.ts` but without vocabulary-specific sections.

**Key Features:**
- Uses `<instructions>`, `<output_format>`, and `<input>` tags
- Outputs 3 revisions in Markdown format
- Includes "Vocabulary Used: None" sections for consistency
- Maintains all existing revision guidelines and instructions

### 2. Create `grammarCheckMarkdownPrompt.ts`
Create a new file for grammar check that outputs Markdown format.

**Key Features:**
- Uses same tag structure as other Markdown prompts
- Outputs only 1 revision (grammar check specific)
- Includes "Vocabulary Used: None" for consistency
- Maintains grammar-only focus

### 3. Update `useWriteRevisionHandler.ts`
Update imports and function calls to use the new Markdown prompts:
- Replace `reviseTextSimplePrompt` import with `reviseTextSimpleMarkdownPrompt`
- Replace `grammarCheckPrompt` dynamic import with `grammarCheckMarkdownPrompt`
- Update function calls in `initiateRevisionDirectly()` and `initiateGrammarCheck()`

### 4. Testing Strategy
- Test simple revision path (no vocabulary)
- Test grammar check path
- Verify all three revision types work consistently
- Ensure parsing works correctly for all formats

## File Structure After Migration
```
pickvocab-client/components/app/write/
├── markdownRevisionParser.ts (✅ existing)
├── useMyVocabularyMarkdownPrompt.ts (✅ existing)
├── reviseTextSimpleMarkdownPrompt.ts (🆕 new)
├── grammarCheckMarkdownPrompt.ts (🆕 new)
├── reviseTextSimplePrompt.ts (🗑️ can be removed after migration)
├── grammarCheckPrompt.ts (🗑️ can be removed after migration)
└── useWriteRevisionHandler.ts (🔄 updated)
```

## Benefits
1. **Unified Parsing**: All revision types use the same Markdown parser
2. **Consistent Format**: All prompts follow identical structure
3. **Better Maintainability**: Single parsing logic across all revision types
4. **Future-Proof**: Easier to extend with new features
5. **Reduced Complexity**: No need to maintain both YAML and Markdown parsers

## Implementation Order
1. ✅ Create plan file
2. ✅ Create `reviseTextSimpleMarkdownPrompt.ts`
3. ✅ Create `grammarCheckMarkdownPrompt.ts`
4. ✅ Update `useWriteRevisionHandler.ts` imports and calls
5. 🔄 Test all revision paths
6. 🔄 Remove old YAML prompt files (optional cleanup)

## Risk Mitigation
- Keep old YAML files until migration is confirmed working
- Test each revision type individually
- Ensure backward compatibility during transition
- Monitor for any parsing errors or format issues 