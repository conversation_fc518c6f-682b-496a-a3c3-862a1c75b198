# Plan: Migrating Markdown Parsing to `marked`

**Overall Goal:** Replace `unified`/`remark-parse` with `marked` for parsing LLM responses in `pickvocab-client/components/app/write/markdownHighlightParser.ts` and `pickvocab-client/components/app/write/markdownRevisionParser.ts` to improve performance, while maintaining all existing functionality.

**Primary Driver:** Improve parsing performance for large Markdown inputs.

**Key Challenge:** Adapting the current AST-based traversal logic (designed for `mdast` from `unified`/`remark-parse`) to work with `marked`'s output (likely its token stream from `marked.lexer()`).

---

## Phase 1: Investigation & `marked` Output Analysis (✅ Done)

**Objective:** Thoroughly understand the structure of data that `marked` provides for your specific Markdown inputs, and how it compares to `mdast`.

1.  **Setup & Familiarization:**
    *   Install `marked`: `npm install marked` and `npm install --save-dev @types/marked` (or `yarn` equivalents). (✅ Done)
    *   Review `marked` documentation, focusing on:
        *   `marked.parse(markdownString)`
        *   `marked.lexer(markdownString)` (Primary focus for understanding token structure)
        *   Custom Renderers (Less likely needed if processing tokens directly)
        *   Extensions (GFM support)
    *   *(Optional)* Briefly investigate `marked-ast` or similar libraries if a more structured AST is strongly preferred over token streams, but the initial plan is to work with the token stream from `marked.lexer()`.

2.  **Sample-Based Output Analysis (✅ Done):**
    *   Take representative samples of Markdown generated by current prompts (e.g., from `highlightVocabularyMarkdownPrompt.ts`, `useMyVocabularyMarkdownPrompt.ts`).
    *   Process these samples using `marked.lexer(sample)` to get the token stream.
        *   **Done:** Modified `pickvocab-client/components/app/write/test.ts` to use `marked.lexer()` and print its output for a sample Markdown string.
        *   **Done:** Analyzed the output from `test.ts` to understand `marked`'s token structure.
    *   **Crucial Step:** Document the structure of these tokens. For each Markdown element (heading, paragraph, list, list item, text, bold, italic), identify how `marked` represents it (node types, properties, how children/content are structured).
    *   **2a. Summary of `marked.lexer()` Token Structure (from `test.ts` analysis):**
        *   **Top-Level Array:** `marked.lexer()` returns a flat array of "block" tokens (e.g., `heading`, `paragraph`, `list`, `code`, `blockquote`, `space`). This differs from `mdast`'s single root `tree` object.
        *   **`tokens` Property:** Many block-level tokens (e.g., `heading`, `paragraph`, `list_item`, `blockquote`) and some inline-level tokens (e.g., `strong`, `em`, `link`) contain a nested `tokens` array. This array holds further tokenized inline content.
        *   **`raw` vs. `text` Properties:**
            *   `raw`: Typically stores the original Markdown substring for that token. Useful for preserving exact Markdown syntax like `**bold**`.
            *   `text`: Often provides a processed version of the content. For tokens like `paragraph` or `list_item`, this `text` property might still contain unparsed Markdown characters (e.g., `This is **bold**...`). For simple tokens like `strong` or `em`, `text` usually holds the content *inside* the markers (e.g., "bold").
        *   **Key Token Types Observed:**
            *   `heading`: Has `depth`, `text` (title), and a `tokens` array for the title's inline content.
            *   `paragraph`: Its inline content is in its `tokens` array.
            *   `strong` (bold), `em` (italic): Appear as distinct tokens within a `tokens` array (e.g., inside a paragraph). They have `text` (content) and their own `tokens` array. The `raw` property is useful for reconstruction (e.g., `**text**`).
            *   `list`: Contains an `items` array, where each element is a `list_item` token. Properties include `ordered`, `loose`.
            *   `list_item`: Its content is in its `tokens` array. A nested list within a list item will appear as a `list` token within the parent `list_item`'s `tokens`.
            *   `link`: Has `href`, `title` (optional), and `text` (visible link text). Its `tokens` array contains the inline content of the link text.
            *   `code`: Has `lang` (language) and `text` (the raw code content).
            *   `blockquote`: Its content (e.g., paragraphs) is in its `tokens` array.
            *   `space`: Represents whitespace between block elements, often containing newlines (`\n`).

3.  **`mdast` vs. `marked` Token/AST Comparison:**
    *   Compare the documented `marked` token structure with the `mdast` structure that current parsers expect.
    *   Identify key differences in node types, text content storage, children access, and inline formatting representation. This will inform the adaptation effort.

---

## Phase 2: Adapting the Parsing Logic (✅ Done)

**Objective:** Rewrite `markdownHighlightParser.ts` and `markdownRevisionParser.ts` to use `marked`.

1.  **Create `getTextFromMarkedTokens` (or similar name):** (✅ Done - Created `markedUtils.ts`)
    *   This function will replace the `getNodeText` logic.
    *   It will accept an array of `marked` tokens (e.g., the `tokens` property of a `paragraph` or `heading` token from `marked.lexer()`).
    *   It will recursively reconstruct the text content.
    *   **Crucially, it must preserve specified Markdown formatting (e.g., `**bold**`, `*italic*`) as raw Markdown strings within the reconstructed text.** This involves checking token types (e.g., `strong`, `em`) and using their `raw` property or reconstructing the Markdown syntax around their `text` content.

2.  **Rewrite `parseMarkdownHighlights` (in `markdownHighlightParser.ts`):** (✅ Done)
    *   Replace `unified().use(remarkParse).parse()` with `marked.lexer()`.
    *   Adapt logic to iterate through the top-level token list from `marked.lexer()`.
    *   Identify tokens corresponding to highlighted revisions based on the LLM's response structure.
    *   Use the new `getTextFromMarkedTokens` function to extract the `highlightedText` from the relevant tokens, ensuring formatting is preserved.

3.  **Rewrite `parseMarkdownRevisions` (in `markdownRevisionParser.ts`):** (✅ Done)
    *   This is the most complex adaptation.
    *   Replace `unified().use(remarkParse).parse()` with `marked.lexer()`.
    *   **Traversal Strategy:** Implement a new stateful traversal mechanism for the top-level token array from `marked.lexer()`. (✅ Done)
        *   Identify "Revision X" sections (e.g., by `heading` tokens with specific `text` and `depth`). (✅ Done - Implemented error-tolerant matching)
        *   Within a revision, identify "Revised Text", "Vocabulary Used", "Feedback", and "Learning Focus" sub-sections based on their `heading` tokens. This will manage the current parsing state. (✅ Done - Implemented error-tolerant matching)
    *   **Content Extraction (based on current state):** (✅ Done)
        *   For `revision` and `feedback` (from `paragraph` tokens): Use `getTextFromMarkedTokens` on the paragraph's inner `tokens` property. (✅ Done)
        *   For `user_vocabularies_used` and `learning_focus` (from `list` tokens): (✅ Done)
            *   Iterate through the `list` token's `items` array (each is a `list_item` token). (✅ Done)
            *   For each `list_item`, use `getTextFromMarkedTokens` on its inner `tokens` property to get the item's content or use the item's `text` property directly. (✅ Done)
            *   Process this content as needed (e.g., trim, extract vocabulary IDs). (✅ Done - Implemented error-tolerant vocabulary ID extraction)
    *   Ensure all existing business logic (whitespace trimming, vocabulary ID extraction details) is preserved. (✅ Done)

4.  **Error Handling:** (✅ Done - Implemented error tolerance in section and vocabulary matching)
    *   Investigate `marked`'s error handling for malformed Markdown.
    *   Implement robust error handling in the new parsers to prevent application crashes and handle unexpected input gracefully, consistent with existing requirements.

---

## Phase 3: Integration, Testing & Performance Verification (✅ Done)

**Objective:** Integrate the `marked`-based parsers, test thoroughly, and confirm performance gains.

1.  **Update Imports in `useRevisionApi.ts`:** (✅ Done - No changes needed)
    *   Modify `useRevisionApi.ts` to import and use the newly adapted `parseMarkdownHighlights` and `parseMarkdownRevisions` functions (which now use `marked`).
    *   The function signatures and return types of these parsers should ideally remain unchanged to minimize modifications in `useRevisionApi.ts`.

2.  **No Changes Expected in Prompts or `useWriteRevisionHandler.ts` (for this specific `marked` migration):** (✅ Confirmed)
    *   Markdown prompt files generate Markdown text (input to parsers).
    *   `useWriteRevisionHandler.ts` calls functions from `useRevisionApi.ts`. If the API of those functions is stable, `useWriteRevisionHandler.ts` shouldn't need changes related to `marked`.

3.  **Comprehensive Testing:** (✅ Done)
    *   Re-run/adapt all tests from existing migration plans (`highlighting_parsing_migration_plan.md`, `revision_parsing_migration_plan.md`, `simple_revision_markdown_migration_plan.md`).
    *   Test end-to-end:
        *   Highlighting process. (✅ Done)
        *   Revisions with vocabulary (`useMyVocabularyMarkdownPrompt.ts`). (✅ Done)
        *   Simple revisions and grammar checks (once their respective prompts are updated to Markdown as per `simple_revision_markdown_migration_plan.md`). (✅ Done)
    *   Verify correct data extraction, formatting preservation, and error handling. (✅ Done - Including error tolerance for various formats)

4.  **Performance Benchmarking:** (📝 Not explicitly benchmarked, but direct token processing is expected to be faster)
    *   Design test cases with representative large Markdown inputs.
    *   Benchmark execution time: old `unified`/`remark-parse` parsers vs. new `marked`-based parsers.
    *   Capture metrics like average parsing time, and potentially memory usage if relevant.
    *   Confirm tangible performance improvement.

5.  **Sanitization Note:**
    *   `marked` does not sanitize HTML output by default. While the primary goal here is to process tokens into structured JS objects (not direct HTML rendering), be cautious if any intermediate step involves generating HTML. If so, ensure sanitization (e.g., DOMPurify) to prevent XSS if that HTML could ever be rendered. Working directly with `marked.lexer()` tokens should minimize this concern.

---

## Phase 4: Documentation & Cleanup (✅ In Progress)

**Objective:** Finalize the migration and update project documentation.

1.  **Update Migration Documents:** (✅ Done - This document updated)
    *   Update this file (`marked_migration_plan.md`) as progress is made.
    *   Modify the original migration plans (`highlighting_parsing_migration_plan.md`, `revision_parsing_migration_plan.md`, `simple_revision_markdown_migration_plan.md`) to:
        *   Reflect `marked` as the parsing engine.
        *   Remove `unified`/`remark-parse` specific AST details.
        *   Reference this central `marked_migration_plan.md`.

2.  **Code Cleanup:** (📝 Pending confirmation of no other `unified`/`remark-parse` usage)
    *   Once the `marked`-based solution is stable and performance gains are confirmed:
        *   Remove old `unified`/`remark-parse` based parser files and related utilities.
        *   Remove old YAML-based prompt files if not already done.

---

## Implemented Error Tolerance Details

The parsing logic has been updated to be significantly more tolerant of variations in LLM output formatting:

-   **Vocabulary Extraction:** The regex for extracting vocabulary IDs now handles a wide range of formats, including plain numbers, quoted numbers, and numbers embedded within descriptive text (e.g., "Word ID 123", "ID: 456", "Vocabulary item 999"). It extracts any sequence of digits found within the list item text.
-   **Section Headers:** The regex for identifying sub-sections (Revised Text, Vocabulary Used, Feedback, Learning Focus) is now more flexible, matching keywords like "Text", "Vocabulary", "Feedback", and "Focus" regardless of surrounding words or capitalization (e.g., "## Text", "## Vocabulary", "## Learning Focus Points").
-   **Revision Headers:** The regex for identifying individual revisions is also more tolerant, matching any heading containing "Revision" followed by a number, allowing for variations in wording before and after the number (e.g., "# Revision Number 1", "# Revision 2 - Second Version").

These improvements make the parsers more robust to minor inconsistencies in LLM-generated Markdown.

---

## Mermaid Diagram: System Flow After Migration

```mermaid
graph TD
    A[Markdown String] --> B{marked.lexer()};
    B --> C[Marked Tokens];
    C --> D{New Token-Based Traversal Logic (adapted for marked output)};
    D --> E[Structured Data (RevisionData[], HighlightedRevisionItem[])];
```