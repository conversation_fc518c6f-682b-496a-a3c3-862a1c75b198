# Highlighting Process Markdown Migration Plan

## Background
We're migrating the highlighting process from YAML to Markdown parsing, similar to the revision parsing migration. The current `generateHighlights` function in `useRevisionApi.ts` uses YAML parsing to extract highlighted revisions from LLM responses.

## Current Implementation Analysis
- **Current Format**: YAML with `highlighted_revisions` array containing `index` and `highlightedText` fields
- **Current Parser**: Uses `YAML.parse()` and `extractFromCodeBlock()` utility
- **Current Prompt**: `highlightVocabularyPrompt.ts` generates YAML-structured prompts
- **Usage**: Called from `useWriteRevisionHandler.ts` in `_fetchAndApplyCombinedHighlighting()`

## Requirements
1. Parse entire raw LLM response as Markdown instead of YAML
2. Use mdast types for AST representation
3. Extract highlighted revision data from Markdown structure
4. Maintain error tolerance and validation
5. Preserve existing `HighlightedRevisionItem` interface (but remove index field)
6. Keep the same function signatures for minimal disruption
7. Preserve markdown formatting (bold tags) in highlighted text
8. Map highlights by array position instead of explicit index

## Implementation Steps

### 1. ✅ Create New Markdown Parser (`markdownHighlightParser.ts`)
**Status: COMPLETED**

Created `markdownHighlightParser.ts` in `pickvocab-client/components/app/write/` with enhanced features:

- **Markdown formatting preservation**: Enhanced `getNodeText` function to preserve `**bold**` and `*italic*` formatting
- **Simplified structure**: Removed index field - highlights are mapped by array position
- **AST traversal**: Uses unified/remark-parse for robust markdown parsing
- **Error tolerance**: Handles malformed responses gracefully

Key features:
```typescript
// Enhanced getNodeText preserves formatting
export function getNodeText(node: Content): string {
  if (node.type === 'text') return node.value;
  if (node.type === 'strong' && 'children' in node) {
    return `**${node.children.map(getNodeText).join('')}**`;
  }
  if (node.type === 'emphasis' && 'children' in node) {
    return `*${node.children.map(getNodeText).join('')}*`;
  }
  // ... rest of implementation
}

// Simplified return structure (no index field)
return highlightedRevisions; // Array of { highlightedText: string }
```

### 2. ✅ Create New Markdown Prompt File (`highlightVocabularyMarkdownPrompt.ts`)
**Status: COMPLETED**

Created `highlightVocabularyMarkdownPrompt.ts` with enhanced vocabulary matching guidelines:

**Enhanced Guidelines:**
- **Word Families**: Include different forms of the same word (e.g., 'analyze', 'analysis', 'analytical')
- **Inflections**: Include different grammatical forms (e.g., 'run' → 'running', 'ran'; 'child' → 'children')
- **Semantic matching**: Prioritize meaning over exact character matches
- **Case insensitivity**: Match regardless of case but preserve original casing

**Enhanced Examples:**
```markdown
## Revision 3
**Text:** The researchers analyzed the data thoroughly. Their analytical approach led to a comprehensive analysis.
**Vocabulary to Highlight:** - analyze

## Revision 4
**Text:** The child ran quickly, while the other children were running slowly.
**Vocabulary to Highlight:** - child, - run

**Output:**
# Revision 3
## Highlighted Text
The researchers **analyzed** the data thoroughly. Their **analytical** approach led to a comprehensive **analysis**.

# Revision 4
## Highlighted Text
The **child** **ran** quickly, while the other **children** were **running** slowly.
```

### 3. ✅ Update HighlightedRevisionItem Interface
**Status: COMPLETED**

**Removed index field** from `HighlightedRevisionItem` interface in `useRevisionApi.ts`:

```typescript
// Before
export interface HighlightedRevisionItem {
  index: number; // Corresponds to the originalIndex in RevisionHighlightInput
  highlightedText: string;
}

// After
export interface HighlightedRevisionItem {
  highlightedText: string;  // The revision text potentially with highlighting markup
}
```

### 4. ✅ Update Imports and Usage in `useRevisionApi.ts`
**Status: COMPLETED**

**Removed YAML dependencies and updated parsing logic:**

```typescript
// Removed imports
// import YAML from 'yaml';
// import { extractFromCodeBlock } from './revisionUtils';

// Added import
import { parseMarkdownHighlights } from './markdownHighlightParser';

// Updated generateHighlights function
async function generateHighlights(prompt: string): Promise<HighlightedRevisionItem[]> {
  // ... model setup code ...
  
  const result = await chatSource.sendMessage(prompt);
  
  // Parse entire response as Markdown (single step)
  const highlightedRevisions = parseMarkdownHighlights(result.message);
  
  if (!highlightedRevisions.length) {
    throw new Error('No valid highlighted revisions found in Markdown response');
  }
  
  // Simplified validation (no index check)
  return highlightedRevisions.filter((item): item is HighlightedRevisionItem =>
    typeof item.highlightedText === 'string' &&
    item.highlightedText.trim().length > 0
  );
}
```

### 5. ✅ Update Imports and Usage in `useWriteRevisionHandler.ts`
**Status: COMPLETED**

**Updated import and mapping logic:**

```typescript
// Updated import
import { highlightVocabularyMarkdownPrompt, type RevisionHighlightInput } from '~/components/app/write/highlightVocabularyMarkdownPrompt';

// Updated prompt usage
const prompt = highlightVocabularyMarkdownPrompt(revisionsToHighlight);

// Updated applyHighlightsToState function - map by array position
highlightedItems.forEach((highlightedItem, highlightIndex) => {
  // Map the highlight index to the corresponding revision in revisionsToHighlight
  if (highlightIndex < revisionsToHighlight.length) {
    const targetRevision = revisionsToHighlight[highlightIndex];
    const targetIndex = targetRevision.originalIndex;
    // ... rest of logic uses targetIndex for actual revision update
  }
});
```

### 6. ✅ Web Extension Compatibility
**Status: COMPLETED**

**Maintained backward compatibility:**
- Web extension still uses YAML-based approach with index field
- Kept `HighlightedRevisionItem` interface in web extension unchanged
- Web extension and client now have separate, compatible implementations

## Migration Results

### ✅ Completed Features
1. **Markdown parsing**: Full AST-based parsing with unified/remark-parse
2. **Formatting preservation**: Bold and italic markdown syntax preserved
3. **Enhanced vocabulary matching**: Word families, inflections, semantic matching
4. **Simplified mapping**: Array position-based mapping instead of explicit indices
5. **Error tolerance**: Robust error handling and validation
6. **Backward compatibility**: Web extension continues to work with YAML approach

### ✅ Key Improvements
1. **Better vocabulary detection**: Enhanced examples for word variations and inflections
2. **Preserved formatting**: Highlighted text retains `**bold**` markdown formatting
3. **Cleaner architecture**: Removed unnecessary index field and mapping complexity
4. **More robust parsing**: AST-based parsing handles edge cases better
5. **Enhanced prompts**: More detailed instructions for semantic matching

### ✅ Testing Verified
- Markdown parser correctly preserves bold formatting: `**highlighted text**`
- Array position mapping works correctly for revision updates
- Enhanced vocabulary matching guidelines improve LLM performance
- Error handling maintains system stability

## Final Implementation Status: ✅ COMPLETE

The highlighting process has been successfully migrated from YAML to Markdown parsing with the following enhancements:

1. **Enhanced vocabulary matching** with word families and inflections
2. **Preserved markdown formatting** in highlighted text
3. **Simplified mapping logic** using array positions
4. **Robust error handling** and validation
5. **Backward compatibility** with existing web extension

The migration maintains all existing functionality while providing better vocabulary detection and formatting preservation.

