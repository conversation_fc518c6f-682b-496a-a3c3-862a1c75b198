import type { SelectionPopup } from '~/lib/foliate-js/examples/selection-popup.js'

interface StyleConfig {
  spacing: number
  justify: boolean
  hyphenate: boolean
}

interface Book {
  metadata?: {
    title?: string | Record<string, string>
    author?: string | Record<string, string> | Array<string | Record<string, string>>
  }
  dir?: string
  toc?: any
  getCover?: () => Promise<Blob | null>
  getCalibreBookmarks?: () => Promise<any[]>
  transformTarget?: {
    addEventListener: (event: string, handler: (event: any) => void) => void
  }
}

interface FoliateView extends HTMLElement {
  open: (file: File | string) => Promise<void>
  addEventListener: (event: string, handler: (event: any) => void) => void
  isFixedLayout: boolean
  renderer: {
    setAttribute: (attr: string, value: string) => void
    setStyles?: (css: string) => void
    next: () => void
    spread?: string
    open?: (book: Book) => void
  }
  book: Book
  goLeft: () => void
  goRight: () => void
  goTo: (href: string) => Promise<void>
  goToFraction: (fraction: number) => void
  getSectionFractions: () => number[]
  addAnnotation: (annotation: any) => void
}

interface MenuGroup {
  element: HTMLElement
  select: (value: string) => void
}

interface Menu {
  element: HTMLElement
  groups: any
}

interface TOCView {
  element: HTMLElement
  setCurrentHref?: (href: string) => void
}

const getCSS = ({ spacing, justify, hyphenate }: StyleConfig): string => `
    @namespace epub "http://www.idpf.org/2007/ops";
    html {
        color-scheme: light dark;
    }
    /* https://github.com/whatwg/html/issues/5426 */
    @media (prefers-color-scheme: dark) {
        a:link {
            color: lightblue;
        }
    }
    p, li, blockquote, dd {
        line-height: ${spacing};
        text-align: ${justify ? 'justify' : 'start'};
        -webkit-hyphens: ${hyphenate ? 'auto' : 'manual'};
        hyphens: ${hyphenate ? 'auto' : 'manual'};
        -webkit-hyphenate-limit-before: 3;
        -webkit-hyphenate-limit-after: 2;
        -webkit-hyphenate-limit-lines: 2;
        hanging-punctuation: allow-end last;
        widows: 2;
    }
    /* prevent the above from overriding the align attribute */
    [align="left"] { text-align: left; }
    [align="right"] { text-align: right; }
    [align="center"] { text-align: center; }
    [align="justify"] { text-align: justify; }

    pre {
        white-space: pre-wrap !important;
    }
    aside[epub|type~="endnote"],
    aside[epub|type~="footnote"],
    aside[epub|type~="note"],
    aside[epub|type~="rearnote"] {
        display: none;
    }
`

const $ = (selector: string): HTMLElement | null => document.querySelector(selector)

const locales = 'en'
const percentFormat = new Intl.NumberFormat(locales, { style: 'percent' })
const listFormat = new Intl.ListFormat(locales, { style: 'short', type: 'conjunction' })

const formatLanguageMap = (x: string | Record<string, string> | undefined): string => {
  if (!x) return ''
  if (typeof x === 'string') return x
  const keys = Object.keys(x)
  return x[keys[0]]
}

const formatOneContributor = (contributor: string | Record<string, string>): string => 
  typeof contributor === 'string' ? contributor : formatLanguageMap(contributor?.name)

const formatContributor = (contributor: string | Record<string, string> | Array<string | Record<string, string>>): string => 
  Array.isArray(contributor)
    ? listFormat.format(contributor.map(formatOneContributor))
    : formatOneContributor(contributor)

export class Reader {
  #tocView: TOCView | null = null
  #selectionPopup: SelectionPopup | null = null
  menu: any = null
  view: FoliateView | null = null
  
  style: StyleConfig = {
    spacing: 1.4,
    justify: true,
    hyphenate: true,
  }
  annotations = new Map()
  annotationsByValue = new Map()

  constructor(
    private createTOCView: (toc: any, onNavigate: (href: string) => void) => TOCView,
    private createMenu: (config: any[]) => any,
    private SelectionPopupClass: new (onLookup: (text: string) => void) => SelectionPopup,
    private Overlayer: any
  ) {
    this.#initialize()
  }

  #initialize(): void {
    const sideBarButton = $('#side-bar-button')
    if (sideBarButton) {
      sideBarButton.addEventListener('click', () => {
        const overlay = $('#dimming-overlay')
        const sideBar = $('#side-bar')
        if (overlay && sideBar) {
          overlay.classList.add('show')
          sideBar.classList.add('show')
        }
      })
    }

    const dimmingOverlay = $('#dimming-overlay')
    if (dimmingOverlay) {
      dimmingOverlay.addEventListener('click', () => this.closeSideBar())
    }
    
    // Initialize selection popup
    this.#selectionPopup = new this.SelectionPopupClass((selectedText: string) => {
      this.#handleLookup(selectedText)
    })

    this.menu = this.createMenu([
      {
        name: 'layout',
        label: 'Layout',
        type: 'radio',
        items: [
          ['Paginated', 'paginated'],
          ['Scrolled', 'scrolled'],
        ],
        onclick: (value: string) => {
          this.view?.renderer.setAttribute('flow', value)
        },
      },
      {
        name: 'zoom',
        label: 'Zoom',
        type: 'radio',
        items: [
          ['Fit Width', 'fit-width'],
          ['Fit Page', 'fit-page'],
          ['50%', '0.5'],
          ['75%', '0.75'],
          ['100%', '1'],
          ['125%', '1.25'],
          ['150%', '1.5'],
          ['200%', '2'],
        ],
        onclick: (value: string) => {
          this.view?.renderer.setAttribute('zoom', value)
        },
      },
    ])
    this.menu.element.classList.add('menu')

    const menuButton = $('#menu-button')
    if (menuButton) {
      menuButton.append(this.menu.element)
      const button = menuButton.querySelector('button')
      if (button) {
        button.addEventListener('click', () =>
          this.menu?.element.classList.toggle('show'))
      }
    }
    this.menu.groups?.layout?.select('paginated')
    this.menu.groups?.zoom?.select('1')
  }

  closeSideBar(): void {
    const overlay = $('#dimming-overlay')
    const sideBar = $('#side-bar')
    if (overlay) overlay.classList.remove('show')
    if (sideBar) sideBar.classList.remove('show')
  }

  #handleLookup(selectedText: string): void {
    // Log selection info when lookup is clicked
    console.log('📖 Text Lookup:', {
      text: selectedText,
      length: selectedText.length,
      wordCount: selectedText.split(/\s+/).length,
    })
    
    // Simple lookup demo - in a real app, this would call a dictionary API
    const result = `Lookup for: "${selectedText}"\n\nThis is a demo lookup result. In a real application, this would show dictionary definitions, translations, or other lookup results.`
    alert(result)
  }

  async open(file: File | string): Promise<void> {
    this.view = document.createElement('foliate-view') as FoliateView
    document.body.append(this.view)
    
    await this.view.open(file)
    
    // Force single column/page layout
    if (!this.view.isFixedLayout && this.view.renderer) {
      // For reflowable content: force single column
      this.view.renderer.setAttribute('max-column-count', '1')
    } else if (this.view.isFixedLayout && this.view.renderer) {
      // For fixed-layout content: force single page
      this.view.renderer.spread = 'none'
      if (this.view.book && (this.view.book as any).rendition) {
        (this.view.book as any).rendition.spread = 'none'
      }
      this.view.renderer.open?.(this.view.book)
    }
    
    this.view.addEventListener('load', this.#onLoad.bind(this))
    this.view.addEventListener('relocate', this.#onRelocate.bind(this))

    const { book } = this.view
    if (book.transformTarget) {
      book.transformTarget.addEventListener('data', ({ detail }: any) => {
        detail.data = Promise.resolve(detail.data).catch((e: Error) => {
          console.error(new Error(`Failed to load ${detail.name}`, { cause: e }))
          return ''
        })
      })
    }
    
    if (this.view.renderer.setStyles) {
      this.view.renderer.setStyles(getCSS(this.style))
    }
    
    // Show/hide menu options based on layout type
    if (this.menu?.groups) {
      if (this.view.isFixedLayout) {
        // Fixed-layout: show zoom, hide layout options
        if (this.menu.groups.zoom?.element) this.menu.groups.zoom.element.style.display = 'block'
        if (this.menu.groups.layout?.element) this.menu.groups.layout.element.style.display = 'none'
      } else {
        // Reflowable: hide zoom, show layout options
        if (this.menu.groups.zoom?.element) this.menu.groups.zoom.element.style.display = 'none'
        if (this.menu.groups.layout?.element) this.menu.groups.layout.element.style.display = 'block'
      }
    }
    
    this.view.renderer.next()

    const headerBar = $('#header-bar') as HTMLElement
    const navBar = $('#nav-bar') as HTMLElement
    if (headerBar) headerBar.style.visibility = 'visible'
    if (navBar) navBar.style.visibility = 'visible'
    
    const leftButton = $('#left-button')
    const rightButton = $('#right-button')
    if (leftButton) leftButton.addEventListener('click', () => this.view?.goLeft())
    if (rightButton) rightButton.addEventListener('click', () => this.view?.goRight())

    const slider = $('#progress-slider') as HTMLInputElement
    if (slider && book.dir) {
      slider.dir = book.dir
      slider.addEventListener('input', (e: Event) => {
        const target = e.target as HTMLInputElement
        this.view?.goToFraction(parseFloat(target.value))
      })
      
      const tickMarks = $('#tick-marks')
      if (tickMarks) {
        for (const fraction of this.view.getSectionFractions()) {
          const option = document.createElement('option')
          option.value = fraction.toString()
          tickMarks.append(option)
        }
      }
    }

    document.addEventListener('keydown', this.#handleKeydown.bind(this))

    const title = formatLanguageMap(book.metadata?.title) || 'Untitled Book'
    document.title = title
    const sideBarTitle = $('#side-bar-title') as HTMLElement
    const sideBarAuthor = $('#side-bar-author') as HTMLElement
    if (sideBarTitle) sideBarTitle.innerText = title
    if (sideBarAuthor) sideBarAuthor.innerText = formatContributor(book.metadata?.author || '')
    
    if (book.getCover) {
      Promise.resolve(book.getCover()).then(blob => {
        if (blob) {
          const cover = $('#side-bar-cover') as HTMLImageElement
          if (cover) cover.src = URL.createObjectURL(blob)
        }
      })
    }

    const toc = book.toc
    if (toc) {
      this.#tocView = this.createTOCView(toc, (href: string) => {
        this.view?.goTo(href).catch((e: Error) => console.error(e))
        this.closeSideBar()
      })
      const tocView = $('#toc-view')
      if (tocView) tocView.append(this.#tocView.element)
    }

    // load and show highlights embedded in the file by Calibre
    if (book.getCalibreBookmarks) {
      const bookmarks = await book.getCalibreBookmarks()
      if (bookmarks) {
        const { fromCalibreHighlight } = await import('~/lib/foliate-js/epubcfi.js')
        for (const obj of bookmarks) {
          if (obj.type === 'highlight') {
            const value = fromCalibreHighlight(obj)
            const color = obj.style.which
            const note = obj.notes
            const annotation = { value, color, note }
            const list = this.annotations.get(obj.spine_index)
            if (list) list.push(annotation)
            else this.annotations.set(obj.spine_index, [annotation])
            this.annotationsByValue.set(value, annotation)
          }
        }
        this.view.addEventListener('create-overlay', (e: any) => {
          const { index } = e.detail
          const list = this.annotations.get(index)
          if (list) for (const annotation of list)
            this.view?.addAnnotation(annotation)
        })
        this.view.addEventListener('draw-annotation', (e: any) => {
          const { draw, annotation } = e.detail
          const { color } = annotation
          draw(this.Overlayer.highlight, { color })
        })
        this.view.addEventListener('show-annotation', (e: any) => {
          const annotation = this.annotationsByValue.get(e.detail.value)
          if (annotation.note) alert(annotation.note)
        })
      }
    }
  }

  #handleKeydown(event: KeyboardEvent): void {
    const k = event.key
    if (k === 'ArrowLeft' || k === 'h') this.view?.goLeft()
    else if(k === 'ArrowRight' || k === 'l') this.view?.goRight()
  }

  #handleTextSelection(doc: Document): void {
    const selection = doc.getSelection()
    if (!selection || selection.rangeCount === 0) {
      this.#selectionPopup?.hide()
      return
    }
    
    const selectedText = selection.toString().trim()
    if (selectedText.length === 0) {
      this.#selectionPopup?.hide()
      return
    }
    
    // Get the bounding rect of the selection
    const range = selection.getRangeAt(0)
    const rect = range.getBoundingClientRect()
    
    // Convert iframe coordinates to window coordinates
    const iframe = (doc.defaultView as any)?.frameElement as HTMLIFrameElement
    let adjustedRect = rect
    
    if (iframe) {
      const iframeRect = iframe.getBoundingClientRect()
      adjustedRect = {
        left: rect.left + iframeRect.left,
        top: rect.top + iframeRect.top,
        right: rect.right + iframeRect.left,
        bottom: rect.bottom + iframeRect.top,
        width: rect.width,
        height: rect.height,
        x: rect.x + iframeRect.left,
        y: rect.y + iframeRect.top,
        toJSON: () => ({})
      } as DOMRect
    }
    
    // Show the popup above the selection
    this.#selectionPopup?.show(adjustedRect, selectedText)
  }

  #onLoad({ detail: { doc } }: { detail: { doc: Document } }): void {
    doc.addEventListener('keydown', this.#handleKeydown.bind(this))
    
    // Handle text selection to show lookup popup
    doc.addEventListener('selectionchange', () => {
      // Use a small delay to ensure selection is finalized
      setTimeout(() => this.#handleTextSelection(doc), 10)
    })
    
    // Also handle mouseup to catch drag selections
    doc.addEventListener('mouseup', () => {
      setTimeout(() => this.#handleTextSelection(doc), 10)
    })
  }

  #onRelocate({ detail }: { detail: any }): void {
    const { fraction, location, tocItem, pageItem } = detail
    const percent = percentFormat.format(fraction)
    const loc = pageItem
      ? `Page ${pageItem.label}`
      : `Loc ${location.current}`
    const slider = $('#progress-slider') as HTMLInputElement
    if (slider) {
      slider.style.visibility = 'visible'
      slider.value = fraction.toString()
      slider.title = `${percent} · ${loc}`
    }
    if (tocItem?.href) this.#tocView?.setCurrentHref?.(tocItem.href)
  }
}