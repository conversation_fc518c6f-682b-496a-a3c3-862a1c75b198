<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog';
import Spinner from '~/components/app/utils/Spinner.vue';
import ExplanationView from '~/components/app/contextualMeaning/ExplanationView.vue';
import { reduceContext } from '~/utils/contextCard';
import { Dictionary } from 'pickvocab-dictionary';
import { useAppStore } from '~/stores/app';
import { useLLMStore } from '~/stores/llm';
import type { DictionarySource, WordInContextEntry } from 'pickvocab-dictionary';
import { RemoteWordInContextApi } from '~/api/wordInContext';
import type { CardId, BaseContextCard } from '~/utils/card';
import { useCreateContextCard } from '~/composables/useCardMutations';

const props = defineProps<{
  word: string;
  context: string;
  offset: number;
  open: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void;
  (e: 'addCard', wordEntry: WordInContextEntry, callback?: () => void): void;
}>();

const isLoading = ref(false);
const isCreatingCardState = ref(false);
const errorMessage = ref('');
const wordEntry = ref<WordInContextEntry | undefined>();
const isDetailed = ref(false);
const selectedSimpleViewLanguage = ref<string | undefined>('English');
const isSaved = ref(false);
const savedCardId = ref<CardId | null>(null);
const showDefinitionView = ref(true);

const appStore = useAppStore();
const llmStore = useLLMStore();

// TanStack Query mutation for proper cache management
const { mutate: createContextCardMutation, isPending: isCreatingCard } = useCreateContextCard();
const api = new RemoteWordInContextApi();

const dictionary = computed(() => {
  let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
  if (llmStore.activeUserModel) {
    sources = [llmStore.createDictionarySource(llmStore.activeUserModel), ...sources];
  }
  return new Dictionary(sources);
});

const cardUrl = computed(() => {
  if (!savedCardId.value) return '';
  return `/app/cards/${savedCardId.value}`;
});

watch(() => props.open, (val) => {
  if (val) {
    showDefinitionView.value = true; // Reset to definition view when modal opens
    handleLookup();
  } else {
    wordEntry.value = undefined;
    errorMessage.value = '';
  }
});

watch(() => isDetailed.value, () => {
  if (!props.word || !props.context || props.offset === undefined || !wordEntry.value) return;
  
  if (isDetailed.value) {
    if (wordEntry.value?.definition) return;
    refresh();
  } else {
    if (wordEntry.value?.definitionShort) return;
    refresh();
  }
});

// Watch for language changes
watch(selectedSimpleViewLanguage, () => {
  simpleLookupForLanguage(selectedSimpleViewLanguage.value);
});

async function handleLookup() {
  isLoading.value = true;
  wordEntry.value = undefined;
  errorMessage.value = '';
  try {
    const reduced = reduceContext(props.context, props.word, props.offset, 3);
    let base;
    if (isDetailed.value) {
      base = await dictionary.value.getMeaningInContext(reduced.selectedText, reduced.text, reduced.offset);
    } else {
      base = await dictionary.value.getMeaningInContextShort(reduced.selectedText, reduced.text, reduced.offset);
    }
    wordEntry.value = await api.create(base);
    selectedSimpleViewLanguage.value = 'English'; // Reset language to English on new lookup
  } catch (err: any) {
    console.error('Error in handleLookup:', err);
    errorMessage.value = err?.message || String(err);
  } finally {
    isLoading.value = false;
  }
}

async function simpleLookupForLanguage(language = 'English') {
  try {
    if (!wordEntry.value) return;
    
    if (language === 'English') {
      if (wordEntry.value?.definitionShort) return;
    } else {
      if (wordEntry.value?.definitionShort?.languages?.[language] && 
          wordEntry.value.definitionShort.languages[language].explanation) return;
    }
    
    await refresh(language);
  } catch (err: any) {
    console.error('Error in simpleLookupForLanguage:', err);
    errorMessage.value = err?.message || String(err);
  }
}

async function refresh(language = 'English') {
  if (!wordEntry.value) return;
  const oldEntry = wordEntry.value;
  wordEntry.value = undefined;
  isLoading.value = true;
  errorMessage.value = '';
  try {
    const reduced = reduceContext(props.context, props.word, props.offset, 3);
    let base;
    if (language === 'English') {
      if (isDetailed.value) {
        base = await dictionary.value.getMeaningInContext(reduced.selectedText, reduced.text, reduced.offset);
      } else {
        base = await dictionary.value.getMeaningInContextShort(reduced.selectedText, reduced.text, reduced.offset);
      }
    } else {
      if (isDetailed.value) throw new Error('Unsupported');
      base = await dictionary.value.getMeaningInContextShortForLanguage(oldEntry, language);
    }
    const newEntry = { ...oldEntry, ...base };
    wordEntry.value = await api.put(newEntry);
  } catch (err: any) {
    console.error('Error in refresh:', err);
    errorMessage.value = err?.message || String(err);
  } finally {
    isLoading.value = false;
  }
}

async function addCard(entry: WordInContextEntry, callback?: () => void) {
  isCreatingCardState.value = true;
  try {
    const baseCard: BaseContextCard = {
      wordInContext: entry
    };
    
    // Use TanStack Query mutation for proper cache management
    const card = await new Promise<ContextCard>((resolve, reject) => {
      createContextCardMutation(baseCard, {
        onSuccess: (createdCard) => resolve(createdCard),
        onError: (error) => reject(error)
      });
    });
    
    // Store card ID and show success state
    savedCardId.value = card.id;
    isSaved.value = true;
    showDefinitionView.value = false;
    
  } catch (error) {
    console.error('Error creating card:', error);
    errorMessage.value = `Failed to create card: ${error}`;
  } finally {
    isCreatingCardState.value = false;
    if (callback) callback();
  }
}

function backToDefinitionView() {
  showDefinitionView.value = true;
}

const llmModel = computed(() => {
  const id = wordEntry.value?.llm_model;
  return typeof id === 'number' ? llmStore.getModelById(id) : undefined;
});
</script>

<template>
  <Dialog v-model:open="props.open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-2xl w-full max-h-[90dvh] overflow-y-auto">
      <DialogClose />
      
      <!-- Error message -->
      <div v-if="errorMessage" class="my-4 text-red-600">{{ errorMessage }}</div>
      
      <!-- Definition view -->
      <div v-if="showDefinitionView">
        <ExplanationView
          :word="props.word"
          :word-entry="wordEntry"
          :llm-model="llmModel"
          :is-loading="isLoading"
          v-model:is-detailed="isDetailed"
          v-model:selected-simple-view-language="selectedSimpleViewLanguage"
          @add-card="addCard"
          @refresh="refresh"
          @simple-lookup-for-language="simpleLookupForLanguage"
          class="border-none !py-0 !px-4"
        />
      </div>
      
      <!-- Success view (after card creation) -->
      <div v-else class="p-8 text-center">
        <div class="flex flex-col items-center justify-center">
          <p class="text-lg font-semibold text-gray-800 mb-4">
            Card saved successfully!
          </p>
          <a :href="cardUrl" target="_blank" class="text-blue-600 hover:text-blue-800 mb-6">
            View card
          </a>
          <button type="button" @click="backToDefinitionView"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 text-center">
            Back to Definition
          </button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
</style> 