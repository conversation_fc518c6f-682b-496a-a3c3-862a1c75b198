<script setup lang="ts">
import { ref, toRef, watch, nextTick, type PropType, onMounted, watchEffect, onUnmounted } from 'vue'
// @ts-ignore
import IconChevronLeft from '@tabler/icons-vue/dist/esm/icons/IconChevronLeft.mjs'
// @ts-ignore
import IconChevronRight from '@tabler/icons-vue/dist/esm/icons/IconChevronRight.mjs'
// @ts-ignore
import IconList from '@tabler/icons-vue/dist/esm/icons/IconList.mjs' // Icon for TOC
// @ts-ignore
import IconPalette from '@tabler/icons-vue/dist/esm/icons/IconPalette.mjs' // Icon for theme toggle
// @ts-ignore
import IconArrowBackUp from '@tabler/icons-vue/dist/esm/icons/IconArrowBackUp.mjs' // Icon for Back to Library

import { useEpubReader } from '~/components/app/epubReader/useEpubReader'
import EpubBubbleMenu from '~/components/app/epubReader/EpubBubbleMenu.vue' // Import bubble menu
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose
} from '~/components/ui/sheet' // Shadcn-vue Sheet components
import LookupModal from '~/components/app/epubReader/LookupModal.vue' // Import LookupModal component

const props = defineProps({
  epubData: {
    type: Object as PropType<ArrayBuffer | null>,
    required: true,
  },
  filename: {
    type: String as PropType<string | null>,
    required: true,
  },
  fileHandleProp: {
    type: Object as PropType<FileSystemFileHandle | null>,
    default: null,
  },
})

const emit = defineEmits(['back-to-library'])

const epubContentArea = ref<HTMLDivElement | null>(null)
// const epubIframeElement = ref<HTMLIFrameElement | null>(null) // No longer needed, will use actualIframeElement from composable
const isTocOpen = ref(false) // State for TOC sidebar visibility

const lookupOpen = ref(false)
const lookupWord = ref('')
const lookupContext = ref('')
const lookupOffset = ref(0)

// Use the composable
const { 
  isReaderReady,
  prevChapter,
  nextChapter,
  tableOfContents, // Get TOC data
  navigateToTocItem, // Get TOC navigation function
  currentSelection, // Get current selection data
  clearSelection,   // Get clear selection function
  // book, // No longer directly used in this component, managed by coreLogic
  actualIframeElement, // Use this iframe element from the composable
  currentTheme,      // Get current theme
  selectTheme,       // Get theme selection function
  currentThemeBackgroundColor, // Get current theme background color
  refreshSelectionRect, // <-- Add this
  destroyEpubInstances, // Get destroy function for immediate cleanup
} = useEpubReader({
  epubDataProp: toRef(props, 'epubData'),
  filenameProp: toRef(props, 'filename'),
  epubContentArea,
  fileHandleProp: toRef(props, 'fileHandleProp'),
})

// watch(currentSelection, (newSelection) => {
//   // console.log('[StandaloneEpubReader] currentSelection changed:', JSON.stringify(newSelection));
// }, { deep: true });

const handleTocItemClick = (href: string) => {
  navigateToTocItem(href)
  isTocOpen.value = false // Close sidebar on navigation
}

const handleLookup = (event: { selectedText: string; text: string; offset: number }) => {
  lookupWord.value    = event.selectedText;
  lookupContext.value = event.text;
  lookupOffset.value  = event.offset;
  lookupOpen.value    = true;
  clearSelection();
}

const handleBubbleCopy = () => {
  // The copy action is handled within EpubBubbleMenu.vue
  // We just clear the selection here to hide the bubble menu.
  clearSelection()
}

const handleBackToLibrary = () => {
  destroyEpubInstances()
  emit('back-to-library')
}

async function addCard(wordEntry: any, callback?: () => void) {
  // Mirror ContextView.vue logic if needed, or emit to parent
  // For now, just call callback if provided
  if (callback) callback();
}

async function simpleLookupForLanguage(language: string) {
  // Placeholder for future logic
}

async function refresh(language: string) {
  // Placeholder for future logic
}
</script>

<template>
  <div class="epub-reader-container w-full h-full flex flex-col relative bg-neutral-700">
    <!-- EPUB Content Area -->
    <div
      ref="epubContentArea"
      class="epub-content-area flex-grow w-full mx-auto sm:max-w-2xl md:max-w-3xl lg:max-w-4xl xl:max-w-5xl 2xl:max-w-5xl h-full pb-[46px] p-0 m-0 rounded relative"
      :style="{ backgroundColor: currentThemeBackgroundColor }"
    >
      <!-- The iframe will be rendered here by epub.js -->
    </div>

    <!-- Bubble Menu -->
    <!-- {{ console.log('[StandaloneEpubReader] Props for EpubBubbleMenu:', { selectionData: currentSelection, iframeElement: actualIframeElement }) }} -->
    <EpubBubbleMenu 
      :selection-data="currentSelection" 
      :iframe-element="actualIframeElement"
      @lookup="handleLookup"
      @copy="handleBubbleCopy"
    />
    <LookupModal
      :word="lookupWord"
      :context="lookupContext"
      :offset="lookupOffset"
      v-model:open="lookupOpen"
      @addCard="addCard"
      @refresh="refresh"
      @simple-lookup-for-language="simpleLookupForLanguage"
    />
    
    <!-- Navigation Control Bar -->
    <div class="epub-control-bar fixed bottom-0 left-0 right-0 w-full bg-neutral-100 border-t border-neutral-300 text-neutral-700 flex items-center justify-between px-4 py-2 h-[46px] z-50">
      <!-- Left side: Back to Library Button & TOC Button -->
      <div class="flex items-center space-x-1">
        <button
          @click="handleBackToLibrary"
          class="control-button flex items-center justify-center w-10 h-10 rounded-full hover:bg-neutral-200 disabled:opacity-30 disabled:cursor-not-allowed transition-all duration-200 ease-in-out hover:text-sky-500"
          :disabled="!isReaderReady"
          title="Back to Library"
        >
          <IconArrowBackUp size="20" />
        </button>
        <Sheet v-model:open="isTocOpen">
          <SheetTrigger as-child>
            <button
              class="control-button flex items-center justify-center w-10 h-10 rounded-full hover:bg-neutral-200 disabled:opacity-30 disabled:cursor-not-allowed transition-all duration-200 ease-in-out hover:text-sky-500"
              :disabled="!isReaderReady || tableOfContents.length === 0"
              title="Table of Contents"
            >
              <IconList size="20" />
            </button>
          </SheetTrigger>
          <SheetContent side="left" class="w-[300px] sm:w-[350px] p-0 flex flex-col bg-neutral-100 text-neutral-800">
            <SheetHeader class="p-4 border-b border-neutral-200">
              <SheetTitle>Table of Contents</SheetTitle>
            </SheetHeader>
            <div class="toc-items-container flex-grow overflow-y-auto">
              <ul v-if="tableOfContents.length > 0" class="p-4 space-y-1">
                <li v-for="item in tableOfContents" :key="item.id">
                  <button
                    @click="handleTocItemClick(item.href)"
                    class="toc-item text-left w-full px-3 py-2 text-sm rounded-md hover:bg-neutral-200 focus:outline-none focus:bg-neutral-200 transition-colors duration-150 ease-in-out truncate"
                    :title="item.label"
                  >
                    {{ item.label }}
                  </button>
                  <!-- Basic recursive rendering for subitems if they exist -->
                  <ul v-if="item.subitems && item.subitems.length > 0" class="pl-4 mt-1 space-y-1">
                    <li v-for="subItem in item.subitems" :key="subItem.id">
                      <button
                        @click="handleTocItemClick(subItem.href)"
                        class="toc-item text-left w-full px-3 py-2 text-xs rounded-md hover:bg-neutral-200 focus:outline-none focus:bg-neutral-200 transition-colors duration-150 ease-in-out truncate text-neutral-600"
                        :title="subItem.label"
                      >
                        {{ subItem.label }}
                      </button>
                    </li>
                  </ul>
                </li>
              </ul>
              <p v-else class="p-4 text-sm text-gray-500">Table of contents is empty or not available.</p>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      <!-- Center: Navigation Controls Only -->
      <div class="absolute left-1/2 -translate-x-1/2 flex items-center space-x-1">
        <button
          @click="prevChapter"
          class="control-button flex items-center justify-center w-10 h-10 rounded-full hover:bg-neutral-200 disabled:opacity-30 disabled:cursor-not-allowed transition-all duration-200 ease-in-out hover:text-sky-500"
          :disabled="!isReaderReady"
          title="Previous Chapter"
        >
          <IconChevronLeft size="20" />
        </button>
        
        <button
          @click="nextChapter"
          class="control-button flex items-center justify-center w-10 h-10 rounded-full hover:bg-neutral-200 disabled:opacity-30 disabled:cursor-not-allowed transition-all duration-200 ease-in-out hover:text-sky-500"
          :disabled="!isReaderReady"
          title="Next Chapter"
        >
          <IconChevronRight size="20" />
        </button>
      </div>

      <!-- Right side: Theme Toggle -->
      <button
        @click="selectTheme(currentTheme === 'default' ? 'rust' : 'default')"
        class="px-3 py-1.5 rounded-md text-sm font-medium hover:bg-neutral-200 transition-all duration-200 ease-in-out flex items-center gap-1.5"
        :title="currentTheme === 'default' ? 'Switch to Rust Theme' : 'Switch to Default Theme'"
        :disabled="!isReaderReady"
      >
        <IconPalette size="16" class="flex-shrink-0" />
        {{ currentTheme === 'default' ? 'Default' : 'Rust' }}
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for the epub reader container or controls if needed */
.epub-reader-container {
  /* background-color: #f3f4f6; */ /* Example: Light gray background for the whole container */
}

.epub-content-area {
  /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */ /* Example: Subtle shadow for content area */
}

.control-button:disabled {
  /* color: #9ca3af; */ /* Lighter color for disabled icons */
}

.toc-item {
  /* color: #374151; */ /* Darker text for TOC items for better readability */
}

.toc-item:hover {
  /* background-color: #e5e7eb; */ /* Subtle hover for TOC items */
}
</style>
