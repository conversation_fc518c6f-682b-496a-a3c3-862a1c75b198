import { ref, watch, onMounted, onUnmounted, nextTick, computed, type Ref, watchEffect } from 'vue';
import type { Contents, Location } from 'epubjs'; // Specific types from epubjs
import { useEpubReadingProgress } from './useEpubReadingProgress';
import { useEpubCoreLogic, type TocItem } from './useEpubCoreLogic';
import { useEpubThemeManager, type ThemesCollection } from './useEpubThemeManager';
import { useEpubInteractionManager, type SelectionData } from './useEpubInteractionManager';
import { useEpubNavigationHandler } from './useEpubNavigationHandler';
import { useRecentBooks } from '~/components/app/epubReader/useRecentBooks'; // Added
import type { RecentBookHandleEntry } from '~/components/app/epubReader/recentBooksDB'; // Added

interface EpubReaderOptions {
  epubDataProp: Ref<ArrayBuffer | null>;
  filenameProp: Ref<string | null>;
  epubContentArea: Ref<HTMLDivElement | null>;
  fileHandleProp?: Ref<FileSystemFileHandle | null>; // Added
}

const THEME_RULES: ThemesCollection = {
  default: {
    'body, body.defaultTheme': {
      'font-family': '"Spectral", "Libre Baskerville", Georgia, serif',
      'font-size': '1.1em',
      'line-height': '1.6',
      'padding': '0',
      'margin': '0',
      'color': '#333333 !important',
      'background-color': '#FFFFFF !important',
      'background': '#FFFFFF !important'
    },
    'p': { 'margin': '0.5em 0', 'text-align': 'justify' },
    'h1': { 'font-size': '1.8em', 'line-height': '1.4', 'margin': '1em 0 0.5em' },
    'h2': { 'font-size': '1.5em', 'margin': '0.8em 0 0.4em' },
    'h3': { 'font-size': '1.2em', 'margin': '0.6em 0 0.3em' }
  },
  rust: {
    'body, body.rustTheme': {
      'font-family': '"Spectral", "Libre Baskerville", Georgia, serif',
      'font-size': '1.1em',
      'line-height': '1.6',
      'padding': '0',
      'margin': '0',
      'color': '#333333 !important',
      'background-color': '#E1E1DB !important',
      'background': '#E1E1DB !important'
    },
    'p': { 'margin': '0.5em 0', 'text-align': 'justify' },
    'h1': { 'font-size': '1.8em', 'line-height': '1.4', 'margin': '1em 0 0.5em' },
    'h2': { 'font-size': '1.5em', 'margin': '0.8em 0 0.4em' },
    'h3': { 'font-size': '1.2em', 'margin': '0.6em 0 0.3em' }
  }
};

export function useEpubReader({ epubDataProp, filenameProp, epubContentArea, fileHandleProp }: EpubReaderOptions) {
  const { addOrUpdateRecentBook } = useRecentBooks();

  const coreLogic = useEpubCoreLogic({
    epubDataProp,
    epubContentArea,
    themeRules: THEME_RULES,
    onIframeReady: (iframe, view) => {
      if (interactionManager) {
        interactionManager.initializeInteractionListeners();
      }
      if (readingProgress && coreLogic.actualIframeElement.value) {
        readingProgress.reinitializeScrollListener();
      }
    },
    onRelocated: (location, renditionInstance) => {
      nextTick(() => {
        readingProgress?.saveProgress('relocated_nextTick');
      });
    },
    onRendered: (section, view) => {
      // Placeholder for future actions
    },
    onBookNavigationLoaded: (toc, spine) => {
      // Placeholder for future actions
    },
    onBookInitFailed: (error) => {
        console.error("[useEpubReader] Orchestrator: Book initialization failed", error);
    }
  });

  const navigationHandler = useEpubNavigationHandler({
    rendition: coreLogic.rendition as any,
    book: coreLogic.book as any,
    currentChapterIndex: coreLogic.currentChapterIndex,
    spineItems: coreLogic.spineItems,
    epubContentArea: epubContentArea,
  });

  const interactionManager = useEpubInteractionManager({
    book: coreLogic.book as any,
    rendition: coreLogic.rendition as any,
    actualIframeElement: coreLogic.actualIframeElement,
    onPrev: navigationHandler.prevChapter,
    onNext: navigationHandler.nextChapter,
  });

  const themeManager = useEpubThemeManager({
    rendition: coreLogic.rendition as any,
    actualIframeElement: coreLogic.actualIframeElement,
    definedThemes: THEME_RULES,
  });

  const readingProgress = useEpubReadingProgress({
    book: coreLogic.book as any,
    rendition: coreLogic.rendition as any,
    filenameProp,
    epubDataProp,
    actualIframeElement: coreLogic.actualIframeElement,
  });

  const destroyEpubInstances = () => {
    // console.log('[useEpubReader] Destroying EPUB instances...'); // Optional: keep a very simple log
    try {
      readingProgress.destroyProgressTracking();
    } catch (e) {
      console.warn('[useEpubReader] Error destroying progress tracking:', e);
    }
    
    try {
      navigationHandler.destroyNavigationListeners();
    } catch (e) {
      console.warn('[useEpubReader] Error destroying navigation listeners:', e);
    }
    
    try {
      interactionManager.destroyInteractionListeners();
    } catch (e) {
      console.warn('[useEpubReader] Error destroying interaction listeners:', e);
    }
    
    try {
      coreLogic.destroyCore(); 
    } catch (e) {
      console.warn('[useEpubReader] Error destroying core logic:', e);
    }
    
    try {
      if (epubContentArea.value) {
          epubContentArea.value.innerHTML = '';
      }
    } catch (e) {
      console.warn('[useEpubReader] Error clearing content area:', e);
    }
  };

  const initializeReader = async () => {
    // Check if we're in the middle of destroying
    if (coreLogic.isDestroying?.value) {
      console.warn("[useEpubReader] initializeReader: Skipping initialization during cleanup.");
      return;
    }
    
    // console.log(`[useEpubReader] Initializing reader for: ${filenameProp.value}`); // Optional: keep simple log
    if (epubContentArea.value) {
        epubContentArea.value.innerHTML = ''; 
    }
    
    if (!epubDataProp.value || !epubContentArea.value || !filenameProp.value) {
      console.warn("[useEpubReader] initializeReader: Missing essential props.");
      return;
    }
    
    if (epubContentArea.value) {
        epubContentArea.value.style.opacity = '1';
    }

    readingProgress.initializeProgressTracking(); 

    const coreSetupSuccess = await coreLogic.initializeBookAndRendition();
    if (!coreSetupSuccess) {
      console.error("[useEpubReader] Core logic initialization failed. Aborting reader initialization.");
      return;
    }

    if (coreLogic.rendition.value) {
        themeManager.initializeThemeManager(); 
    } else {
        console.warn("[useEpubReader] Rendition not available for theme manager initialization.");
    }
    
    navigationHandler.initializeNavigationListeners();

    if (coreLogic.rendition.value) {
        coreLogic.rendition.value.off('selected', interactionManager.onRenditionSelected);
        coreLogic.rendition.value.on('selected', interactionManager.onRenditionSelected);
    } else {
        console.warn("[useEpubReader] Rendition not available to attach 'selected' event listener.");
    }

    let cfiToDisplay: string | undefined = undefined;
    if (coreLogic.book.value) {
      await readingProgress.generateBookId();
      if (readingProgress.bookId.value) {
        const savedProgress = readingProgress.loadProgress();
        if (savedProgress?.cfi) {
          cfiToDisplay = savedProgress.cfi;
        }
      }
    } else {
      console.warn("[useEpubReader] Book not available for generating bookId for progress.");
    }
    
    await coreLogic.displayContent(cfiToDisplay);
    // console.log('[useEpubReader] Book initialized and content displayed.'); // Optional: keep simple log
  };

  watch([epubDataProp, filenameProp], async ([newEpubData, newFilename], [oldEpubData, oldFilename]) => {
    // console.log(`[useEpubReader] Watcher: Data changed. New: ${newFilename}, Old: ${oldFilename}`); // Optional
    
    if (newEpubData && newFilename) {
      if (!(newEpubData instanceof ArrayBuffer)) {
        console.error('[useEpubReader] Invalid EPUB data format:', typeof newEpubData, 'Expected ArrayBuffer.');
        return;
      }
      if (newEpubData.byteLength < 100) { 
        console.warn('[useEpubReader] Suspiciously small EPUB file:', newEpubData.byteLength, 'bytes. Filename:', newFilename);
      }

      try {
        destroyEpubInstances();
        
        if (epubContentArea.value) {
          await initializeReader();
        } else {
          console.warn('[useEpubReader] epubContentArea is NOT available. Waiting for nextTick...');
          await nextTick();
          if (epubContentArea.value) {
            await initializeReader();
          } else {
            console.error('[useEpubReader] CRITICAL: epubContentArea still not available after nextTick. Cannot initialize reader.');
          }
        }
      } catch (e) {
        console.error('[useEpubReader] CRITICAL ERROR during watcher re-initialization:', e);
      }
    } else if (!newEpubData || !newFilename) {
      // console.warn('[useEpubReader] Watcher: newEpubData or newFilename is null/undefined. Destroying instances.'); // Optional
      destroyEpubInstances();
    }
  }, { immediate: true, deep: true });

  onMounted(() => {
    window.addEventListener('resize', coreLogic.handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', coreLogic.handleResize);
    destroyEpubInstances();
  });

  watchEffect(async () => {
    const handle = fileHandleProp?.value;
    const currentBookId = readingProgress.bookId.value;
    const currentFilename = filenameProp.value; 
    const bookInstance = coreLogic.book.value;

    // Allow recent book creation even when handle is null (fallback browsers)
    if (currentBookId && currentFilename && bookInstance && bookInstance.packaging?.metadata) {
      const title = bookInstance.packaging.metadata.title || currentFilename;
      let coverImageAsDataUrl: string | undefined = undefined;

      if (bookInstance.coverUrl) {
        try {
          const blobUrl = await bookInstance.coverUrl();
          if (blobUrl) {
            // Fetch the blob content
            const response = await fetch(blobUrl);
            const blob = await response.blob();
            
            // Convert blob to base64 data URL
            coverImageAsDataUrl = await new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onloadend = () => resolve(reader.result as string);
              reader.onerror = reject;
              reader.readAsDataURL(blob);
            });
            URL.revokeObjectURL(blobUrl); // Clean up the temporary blob URL
          }
        } catch (coverError) {
          console.warn('[useEpubReader] Could not extract or convert cover image:', coverError);
          // It's okay if cover extraction/conversion fails, proceed without it
        }
      }

      // For fallback browsers, prepare file data for storage
      let fileData: ArrayBuffer | undefined;
      let fileSize: number | undefined;
      
      if (!handle && epubDataProp.value) {
        // This is a fallback browser - store the file data
        fileSize = epubDataProp.value.byteLength;
        
        // Import storage utilities
        const { canStoreFile, cleanupOldBooks, STORAGE_LIMITS } = await import('~/components/app/epubReader/recentBooksDB');
        
        if (fileSize <= STORAGE_LIMITS.MAX_FILE_SIZE) {
          const canStore = await canStoreFile(fileSize);
          if (canStore) {
            fileData = epubDataProp.value.slice(); // Create a copy
          } else {
            // Try cleanup and check again
            await cleanupOldBooks();
            const canStoreAfterCleanup = await canStoreFile(fileSize);
            if (canStoreAfterCleanup) {
              fileData = epubDataProp.value.slice();
            } else {
              console.warn(`[useEpubReader] Cannot store file ${currentFilename} - would exceed storage limits`);
            }
          }
        } else {
          console.warn(`[useEpubReader] File ${currentFilename} too large (${Math.round(fileSize / 1024 / 1024)}MB) for storage`);
        }
      }

      const entry: Omit<RecentBookHandleEntry, 'lastOpened'> & { id: string, coverImage?: string } = {
        id: currentBookId,
        fileHandle: handle || null,
        filename: currentFilename,
        title: title,
        coverImage: coverImageAsDataUrl,
        supportsFileSystemAccess: handle !== null,
        fileData: fileData,
        fileSize: fileSize,
      };

      try {
        // console.log('[useEpubReader] Attempting to add/update recent book:', entry); // Optional
        await addOrUpdateRecentBook(entry);
        // console.log('[useEpubReader] Successfully added/updated recent book.'); // Optional
      } catch (error) {
        console.error('[useEpubReader] Failed to add recent book:', error);
      }
    }
  });

  return {
    isReaderReady: coreLogic.isReaderReady,
    tableOfContents: coreLogic.tableOfContents,
    actualIframeElement: coreLogic.actualIframeElement, 
    currentChapterIndex: coreLogic.currentChapterIndex, 
    spineItems: coreLogic.spineItems, 
    currentSelection: interactionManager.currentSelection,
    clearSelection: interactionManager.clearSelection,
    prevChapter: navigationHandler.prevChapter,
    nextChapter: navigationHandler.nextChapter,
    navigateToTocItem: navigationHandler.navigateToTocItem,
    currentTheme: themeManager.currentTheme,
    selectTheme: themeManager.selectTheme,
    currentThemeBackgroundColor: themeManager.currentThemeBackgroundColor,
    refreshSelectionRect: interactionManager.refreshSelectionRect,
    destroyEpubInstances,
  };
}