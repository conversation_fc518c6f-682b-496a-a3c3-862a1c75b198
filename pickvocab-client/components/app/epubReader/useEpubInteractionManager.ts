import { ref, type Ref } from 'vue';
import { useSwipe, type UseSwipeDirection } from '@vueuse/core';
import type { Book, Rendition, Contents } from 'epubjs'; // Assuming Contents is the type for rendition event callbacks

/**
 * Data structure for the currently selected text and its context.
 */
export interface SelectionData {
  selectedText: string;
  rect: DOMRect | null; // Position of the selection
  cfiRange: string; // The ePub CFI range of the selection
  fullContextText: string; // Full text of the paragraph/section containing the selection
  offsetInContext: number; // Character offset of the selection within the fullContextText
}

/**
 * Options for initializing the useEpubInteractionManager composable.
 */
export interface EpubInteractionManagerOptions {
  /** Reactive reference to the ePub.js Book instance. */
  book: Ref<Book | null>;
  /** Reactive reference to the ePub.js Rendition instance. */
  rendition: Ref<Rendition | null>;
  /** Reactive reference to the actual iframe element of the EPUB content. */
  actualIframeElement: Ref<HTMLIFrameElement | null>;
  /** Callback to navigate to the previous chapter/page. */
  onPrev: () => void;
  /** Callback to navigate to the next chapter/page. */
  onNext: () => void;
  /** Optional threshold for swipe gesture detection (pixels). */
  swipeThreshold?: number;
}

/**
 * Finds the closest block-level parent element for a given DOM node.
 * @param node The starting DOM node.
 * @param contentDocument The document object of the content (e.g., iframe's document).
 * @param contentsWindow The window object of the content (e.g., iframe's window).
 * @returns The closest block-level HTMLElement or null if not found.
 */
function findClosestBlockElement(node: Node | null, contentDocument: Document, contentsWindow: Window): HTMLElement | null {
  // Validate window is still valid
  if (!contentsWindow || contentsWindow.closed || !contentDocument) {
    return null;
  }
  
  let current: Node | null = node;
  while (current && current !== contentDocument.body && current !== contentDocument.documentElement) {
    if (current.nodeType === Node.ELEMENT_NODE) {
      const el = current as HTMLElement;
      const tagName = el.tagName.toLowerCase();
      
      // Check if element is still connected to avoid getComputedStyle errors
      if (!el.isConnected) {
        return null;
      }
      
      try {
        const display = contentsWindow.getComputedStyle(el).display;
        if (tagName === 'p' || display === 'block' ||
            ['div', 'article', 'section', 'li', 'dd', 'dt', 'blockquote', 'header', 'footer', 'aside', 'main', 'nav'].includes(tagName) ||
            tagName.match(/^h[1-6]$/)) {
          return el;
        }
      } catch (error) {
        // getComputedStyle failed, element likely detached
        return null;
      }
    }
    current = current.parentNode;
  }
  return null;
}

/**
 * Gets the raw and normalized selected text from a Selection object.
 * @param selection The DOM Selection object.
 * @returns An object with raw and normalized text, or null if no selection.
 */
function getNormalizedSelectedText(selection: Selection | null): { raw: string; normalized: string } | null {
  if (!selection) return null;
  const raw = selection.toString().trim(); // Keep original trim behavior
  if (raw.length === 0) return null;
  const normalized = raw.replace(/\s+/g, ' ').trim();
  return { raw, normalized };
}

/**
 * Gets the Range and DOMRect from a Selection object.
 * @param selection The DOM Selection object.
 * @returns An object with range and rect, or null if no valid range.
 */
function getSelectionDetails(selection: Selection): { range: Range; rect: DOMRect } | null {
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    return { range, rect };
  }
  return null;
}

/**
 * Calculates the character offset of a normalized selected text within a raw context text.
 * @param rawContextText The raw text of the context (e.g., paragraph).
 * @param normalizedSelectedText The selected text, already normalized (whitespace collapsed to single spaces, trimmed).
 * @returns The character offset, or -1 if not found.
 */
function calculateSelectionOffset(rawContextText: string, normalizedSelectedText: string): number {
  if (!rawContextText || !normalizedSelectedText) return -1;
  const normalizedContextText = rawContextText.replace(/\s+/g, ' ').trim();
  return normalizedContextText.indexOf(normalizedSelectedText);
}

/**
 * Extracts the full context text (potentially spanning multiple paragraphs) for a given selection range.
 * @param range The DOM Range of the selection.
 * @param contentDocument The document object of the content.
 * @param contentsWindow The window object of the content.
 * @returns The concatenated text content of the involved block elements, or an empty string.
 */
function extractContextTextFromRange(range: Range, contentDocument: Document, contentsWindow: Window): string {
  let rawParagraphText = '';
  // contextFound is implicitly handled by whether rawParagraphText gets populated.

  const startNode = range.startContainer;
  const endNode = range.endContainer;
  const startBlock = findClosestBlockElement(startNode, contentDocument, contentsWindow);
  const endBlock = findClosestBlockElement(endNode, contentDocument, contentsWindow);

  if (startBlock) {
    if (!endBlock || startBlock === endBlock) {
      rawParagraphText = startBlock.textContent || '';
    } else { // Distinct startBlock and endBlock
      const collectedTexts: string[] = [];
      let commonAncestor: Node | null = startBlock;
      while (commonAncestor && !commonAncestor.contains(endBlock)) {
        commonAncestor = commonAncestor.parentNode;
      }

      const walkerRoot = (commonAncestor && commonAncestor !== contentDocument && commonAncestor !== contentDocument.documentElement && commonAncestor !== contentDocument.body)
                         ? commonAncestor
                         : (contentDocument.body || contentDocument.documentElement);

      if (!walkerRoot) {
        console.error("[InteractionManager] Cannot determine a root for TreeWalker. Falling back to start/end blocks only.");
        collectedTexts.push(startBlock.textContent || '');
        if (startBlock !== endBlock) {
          collectedTexts.push(endBlock.textContent || '');
        }
        rawParagraphText = collectedTexts.join('\n\n');
      } else {
        const walker = contentDocument.createTreeWalker(
          walkerRoot,
          NodeFilter.SHOW_ELEMENT,
          {
            acceptNode: (node) => {
              const el = node as HTMLElement;
              const tagName = el.tagName.toLowerCase();
              
              // Check if element is still connected and window is valid
              if (!el.isConnected || !contentsWindow || contentsWindow.closed) {
                return NodeFilter.FILTER_SKIP;
              }
              
              try {
                const display = contentsWindow.getComputedStyle(el).display;
                if (tagName === 'p' || display === 'block' ||
                    ['div', 'article', 'section', 'li', 'dd', 'dt', 'blockquote', 'header', 'footer', 'aside', 'main', 'nav'].includes(tagName) ||
                    tagName.match(/^h[1-6]$/)) {
                  return NodeFilter.FILTER_ACCEPT;
                }
              } catch (error) {
                // getComputedStyle failed, skip this node
                return NodeFilter.FILTER_SKIP;
              }
              return NodeFilter.FILTER_SKIP;
            }
          }
        );

        let currentNodeWalker: Node | null;
        let collecting = false;
        while (currentNodeWalker = walker.nextNode()) {
          if (currentNodeWalker === startBlock) {
            collecting = true;
          }
          if (collecting) {
            collectedTexts.push(currentNodeWalker.textContent || '');
          }
          if (currentNodeWalker === endBlock) {
            break;
          }
        }

        if (collectedTexts.length > 0) {
          rawParagraphText = collectedTexts.join('\n\n');
        } else {
          console.warn("[InteractionManager] Multi-paragraph TreeWalker yielded no text. Using startBlock text or start/end if different.");
          if (startBlock === endBlock) {
            rawParagraphText = startBlock.textContent || '';
          } else {
            rawParagraphText = (startBlock.textContent || '') + '\n\n' + (endBlock.textContent || '');
          }
        }
      }
    }
  } else { // No startBlock found
    const parentEl = range.startContainer.parentElement;
    if (parentEl && parentEl !== contentDocument.body && parentEl !== contentDocument.documentElement) {
      rawParagraphText = parentEl.textContent || '';
    } else {
      console.warn('[InteractionManager] Could not determine any block context for selection (startBlock not found).');
    }
  }
  return rawParagraphText;
}


/**
 * Composable for managing user interactions within the EPUB reader,
 * including text selection, swipe navigation, and iframe click handling.
 */
export function useEpubInteractionManager(options: EpubInteractionManagerOptions) {
  const {
    book,
    rendition,
    actualIframeElement,
    onPrev,
    onNext,
    swipeThreshold = 50,
  } = options;

  const currentSelection = ref<SelectionData | null>(null);
  let stopSwipeListener: (() => void) | null = null;
  let iframeClickListener: ((event: MouseEvent) => void) | null = null;

  /**
   * Handles clicks within the EPUB iframe.
   * If a click occurs outside of any selected text, it clears the current selection.
   */
  const handleIframeClick = (event: MouseEvent) => {
    if (actualIframeElement.value?.contentWindow) {
      const selectionInIframe = actualIframeElement.value.contentWindow.getSelection()?.toString().trim() || '';
      if (selectionInIframe === '' && currentSelection.value !== null) {
        currentSelection.value = null;
      }
    }
  };

  /**
   * Adds a click listener to the EPUB iframe's document.
   */
  const addIframeClickListener = () => {
    if (!actualIframeElement.value?.contentDocument || iframeClickListener) return;

    // Define the listener function
    const listener = (event: MouseEvent) => handleIframeClick(event);
    
    actualIframeElement.value.contentDocument.addEventListener('click', listener);
    iframeClickListener = listener; // Store the bound listener
  };

  /**
   * Removes the click listener from the EPUB iframe's document.
   */
  const removeIframeClickListener = () => {
    if (actualIframeElement.value?.contentDocument && iframeClickListener) {
      actualIframeElement.value.contentDocument.removeEventListener('click', iframeClickListener);
      iframeClickListener = null;
    }
  };

  /**
   * Sets up swipe navigation listeners on the EPUB iframe's document element.
   */
  const setupSwipeNavigation = () => {
    if (stopSwipeListener) { // Clear any existing listener
      stopSwipeListener();
      stopSwipeListener = null;
    }

    if (actualIframeElement.value?.contentDocument?.documentElement) {
      const { stop } = useSwipe(
        actualIframeElement.value.contentDocument.documentElement,
        {
          threshold: swipeThreshold,
          passive: true, // Recommended for performance
          onSwipeEnd: (e: TouchEvent, direction: UseSwipeDirection) => {
            if (direction === 'right') {
              onPrev();
            } else if (direction === 'left') {
              onNext();
            }
          },
        }
      );
      stopSwipeListener = stop; // Store the stop function to allow cleanup
    }
  };

  /**
   * Clears the swipe navigation listeners.
   */
  const clearSwipeNavigation = () => {
    if (stopSwipeListener) {
      stopSwipeListener();
      stopSwipeListener = null;
    }
  };

  /**
   * Handles the 'selected' event from the ePub.js rendition.
   * Extracts selection data and updates the `currentSelection` ref.
   * @param cfiRange The CFI range of the selection.
   * @param contents The ePub.js Contents object for the selection event.
   */
  const onRenditionSelected = async (cfiRange: string | null, contents: Contents) => {
    // Add validation to prevent processing when iframe is destroyed
    if (!actualIframeElement.value || 
        !actualIframeElement.value.contentWindow || 
        !actualIframeElement.value.contentDocument ||
        actualIframeElement.value.contentWindow.closed) {
      return;
    }
    
    // Safari-specific validation: ensure iframe has proper permissions
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    if (isSafari) {
      const iframe = actualIframeElement.value;
      const sandbox = iframe.getAttribute('sandbox');
      if (!sandbox || !sandbox.includes('allow-scripts')) {
        console.warn('[InteractionManager] Safari iframe missing allow-scripts permission, selection may not work properly');
        return;
      }
    }
    
    if (cfiRange && contents.window && book.value) {
      const selectionInWindow = contents.window.getSelection();
      const normTextResult = getNormalizedSelectedText(selectionInWindow);

      if (!normTextResult) {
        currentSelection.value = null;
        // console.log('[InteractionManager] No valid text selected or selection is empty, currentSelection cleared.');
        return;
      }
      const { raw: selectedTextInstance, normalized: normalizedSelectedText } = normTextResult;
      // console.log('[InteractionManager] Raw selected text instance:', selectedTextInstance);
      // console.log('[InteractionManager] Normalized selected text instance:', normalizedSelectedText);

      // We know selectionInWindow is not null here due to normTextResult check
      const selDetails = getSelectionDetails(selectionInWindow!);
      
      if (!selDetails) {
        currentSelection.value = null;
        // console.log('[InteractionManager] Selection range not valid, currentSelection cleared.');
        return;
      }
      const { range, rect } = selDetails;
          
      let fullContextText = '';
      let offsetInContext = -1;
      // rawParagraphText, contextFound, and contentDocument are now handled within extractContextTextFromRange or passed to it.

      try {
        const rawParagraphText = extractContextTextFromRange(range, contents.document, contents.window);

        if (rawParagraphText) { // Check if extractContextTextFromRange returned something
          fullContextText = rawParagraphText;
          // normalizedSelectedText is already defined from earlier
          offsetInContext = calculateSelectionOffset(rawParagraphText, normalizedSelectedText);
          
          // Debugging logs (can be removed or made conditional later)
          // console.log("Normalized Selected Text for offset:", normalizedSelectedText.substring(0,100));
          // console.log("Raw Paragraph Text for offset:", rawParagraphText.substring(0,100));
          // console.log("Calculated Offset:", offsetInContext);

          if (offsetInContext === -1) {
            const normalizedContextForLog = rawParagraphText.replace(/\s+/g, ' ').trim();
            console.warn(
              '[InteractionManager] Selected text not found in determined context.',
              'NormContext:', normalizedContextForLog.substring(0, 100),
              'NormSelected:', normalizedSelectedText.substring(0, 100),
              'OrigContext:', rawParagraphText.substring(0, 100),
              'OrigSelected:', selectedTextInstance.substring(0, 100)
            );
          } else {
            // console.log('[InteractionManager] Offset calculated successfully:', offsetInContext);
          }
        } else { // This 'else' corresponds to 'if (rawParagraphText)'
          console.warn('[InteractionManager] No context text extracted, `fullContextText` will be empty.');
          fullContextText = ''; // Ensure it's empty if extractContextTextFromRange returned nothing
          offsetInContext = -1;
        }

          } catch (error) {
            console.error('Error getting full context text for selection:', error);
            fullContextText = ''; // Ensure it's empty on error
            offsetInContext = -1;
          }
          
          const selectionData: SelectionData = {
            selectedText: selectedTextInstance,
            rect,
            cfiRange,
            fullContextText: fullContextText,
            offsetInContext,
          };
          currentSelection.value = selectionData;
          // console.log('[InteractionManager] currentSelection updated:', selectionData);
    // The inner else blocks for (!selDetails and !normTextResult) were handled by early returns.
    // Now, correctly close the main if (cfiRange && contents.window && book.value) block.
    } else {
      // This is the case where cfiRange, contents.window, or book.value was initially invalid.
      currentSelection.value = null;
      // console.log('[InteractionManager] CFI range null or missing dependencies, currentSelection cleared. CFI:', cfiRange, 'Contents Window:', !!contents?.window, 'Book:', !!book?.value);
    }
}; // End of onRenditionSelected function

  
  /**
   * Clears the current text selection.
   */
  const clearSelection = () => {
    currentSelection.value = null;
    // Optionally, try to clear the actual browser selection within the iframe
    if (actualIframeElement.value?.contentWindow) {
      actualIframeElement.value.contentWindow.getSelection()?.empty?.();
      actualIframeElement.value.contentWindow.getSelection()?.removeAllRanges?.();
    }
  };

  /**
   * Initializes all interaction listeners.
   * Should be called when the iframe content is ready.
   */
  const initializeInteractionListeners = () => {
    if (!rendition.value) return;

    // Remove old listeners before adding new ones to prevent duplicates
    removeIframeClickListener();
    // clearSwipeNavigation();
    rendition.value.off('selected', onRenditionSelected); // Ensure previous listener is off

    addIframeClickListener();
    // setupSwipeNavigation();
    rendition.value.on('selected', onRenditionSelected);
  };

  /**
   * Destroys all interaction listeners.
   * Should be called when the EPUB reader is being torn down.
   */
  const destroyInteractionListeners = () => {
    removeIframeClickListener();
    // clearSwipeNavigation();
    if (rendition.value) {
      rendition.value.off('selected', onRenditionSelected);
    }
    currentSelection.value = null; // Clear selection on destroy
  };

  /**
   * Refreshes the selection rect if there is a current selection.
   * This should be called after viewport/visualViewport resize to update the menu position.
   */
  const refreshSelectionRect = () => {
    if (!currentSelection.value || !actualIframeElement.value?.contentWindow) return;
    const selection = actualIframeElement.value.contentWindow.getSelection();
    if (!selection || selection.rangeCount === 0) return;
    const selDetails = getSelectionDetails(selection);
    if (!selDetails) return;
    // Only update the rect, keep other data the same
    currentSelection.value = {
      ...currentSelection.value,
      rect: selDetails.rect,
    };
  };

  return {
    currentSelection, // Readonly ref
    clearSelection,
    initializeInteractionListeners,
    destroyInteractionListeners,
    onRenditionSelected, // Expose this handler
    refreshSelectionRect, // Expose the new method
  };
}