<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, type PropType, nextTick } from 'vue'
import { Button } from '@/components/ui/button' // Assuming shadcn-vue button
import type { CSSProperties } from 'vue'
import {
  autoUpdate,
  computePosition,
  flip,
  shift,
  offset as offsetMiddleware
} from '@floating-ui/dom'

// Updated to match the structure from useEpubReader.ts
interface SelectionData {
  selectedText: string;
  rect: DOMRect | null;
  cfiRange: string;
  fullContextText: string;
  offsetInContext: number;
}

const props = defineProps({
  selectionData: {
    type: Object as PropType<SelectionData | null>,
    default: null,
  },
  iframeElement: {
    type: Object as PropType<HTMLIFrameElement | null>,
    default: null,
  },
})

const emit = defineEmits<{
  (e: 'lookup', payload: { selectedText: string; text: string; offset: number }): void;
  (e: 'copy'): void;
}>()

const isVisible = ref(false)
const bubbleMenuRef = ref<HTMLDivElement | null>(null)
const menuStyle = ref<CSSProperties>({
  position: 'fixed',
  top: '0px',
  left: '0px',
  zIndex: '40',
  visibility: 'hidden',
  background: 'white',
  border: '1px solid #ccc',
  borderRadius: '4px',
  padding: '4px',
  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
})

const MENU_GAP = 8 // px
let cleanupAutoUpdate: (() => void) | null = null

// Create a virtual element tracking the selection position
const getVirtualElement = () => ({
  getBoundingClientRect: () => {
    if (!props.selectionData?.rect || !props.iframeElement) {
      return {
        width: 0,
        height: 0,
        x: 0,
        y: 0,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        toJSON: () => {},
      }
    }
    
    try {
      // Get the iframe's position relative to the viewport
      const iframeRect = props.iframeElement.getBoundingClientRect()
      
      // Get the selection rect (this is relative to the iframe's viewport)
      const selRect = props.selectionData.rect
      
      // Safari-specific validation
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
      if (isSafari && (!iframeRect || !selRect)) {
        console.warn('[EpubBubbleMenu] Safari: Invalid rect data for positioning')
        return {
          width: 0,
          height: 0,
          x: 0,
          y: 0,
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          toJSON: () => {},
        }
      }
      
      // Combine to get absolute position relative to the main window
      return {
        width: selRect.width,
        height: selRect.height,
        x: iframeRect.left + selRect.left,
        y: iframeRect.top + selRect.top,
        top: iframeRect.top + selRect.top,
        left: iframeRect.left + selRect.left,
        right: iframeRect.left + selRect.left + selRect.width,
        bottom: iframeRect.top + selRect.top + selRect.height,
        toJSON: () => ({}),
      }
    } catch (error) {
      console.warn('[EpubBubbleMenu] Error calculating position:', error)
      return {
        width: 0,
        height: 0,
        x: 0,
        y: 0,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        toJSON: () => {},
      }
    }
  }
})

// Function to update the bubble menu's position
function updateBubbleMenuPosition() {
  if (!bubbleMenuRef.value) return

  computePosition(
    getVirtualElement(),
    bubbleMenuRef.value,
    {
      placement: 'top',
      strategy: 'fixed',
      middleware: [
        offsetMiddleware(MENU_GAP),
        flip({ fallbackPlacements: ['bottom'] }),
        shift({ padding: 5 })
      ]
    }
  ).then(({ x, y }) => {
    if (!bubbleMenuRef.value) return

    // Update position immediately
    Object.assign(bubbleMenuRef.value.style, {
      left: `${x}px`,
      top: `${y}px`,
      visibility: 'visible'
    })
  })
}

function setupFloatingUI() {
  if (!props.selectionData?.selectedText || !props.selectionData?.rect || !props.iframeElement) {
    isVisible.value = false
    return
  }
  
  // Cleanup any existing update
  if (cleanupAutoUpdate) {
    cleanupAutoUpdate()
    cleanupAutoUpdate = null
  }
  
  // Make component visible
  isVisible.value = true
  
  nextTick(() => {
    if (!bubbleMenuRef.value) {
      return
    }
    
    try {
      // Initial style to avoid flashes 
      Object.assign(bubbleMenuRef.value.style, {
        position: 'fixed',
        visibility: 'hidden',
        zIndex: '9999',
      })
      
      // Set up autoUpdate to continuously track position
      cleanupAutoUpdate = autoUpdate(
        getVirtualElement(),
        bubbleMenuRef.value,
        updateBubbleMenuPosition,
        // Use the most aggressive update strategy
        { animationFrame: true }
      )
    } catch (error) {
    }
  })
}

// Watch for changes to selection data and iframe
watch(
  () => props.selectionData?.selectedText && props.selectionData?.rect && props.iframeElement,
  (hasValidSelection) => {
    if (hasValidSelection) {
      setupFloatingUI()
    } else {
      if (cleanupAutoUpdate) {
        cleanupAutoUpdate()
        cleanupAutoUpdate = null
      }
      isVisible.value = false
    }
  },
  { immediate: true }
)

// Handle lookup button click
const handleLookup = () => {
  if (props.selectionData?.selectedText && props.selectionData.fullContextText !== undefined) {
    emit('lookup', { 
      selectedText: props.selectionData.selectedText,
      text: props.selectionData.fullContextText,
      offset: props.selectionData.offsetInContext
    })
    isVisible.value = false
  }
}

// Handle copy button click
const handleCopy = () => {
  if (props.selectionData?.selectedText) {
    navigator.clipboard.writeText(props.selectionData.selectedText)
      .then(() => {
        emit('copy')
        isVisible.value = false
      })
      .catch(err => {
      })
  }
}

onMounted(() => {
  // Initial setup if selection already exists
  if (props.selectionData?.selectedText && props.selectionData?.rect && props.iframeElement) {
    setupFloatingUI()
  }
})

onUnmounted(() => {
  if (cleanupAutoUpdate) {
    cleanupAutoUpdate()
    cleanupAutoUpdate = null
  }
})
</script>

<template>
  <div
    ref="bubbleMenuRef"
    v-show="isVisible"
    class="bg-background border shadow-md rounded-md flex items-center space-x-1 p-0.5"
  >
    <Button variant="ghost" size="xs" @click="handleLookup">Lookup</Button>
    <Button variant="ghost" size="xs" @click="handleCopy">Copy</Button>
  </div>
</template> 