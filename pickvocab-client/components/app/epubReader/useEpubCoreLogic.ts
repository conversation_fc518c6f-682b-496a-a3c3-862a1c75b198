import { ref, computed, nextTick, type Ref } from 'vue';
import ePub, { Book, Rendition, Contents } from 'epubjs';
import type { Location, NavItem } from 'epubjs'; // Removed Section import

/**
 * Interface for Table of Contents items.
 */
export interface TocItem extends NavItem {
  // NavItem already includes id, href, label, subitems, parent
  // Add any custom properties if needed in the future
}

/**
 * Options for initializing the useEpubCoreLogic composable.
 */
export interface EpubCoreLogicOptions {
  /** Reactive reference to the EPUB data ArrayBuffer. */
  epubDataProp: Ref<ArrayBuffer | null>;
  /** Reactive reference to the HTMLDivElement where the EPUB content will be rendered. */
  epubContentArea: Ref<HTMLDivElement | null>;
  /** Theme rules to be registered with the rendition. */
  themeRules: Record<string, any>; // Consider a more specific type for theme rules

  /** Callback invoked when the EPUB iframe is ready and its contents are loaded. */
  onIframeReady: (
    iframe: HTMLIFrameElement,
    view: Contents // epub.js Contents object, includes window and document
  ) => void;
  /** Callback invoked when the rendition relocates to a new section. */
  onRelocated: (location: Location, renditionInstance: Rendition) => void;
  /** Callback invoked when a new section is rendered. */
  onRendered: (section: { href: string, index: number }, view: Contents) => void;
  /** Callback invoked when the book's navigation (TOC and Spine) is loaded. */
  onBookNavigationLoaded: (toc: TocItem[], spine: any[]) => void; // Changed Section[] to any[]
  /** Callback invoked if book initialization fails. */
  onBookInitFailed?: (error: Error) => void;
}

/**
 * Composable for managing the core ePub.js Book and Rendition instances.
 * It handles the lifecycle of the EPUB rendering, content display,
 * and emits events for other composables to hook into.
 */
export function useEpubCoreLogic(options: EpubCoreLogicOptions) {
  const {
    epubDataProp,
    epubContentArea,
    themeRules,
    onIframeReady,
    onRelocated,
    onRendered,
    onBookNavigationLoaded,
    onBookInitFailed,
  } = options;

  const book = ref<Book | null>(null);
  const rendition = ref<Rendition | null>(null);
  const tableOfContents = ref<TocItem[]>([]);
  const spineItems = ref<any[]>([]); // Changed Section[] to any[]
  const currentChapterIndex = ref(0);
  const actualIframeElement = ref<HTMLIFrameElement | null>(null);
  const isDestroying = ref(false); // Flag to prevent re-initialization during cleanup

  const isReaderReady = computed(() => !!rendition.value && !!book.value?.spine && !isDestroying.value);

  /**
   * Destroys the current Book and Rendition instances.
   */
  const destroyCore = () => {
    isDestroying.value = true; // Set flag to prevent re-initialization
    try {
      // First, remove all event listeners from rendition to prevent callbacks
      if (rendition.value) {
        // Stop any ongoing rendering or processing first
        if (rendition.value.manager) {
          // Stop the queue to prevent new operations
          if (rendition.value.manager.q && typeof rendition.value.manager.q.stop === 'function') {
            rendition.value.manager.q.stop();
          }
          
          // Clear views to disconnect from DOM immediately
          if (rendition.value.manager.views && typeof rendition.value.manager.views.clear === 'function') {
            rendition.value.manager.views.clear();
          }
          
          // Abort any pending requests
          if (rendition.value.manager.request && typeof rendition.value.manager.request.abort === 'function') {
            rendition.value.manager.request.abort();
          }
        }
        
        // Remove all listeners safely (check if methods exist)
        try { rendition.value.off('relocated'); } catch (e) { /* ignore */ }
        try { rendition.value.off('rendered'); } catch (e) { /* ignore */ }
        try { rendition.value.off('selected'); } catch (e) { /* ignore */ }
        try { rendition.value.off('resized'); } catch (e) { /* ignore */ }
        try { rendition.value.off('orientationchange'); } catch (e) { /* ignore */ }
        try { rendition.value.off('layout'); } catch (e) { /* ignore */ }
        
        // Safely destroy the rendition
        try {
          rendition.value.destroy();
        } catch (e) {
          console.warn('[useEpubCoreLogic] Error destroying rendition:', e);
        }
        rendition.value = null;
      }
      
      if (book.value) {
        // Remove book event listeners safely
        try { book.value.off('ready'); } catch (e) { /* ignore */ }
        try { book.value.off('opened'); } catch (e) { /* ignore */ }
        
        // Safely destroy the book
        try {
          book.value.destroy();
        } catch (e) {
          console.warn('[useEpubCoreLogic] Error destroying book:', e);
        }
        book.value = null;
      }
      
      // Clear the content area immediately
      if (epubContentArea.value) {
        epubContentArea.value.innerHTML = '';
      }
    } catch (error) {
      console.warn('[useEpubCoreLogic] Error during cleanup:', error);
    } finally {
      // Always reset refs regardless of cleanup errors
      actualIframeElement.value = null;
      tableOfContents.value = [];
      spineItems.value = [];
      currentChapterIndex.value = 0;
      isDestroying.value = false; // Reset flag to allow future initialization
    }
  };

  /**
   * Initializes the ePub Book and Rendition instances.
   * Sets up rendering, themes, content hooks, and event listeners.
   */
  const initializeBookAndRendition = async (): Promise<boolean> => {
    // Prevent re-initialization during cleanup
    if (isDestroying.value) {
      return false;
    }

    if (!epubDataProp.value || !epubContentArea.value) {
      destroyCore();
      return false;
    }

    try {
      book.value = ePub(epubDataProp.value);
      await book.value.ready; // Wait for the book to be parsed

      const container = epubContentArea.value;
      if (!container || container.offsetWidth === 0 || container.offsetHeight === 0) {
        // This check might be too early if container visibility is set later.
        // Consider if this is the best place or if it should be handled by the caller.
        console.warn('EPUB container not ready or has no dimensions.');
        destroyCore();
        onBookInitFailed?.(new Error('EPUB container not ready or has no dimensions.'));
        return false;
      }

      // Safari-specific iframe configuration
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      
      rendition.value = book.value.renderTo(container, {
        width: '100%', // Use '100%' for scrolled-doc, or container.offsetWidth for paginated
        height: '100%',
        spread: 'none',
        flow: 'scrolled-doc', // Ensure this matches desired reading mode
        manager: 'default',
        ...(isSafari && {
          allowScriptedContent: true, // Enable for Safari
          sandbox: ['allow-same-origin', 'allow-scripts', 'allow-forms'] // Safari sandbox permissions
        })
      });

      // Register themes
      if (rendition.value && themeRules) {
        for (const themeName in themeRules) {
          rendition.value.themes.register(themeName, themeRules[themeName]);
        }
        // Note: Actual theme selection will be handled by ThemeManager via orchestrator
      }

      // Setup content hook
      rendition.value.hooks.content.register((contents: Contents) => {
        const doc = contents.document;
        if (doc.documentElement) {
          doc.documentElement.style.height = 'auto';
          doc.documentElement.style.overflowY = 'scroll';
          doc.documentElement.style.setProperty('-webkit-touch-callout', 'none'); // Disable callout on touch
        }
        if (doc.body) {
          doc.body.style.height = 'auto';
          doc.body.style.overflowY = 'visible';
        }
        doc.addEventListener('contextmenu', (e: Event) => e.preventDefault());

        if (contents.window && contents.window.frameElement) {
          const currentIframe = contents.window.frameElement as HTMLIFrameElement;
          
          // Safari-specific iframe sandbox configuration
          if (isSafari && currentIframe) {
            // Ensure the iframe has proper sandbox permissions for Safari
            const existingSandbox = currentIframe.getAttribute('sandbox');
            if (existingSandbox) {
              // Add missing permissions if not already present
              const permissions = ['allow-same-origin', 'allow-scripts', 'allow-forms'];
              const currentPermissions = existingSandbox.split(' ');
              const missingPermissions = permissions.filter(p => !currentPermissions.includes(p));
              if (missingPermissions.length > 0) {
                currentIframe.setAttribute('sandbox', existingSandbox + ' ' + missingPermissions.join(' '));
              }
            } else {
              currentIframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms');
            }
          }
          
          actualIframeElement.value = currentIframe;
          onIframeReady(currentIframe, contents);
        }
      });

      // Setup event listeners for rendition
      rendition.value.on('relocated', (location: Location) => {
        if (location.start?.index !== undefined) {
          currentChapterIndex.value = location.start.index;
        }
        if (rendition.value) { // Ensure rendition still exists
            onRelocated(location, rendition.value as Rendition); // Cast for type safety if TS struggles
        }
      });

      rendition.value.on('rendered', (section: { href: string, index: number }, view: Contents) => {
        if (section.index !== undefined && currentChapterIndex.value !== section.index) {
          currentChapterIndex.value = section.index;
        }
        onRendered(section, view);
      });
      
      // Load navigation items (TOC and Spine)
      // This needs to happen after book is ready.
      await book.value.loaded.navigation; // Wait for navigation to be loaded
      tableOfContents.value = book.value.navigation.toc as TocItem[];
      
      // Accessing spine items (epubjs v0.3)
      if (book.value && book.value.spine) {
        await book.value.loaded.spine; // Ensure spine is loaded
        const bookSpine = book.value.spine as any; // Cast to any to bypass TS errors for now
        const numSections = bookSpine.length;
        const tempSpineItems: any[] = []; // Use any for items temporarily
        if (typeof numSections === 'number') { // Check if length is valid
          for (let i = 0; i < numSections; i++) {
            const section = bookSpine.get(i); // Use the casted spine
            if (section) {
              tempSpineItems.push(section);
            }
          }
        }
        spineItems.value = tempSpineItems; // No final cast to Section[] needed now
        if (tempSpineItems.length === 0 && numSections > 0) {
            console.warn('Spine items could not be retrieved using spine.get(), though spine.length suggests sections exist.');
        } else if (tempSpineItems.length === 0) {
            console.warn('No spine items found or retrieved.');
        }
      } else {
        spineItems.value = [];
        console.warn('Book or book.spine not available for populating spineItems.');
      }
      onBookNavigationLoaded(tableOfContents.value, spineItems.value);

      return true;
    } catch (error) {
      console.error('Failed to initialize EPUB Book and Rendition:', error);
      destroyCore();
      onBookInitFailed?.(error as Error);
      return false;
    }
  };

  /**
   * Displays EPUB content, optionally at a specific CFI.
   * @param cfi Optional ePub CFI string to navigate to.
   */
  const displayContent = async (cfi?: string): Promise<void> => {
    if (!rendition.value) {
      console.warn('Rendition not available for displayContent.');
      return;
    }
    try {
      if (cfi) {
        await rendition.value.display(cfi);
      } else {
        await rendition.value.display();
      }
      // Location and chapter index will be updated by 'relocated' or 'rendered' events.
    } catch (error) {
      console.error('Error displaying EPUB content:', error);
      // Attempt to display from the beginning if CFI display failed
      if (cfi && rendition.value) {
        try {
          await rendition.value.display();
        } catch (displayError) {
          console.error('Error displaying EPUB content from beginning after CFI fail:', displayError);
        }
      }
    }
  };

  /**
   * Handles window resize events to adjust the rendition dimensions.
   */
  const handleResize = () => {
    if (!rendition.value || !epubContentArea.value) return;
    const container = epubContentArea.value;
    // Ensure container has valid dimensions before resizing
    if (container.offsetWidth > 0 && container.offsetHeight > 0) {
        rendition.value.resize(container.offsetWidth, container.offsetHeight);
    }
  };

  return {
    book, // Readonly ref to the book instance
    rendition, // Readonly ref to the rendition instance
    tableOfContents, // Readonly ref
    spineItems, // Readonly ref
    currentChapterIndex, // Readonly ref
    actualIframeElement, // Readonly ref, discovered by this composable
    isReaderReady, // Computed
    isDestroying, // Readonly ref to track cleanup state
    initializeBookAndRendition,
    destroyCore,
    displayContent,
    handleResize,
  };
}