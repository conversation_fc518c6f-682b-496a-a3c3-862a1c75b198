import Dexie, { type Table } from 'dexie';

export interface RecentBookHandleEntry {
  id: string; // Primary Key: bookId generated by useEpubReadingProgress.ts
  fileHandle: FileSystemFileHandle | null; // The actual file handle (null for fallback browsers)
  filename: string; // Original filename (e.g., "my-book.epub")
  title?: string; // Book title from EPUB metadata
  coverImage?: string; // URL or base64 string for the cover image
  lastOpened: number; // Timestamp, for sorting (will be an index)
  supportsFileSystemAccess: boolean; // Whether this entry was created with File System Access API
  fileData?: ArrayBuffer; // For fallback browsers - store the actual file data
  fileSize?: number; // File size in bytes for display and validation
}

export class RecentBooksDB extends Dexie {
  recentBooks!: Table<RecentBookHandleEntry, string>; // string is the type of the primary key 'id'

  constructor() {
    super('pickvocabAppDB'); // Database name
    this.version(4).stores({ // Incremented version number
      recentBooks: 'id, lastOpened, filename, coverImage, supportsFileSystemAccess, fileSize' // Added fileSize index
    }).upgrade(tx => {
      console.log("Upgrading recentBooks table to version 4, adding fileData and fileSize fields.");
      return tx.table("recentBooks").toCollection().modify(book => {
        if (book.coverImage === undefined) {
          // @ts-ignore
          book.coverImage = null;
        }
        if (book.supportsFileSystemAccess === undefined) {
          // @ts-ignore
          book.supportsFileSystemAccess = book.fileHandle !== null; // Assume true if fileHandle exists
        }
        if (book.fileSize === undefined) {
          // @ts-ignore
          book.fileSize = null; // New field for file size tracking
        }
        // fileData will be undefined for existing entries (not indexed, too large)
      });
    });
  }
}
export const db = new RecentBooksDB();

// Storage limits for fallback browsers
export const STORAGE_LIMITS = {
  MAX_TOTAL_SIZE: 500 * 1024 * 1024, // 500MB total
  MAX_FILE_SIZE: 50 * 1024 * 1024,   // 50MB per file
  MAX_BOOKS_COUNT: 100,               // Maximum number of books
};

// Helper function to get recent book by ID
export async function getRecentBookById(id: string) {
  return db.recentBooks.get(id);
}

// Storage management utilities
export async function getTotalStorageUsed(): Promise<number> {
  try {
    // Get all books and filter those with file size
    const allBooks = await db.recentBooks.toArray();
    const booksWithSize = allBooks.filter(book => book.fileSize && book.fileSize > 0);
    return booksWithSize.reduce((total, book) => total + (book.fileSize || 0), 0);
  } catch (error) {
    console.warn('Error calculating storage usage:', error);
    return 0;
  }
}

export async function cleanupOldBooks(): Promise<void> {
  try {
    const totalSize = await getTotalStorageUsed();
    
    if (totalSize > STORAGE_LIMITS.MAX_TOTAL_SIZE) {
      // Get all books and filter/sort those with file data
      const allBooks = await db.recentBooks.toArray();
      const booksWithData = allBooks
        .filter(book => book.fileSize && book.fileSize > 0)
        .sort((a, b) => a.lastOpened - b.lastOpened); // Sort by lastOpened (oldest first)
      
      let currentSize = totalSize;
      for (const book of booksWithData) {
        if (currentSize <= STORAGE_LIMITS.MAX_TOTAL_SIZE * 0.8) break; // Keep 80% of limit
        
        // Remove file data but keep metadata
        await db.recentBooks.update(book.id, {
          fileData: undefined,
          fileSize: undefined,
        });
        
        currentSize -= (book.fileSize || 0);
        console.log(`Cleaned up stored data for: ${book.filename}`);
      }
    }
  } catch (error) {
    console.warn('Error cleaning up old books:', error);
  }
}

export async function canStoreFile(fileSize: number): Promise<boolean> {
  if (fileSize > STORAGE_LIMITS.MAX_FILE_SIZE) return false;
  
  const totalSize = await getTotalStorageUsed();
  return (totalSize + fileSize) <= STORAGE_LIMITS.MAX_TOTAL_SIZE;
}