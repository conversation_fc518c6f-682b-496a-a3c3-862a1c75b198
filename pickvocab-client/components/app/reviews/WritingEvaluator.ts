import type { WritingEvaluation } from './CreativeWritingTypes';
import { calculateDeltaScore } from './CreativeWritingTypes';
import { constructWritingEvaluationPrompt } from './CreativeWritingPrompt';
import { parseMarkdownWritingEvaluation } from './markdownWritingEvaluationParser';
import { useLLMStore } from '~/stores/llm';

/**
 * Composable for evaluating creative writing exercises using AI
 * Provides multi-criteria assessment with word usage detection
 */
export function useWritingEvaluator() {
  const llmStore = useLLMStore();


  /**
   * Evaluate user's creative writing using AI
   */
  async function evaluateWriting(
    targetWord: string,
    userText: string,
    context: string
  ): Promise<WritingEvaluation> {
    try {
      // Get LLM configuration
      let modelConfigToUse = llmStore.activeUserModel;
      if (!modelConfigToUse) {
        const defaultModelId = llmStore.pickvocabChatSource?.modelId;
        if (typeof defaultModelId === 'number') {
          modelConfigToUse = llmStore.getModelById(defaultModelId);
        }
      }
      if (!modelConfigToUse) {
        throw new Error('No language model configuration available for writing evaluation');
      }

      const chatSource = llmStore.createChatSource(modelConfigToUse);
      if (!chatSource) {
        throw new Error(`Failed to create ChatSource for model: ${modelConfigToUse.name}`);
      }

      // Construct evaluation prompt
      const prompt = constructWritingEvaluationPrompt(targetWord, userText, context);
      
      // Get AI evaluation
      const result = await chatSource.sendMessage(prompt);
      
      // Parse the markdown response
      const evaluation = parseMarkdownWritingEvaluation(result.message);
      
      return evaluation;
    } catch (error) {
      console.error('Error evaluating writing:', error);
      throw error; // Re-throw to let caller handle the error
    }
  }



  return {
    evaluateWriting,
    calculateDeltaScore
  };
}