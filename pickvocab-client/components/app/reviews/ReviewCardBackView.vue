<script setup lang="ts">
// import { IconCircleX, IconCircleCheck } from '@tabler/icons-vue';
// @ts-ignore
import IconCircleX from '@tabler/icons-vue/dist/esm/icons/IconCircleX.mjs';
// @ts-ignore
import IconCircleCheck from '@tabler/icons-vue/dist/esm/icons/IconCircleCheck.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
import FunctionalCardView from '../cards/FunctionalCardView.vue';
import CardExplanationView from '../cards/CardExplanationView.vue';
import { CardType } from '~/utils/card';

const llmStore = useLLMStore();

const props = defineProps<{
  card: Card;
  showContinueOnly?: boolean;
}>();

const llmModel = computed(() => {
  if (props.card.cardType === CardType.DefinitionCard) {
    return props.card.referenceWord ? llmStore.getModelById(props.card.referenceWord?.llm_model) : undefined;
  }
  return props.card.wordInContext.llm_model
    ? llmStore.getModelById(props.card.wordInContext.llm_model) : undefined;
});

function handleDeleteAttempt() {
  alert('You cannot delete cards during a review session.');
}
</script>

<template>
  <div class="flex-grow">
    <div class="sm:ml-64 pt-24 pb-4 pl-10 pr-10 xl:pr-48 h-full flex flex-col">
      <template v-if="card.cardType === CardType.DefinitionCard">
        <FunctionalCardView
          :card="card"
          :llm-model="llmModel"
          :llm-icon="
            llmModel ? llmStore.providerMap[llmModel.provider].logo : undefined
          "
          class="flex-grow"
        >
        </FunctionalCardView>
      </template>
      <template v-else>
        <CardExplanationView :card="card" @delete="handleDeleteAttempt" />
      </template>
      <div class="sticky w-full bottom-4 flex justify-center mt-4">
        <div
          class="flex text-sm text-gray-700 py-2 px-2 rounded-md border border-gray-100 shadow-md bg-white"
        >
          <!-- Continue button for MCQ context -->
          <template v-if="showContinueOnly">
            <button
              @click="$emit('answer', 1)"
              class="px-2 py-1 border rounded-md border-gray-300 flex items-center hover:bg-gray-100"
            >
              <IconArrowRight class="inline-block w-5 h-5 me-2" />
              <span>Continue</span>
            </button>
          </template>
          
          <!-- Regular forgot/remembered buttons -->
          <template v-else>
            <button
              @click="$emit('answer', -1)"
              class="px-2 py-1 border rounded-md border-gray-300 flex items-center hover:bg-gray-100"
            >
              <icon-circle-x stroke-width="1.5" class="inline-block w-5 h-5 me-2"></icon-circle-x>
              <span>Forgot</span>
            </button>
            <button
              @click="$emit('answer', 1)"
              class="ml-2 px-2 py-1 border rounded-md border-gray-300 hover:bg-gray-100"
            >
              <icon-circle-check stroke-width="1.5" class="inline-block w-5 h-5 me-2"></icon-circle-check>
              <span>Remembered</span>
            </button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
