import type { Card, DefinitionCard, ContextCard } from '~/utils/card';
import { CardType } from '~/utils/card';
import type { LLMCardInput, MCQItem, TypeInQuestionItem } from './reviewTypes';
import type { CreativeWritingItem } from './CreativeWritingTypes';
import { parseMarkdownReviewQuestions } from './markdownReviewQuestionParser';
import { parseMarkdownTypeInQuestions } from './markdownTypeInQuestionParser';
import { parseMarkdownCreativeWriting } from './markdownCreativeWritingParser';
import { useLLMStore } from '~/stores/llm';
import { constructMcqPrompt } from './mcqPrompt';
import { constructTypeInPrompt } from './typeInPrompt';
import { constructCreativeWritingPrompt } from './CreativeWritingPrompt';

/**
 * Composable for generating AI-powered review questions from flashcards.
 * Supports multiple-choice (MCQ), type-in/fill-in-the-blank questions, and creative writing exercises.
 */
export function useAiReviewGenerator() {
  const llmStore = useLLMStore();

  /**
   * Prepare unique valid cards for LLM input by deduplicating and formatting.
   * This is a generic utility for both MCQ and Type-in question generation.
   */
  function prepareUniqueValidInputsForLLM(cardsToFormat: Card[]): LLMCardInput[] {
    const uniqueCards: Card[] = [];
    const seenCardIds = new Set<string>();

    for (const card of cardsToFormat) {
      if (card.id && !seenCardIds.has(String(card.id))) {
        uniqueCards.push(card);
        seenCardIds.add(String(card.id));
      }
    }

    return uniqueCards.map(card => {
      let word: string;
      let definition: string;

      if (card.cardType === CardType.DefinitionCard) {
        const defCard = card as DefinitionCard;
        word = defCard.word;
        definition = defCard.definition.definition || '';
      } else if (card.cardType === CardType.ContextCard) {
        const contextCard = card as ContextCard;
        word = contextCard.wordInContext.word;
        definition = contextCard.wordInContext.definition?.definition ||
                     contextCard.wordInContext.definitionShort?.explanation || '';
      } else {
        word = '';
        definition = '';
      }

      return {
        id: String(card.id),
        word: word.trim(),
        definition: definition.trim()
      };
    }).filter(item => item.word && item.definition);
  }


  async function generateQuestions<T extends MCQItem | TypeInQuestionItem | CreativeWritingItem>(
    cards: Card[],
    promptConstructor: (cards: LLMCardInput[]) => string,
    parser: (markdown: string) => T[]
  ): Promise<T[]> {
    try {
      const formattedCards = prepareUniqueValidInputsForLLM(cards);
      if (formattedCards.length === 0) {
        throw new Error('No valid cards to generate questions from');
      }

      const prompt = promptConstructor(formattedCards);

      let modelConfigToUse = llmStore.activeUserModel;
      if (!modelConfigToUse) {
        const defaultModelId = llmStore.pickvocabChatSource?.modelId;
        if (typeof defaultModelId === 'number') {
          modelConfigToUse = llmStore.getModelById(defaultModelId);
        }
      }
      if (!modelConfigToUse) {
        throw new Error('No language model configuration available for AI review generation');
      }

      const chatSource = llmStore.createChatSource(modelConfigToUse);
      if (!chatSource) {
        throw new Error(`Failed to create ChatSource for model: ${modelConfigToUse.name}`);
      }

      const result = await chatSource.sendMessage(prompt);
      const items = parser(result.message);

      if (items.length === 0) {
        throw new Error('No valid items found in LLM response');
      }

      return items.map(item => ({
        ...item,
        originalCardId: item.llmSourceCardId ? String(item.llmSourceCardId) : undefined
      }));

    } catch (error) {
      console.error('Error generating AI review questions:', error);
      throw error;
    }
  }

  /**
   * Generate multiple-choice questions from cards using LLM
   */
  async function generateMcqs(cards: Card[]): Promise<MCQItem[]> {
    return generateQuestions(cards, constructMcqPrompt, parseMarkdownReviewQuestions);
  }

  /**
   * Generate type-in and fill-in-the-blank questions from cards using LLM
   */
  async function generateTypeInQuestions(cards: Card[]): Promise<TypeInQuestionItem[]> {
    return generateQuestions(cards, constructTypeInPrompt, parseMarkdownTypeInQuestions);
  }

  /**
   * Generate creative writing exercises from cards using LLM
   */
  async function generateCreativeWritingExercises(cards: Card[]): Promise<CreativeWritingItem[]> {
    return generateQuestions(cards, constructCreativeWritingPrompt, parseMarkdownCreativeWriting);
  }

  return {
    generateMcqs,
    generateTypeInQuestions,
    generateCreativeWritingExercises,
    prepareUniqueValidInputsForLLM
  };
}