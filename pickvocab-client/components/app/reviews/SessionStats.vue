<script setup lang="ts">
import type { ReviewSessionSummary } from './reviewSummaryTypes';

interface Props {
  sessionSummary: ReviewSessionSummary;
}

const props = defineProps<Props>();

// Computed properties for visual styling
const accuracyColor = computed(() => {
  const accuracy = props.sessionSummary.accuracyPercentage;
  if (accuracy >= 80) return 'text-green-600';
  if (accuracy >= 60) return 'text-yellow-600';
  return 'text-orange-600';
});

const accuracyBgColor = computed(() => {
  const accuracy = props.sessionSummary.accuracyPercentage;
  if (accuracy >= 80) return 'bg-green-100';
  if (accuracy >= 60) return 'bg-yellow-100';
  return 'bg-orange-100';
});

const progressBarColor = computed(() => {
  const accuracy = props.sessionSummary.accuracyPercentage;
  if (accuracy >= 80) return 'bg-green-500';
  if (accuracy >= 60) return 'bg-yellow-500';
  return 'bg-orange-500';
});
</script>

<template>
  <div class="space-y-4 sm:space-y-6">
    <!-- Main Score Display -->
    <div class="text-center">
      <div class="inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 rounded-full border-4 sm:border-8 border-gray-100 mb-3 sm:mb-4"
           :class="accuracyBgColor">
        <div class="text-center">
          <div class="text-xl sm:text-3xl font-bold" :class="accuracyColor">
            {{ sessionSummary.correctAnswers }}/{{ sessionSummary.totalCards }}
          </div>
          <div class="text-xs sm:text-sm text-gray-600 font-medium">Correct</div>
        </div>
      </div>
      
      <!-- Accuracy Percentage -->
      <div class="text-xl sm:text-2xl font-bold mb-2" :class="accuracyColor">
        {{ sessionSummary.accuracyPercentage }}% Accuracy
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="w-full bg-gray-200 rounded-full h-2 sm:h-3">
      <div 
        class="h-2 sm:h-3 rounded-full transition-all duration-500 ease-out"
        :class="progressBarColor"
        :style="{ width: `${sessionSummary.accuracyPercentage}%` }"
      ></div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-3 gap-2 sm:gap-4 text-center">
      <div class="bg-green-50 rounded-lg p-3 sm:p-4">
        <div class="text-lg sm:text-2xl font-bold text-green-600">
          {{ sessionSummary.correctAnswers }}
        </div>
        <div class="text-xs sm:text-sm text-green-700 font-medium">Correct</div>
      </div>
      
      <div class="bg-red-50 rounded-lg p-3 sm:p-4">
        <div class="text-lg sm:text-2xl font-bold text-red-600">
          {{ sessionSummary.incorrectAnswers }}
        </div>
        <div class="text-xs sm:text-sm text-red-700 font-medium">Incorrect</div>
      </div>
      
      <div class="bg-blue-50 rounded-lg p-3 sm:p-4">
        <div class="text-lg sm:text-2xl font-bold text-blue-600">
          {{ sessionSummary.totalCards }}
        </div>
        <div class="text-xs sm:text-sm text-blue-700 font-medium">Total</div>
      </div>
    </div>

    <!-- Review Mode Indicator -->
    <div v-if="sessionSummary.reviewModes.length > 0" class="text-center">
      <div class="text-xs sm:text-sm text-gray-600 mb-2">Review Mode</div>
      <div class="flex flex-wrap justify-center gap-1 sm:gap-2">
        <span 
          v-for="mode in sessionSummary.reviewModes" 
          :key="mode"
          class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-medium"
          :class="{
            'bg-blue-100 text-blue-800': mode === 'basic',
            'bg-purple-100 text-purple-800': mode === 'mcq',
            'bg-green-100 text-green-800': mode === 'type-in',
            'bg-orange-100 text-orange-800': mode === 'creative-writing'
          }"
        >
          {{ 
            mode === 'basic' ? 'Basic Review' : 
            mode === 'mcq' ? 'Multiple Choice' : 
            mode === 'type-in' ? 'Type-in' : 
            mode === 'creative-writing' ? 'Creative Writing' :
            mode 
          }}
        </span>
      </div>
    </div>
  </div>
</template>