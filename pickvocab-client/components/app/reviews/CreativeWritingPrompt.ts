import type { LLMCardInput } from './reviewTypes';

/**
 * Constructs a structured prompt for generating creative writing contexts
 * for vocabulary exercises. The prompt follows the established XML format
 * pattern used in other LLM prompts in the codebase.
 */
export function constructCreativeWritingPrompt(cards: LLMCardInput[]): string {
  const cardsJson = JSON.stringify(cards);
  
  return `<instructions>
You are an expert language teacher creating creative writing exercises. For each vocabulary word provided, generate a context/scenario that naturally calls for the use of that word WITHOUT revealing the word itself.

The context should:
1. Create a situation where the target word would naturally fit
2. Be interesting and engaging
3. NOT mention or hint at the exact word
4. Be clear enough that someone who knows the word would think to use it
5. Include a specific writing task or prompt
</instructions>

<output_format>
For each word, create a markdown section with this EXACT format:

## [word] (id: [card_id])

### Context
[A 2-3 sentence scenario that sets up a situation where this word would naturally be used. Do not mention the word itself.]

### Prompt
[A specific writing instruction that guides the user to write something that would naturally include this word. For example: "Write about...", "Describe...", "Explain..."]

### Hint
[A subtle clue about the type of word needed, such as "Think of a word that means..." or "Use an adjective that..." - but never give the exact word]
</output_format>

<example_output>
## meticulous (id: 123)

### Context
Sarah is preparing for the most important presentation of her career. Every slide must be perfect, every number double-checked, and every detail considered. She's known for her extremely careful and precise approach to work.

### Prompt
Write a paragraph describing Sarah's preparation process for this presentation, emphasizing her attention to detail and careful approach.

### Hint
Think of an adjective that means extremely careful and precise about details.

## surreptitious (id: 456)

### Context
During the boring company meeting, Jake needed to check an urgent personal message on his phone. The boss has a strict no-phone policy and watches everyone like a hawk.

### Prompt
Describe how Jake attempts to check his phone without anyone noticing, especially his watchful boss.

### Hint
Use an adjective that describes doing something secretly or stealthily.
</example_output>

<input>
${cardsJson}
</input>`;
}

/**
 * Constructs a structured prompt for evaluating user's creative writing
 * exercises with multi-criteria assessment.
 */
export function constructWritingEvaluationPrompt(
  targetWord: string,
  userText: string,
  context: string
): string {
  return `<instructions>
You are evaluating a user's creative writing exercise. The user was given a context and asked to write text that naturally incorporates a specific vocabulary word. Evaluate their writing on multiple criteria.

IMPORTANT: Do not apply any text formatting (bold, italic, etc.) in your response. Use plain text only.

Target word: "${targetWord}"
Context provided: "${context}"
</instructions>

<evaluation_criteria>
1. Grammar: Are there grammar or spelling errors?
2. Fluency: Does the writing flow naturally?
3. Context Fit: Does the writing match the given context/scenario?
4. Word Usage: Did they use the target word correctly? If the word is not used, this should be a very low score (0-20). If used correctly, score based on how well it's integrated.
</evaluation_criteria>

<output_format>
Provide your evaluation in markdown format with these sections:

# Evaluation Results

## Word Usage
- Used: [Yes/No]
- Used Correctly: [Yes/No]

## Scores
- Grammar: [0-100]/100
- Fluency: [0-100]/100
- Context Fit: [0-100]/100
- Word Usage: [0-100]/100

## Feedback
[Overall feedback message about the writing quality and word usage]

## Issues
- [Specific issue 1]
- [Specific issue 2]
(Only include if there are actual issues)

## Suggestions
- [Suggestion for improvement 1]
- [Suggestion for improvement 2]

## Examples
[If the word was used incorrectly or not used, provide 1-2 examples of how to use it correctly in this context. If the word was used correctly, provide 2-3 revised versions of the user's writing that incorporate the feedback and suggestions above.]
</output_format>

<example_output>
# Evaluation Results

## Word Usage
- Used: Yes
- Used Correctly: Yes

## Scores
- Grammar: 95/100
- Fluency: 90/100
- Context Fit: 100/100
- Word Usage: 85/100

## Feedback
The writing effectively captures Sarah's meticulous approach to her presentation preparation. The target word is used correctly and fits naturally within the context. The description is vivid and engaging.

## Issues
- Minor repetition of "careful" could be varied with synonyms
- One comma splice in the second sentence

## Suggestions
- Consider varying vocabulary to avoid repetition
- Break the longer sentence into two for better flow

## Examples
- Sarah approached her presentation with meticulous attention to detail. Every slide was thoroughly reviewed, every number triple-checked, and every visual element positioned precisely. Her systematic preparation process ensured that nothing would be left to chance during this career-defining moment.
- Sarah was meticulous in her presentation preparation. She carefully reviewed each slide and triple-checked every number. Her precise attention to visual elements and systematic approach ensured nothing would be overlooked during this career-defining moment.
- Known for her meticulous nature, Sarah left nothing to chance in her presentation preparation. Every detail received careful scrutiny—from slide content to numerical accuracy to visual positioning. This thorough approach reflected her commitment to excellence.
</example_output>

<user_text>
${userText}
</user_text>`;
}