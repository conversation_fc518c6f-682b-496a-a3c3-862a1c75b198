import { marked } from 'marked';
import type { MCQItem } from './reviewTypes';
import { getTextFromMarkedTokens, decodeHtmlEntities, type MarkedToken } from '../write/markedUtils';

/**
 * Parse markdown response from LLM into structured MCQItem objects
 * Error-tolerant parser that handles case insensitive headings, redundant whitespace, etc.
 */
export function parseMarkdownReviewQuestions(markdown: string): MCQItem[] {
  const tokens = marked.lexer(markdown);
  
  const mcqItems: MCQItem[] = [];
  
  let currentMCQ: Partial<MCQItem> | null = null;
  let currentSection: string | null = null;
  let optionLetterOrder: string[] = [];
  
  const traverse = (token: MarkedToken) => {
    // Handle heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      // Level 2 heading: Word (starts new MCQ)
      if (token.depth === 2 && headingText) {
        // Save previous MCQ if complete and valid
        if (currentMCQ && isValidMCQ(currentMCQ)) {
          mcqItems.push(currentMCQ as MCQItem);
        }
        
        // Parse heading to extract word and card ID (e.g., "word (id: 123)" or "word (123)")
        const match = headingText.match(/^(.*?)\s*\(.*?(\d+).*?\)$/i);
        
        if (match && match[1] && match[2]) {
          const parsedWord = match[1].trim();
          const parsedCardId = match[2].trim(); // Numeric string "123"
          
          // Initialize new MCQ with extracted data
          currentMCQ = {
            id: generateMCQId(),
            llmSourceCardId: parsedCardId,
            word: parsedWord,
            questionType: '',
            questionText: '',
            options: { a: '', b: '', c: '', d: '' },
            correctAnswer: 'a',
            explanation: ''
          };
          currentSection = null;
          optionLetterOrder = [];
        } else {
          console.warn(`AI Review Parser: Could not parse word and numeric card ID from heading: "${headingText}". Expected format: "word (123)" or "word (id: 123)".`);
          // Fallback logic - save previous MCQ if valid and skip this one
          if (currentMCQ && isValidMCQ(currentMCQ)) { 
            mcqItems.push(currentMCQ as MCQItem);
          }
          currentMCQ = null; 
        }
      }
      // Level 3 heading: Question type or section
      else if (token.depth === 3 && currentMCQ) {
        const normalizedHeading = headingText.toLowerCase().trim();
        
        if (normalizedHeading.includes('options')) {
          currentSection = 'options';
        } else if (normalizedHeading.includes('answer')) {
          currentSection = 'answer';
        } else if (normalizedHeading.includes('explanation')) {
          currentSection = 'explanation';
        } else {
          // Treat as question type
          currentMCQ.questionType = headingText.trim();
          currentSection = 'question';
        }
      }
      // Level 4 heading: Might be "Options", "Answer", "Explanation"
      else if (token.depth === 4 && currentMCQ) {
        const normalizedHeading = headingText.toLowerCase().trim();
        
        if (normalizedHeading.includes('options')) {
          currentSection = 'options';
        } else if (normalizedHeading.includes('answer')) {
          currentSection = 'answer';
        } else if (normalizedHeading.includes('explanation')) {
          currentSection = 'explanation';
        }
      }
    }
    
    // Handle paragraph tokens
    else if (token.type === 'paragraph' && currentMCQ) {
      if (currentSection === 'question') {
        const paragraphText = token.tokens 
          ? getTextFromMarkedTokens(token.tokens) 
          : decodeHtmlEntities(token.text || '').trim();
        currentMCQ.questionText += (currentMCQ.questionText ? ' ' : '') + paragraphText;
      } else if (currentSection === 'answer') {
        const paragraphText = token.tokens 
          ? getTextFromMarkedTokens(token.tokens) 
          : decodeHtmlEntities(token.text || '').trim();
        // Extract answer letter (case insensitive, handle various formats)
        const answerMatch = paragraphText.match(/\b([a-d])\b/i);
        if (answerMatch) {
          currentMCQ.correctAnswer = answerMatch[1].toLowerCase() as 'a' | 'b' | 'c' | 'd';
        }
      } else if (currentSection === 'explanation') {
        const paragraphText = token.tokens 
          ? getTextFromMarkedTokens(token.tokens) 
          : decodeHtmlEntities(token.text || '').trim();
        currentMCQ.explanation += (currentMCQ.explanation ? ' ' : '') + paragraphText;
      } else if (currentSection === 'options') {
        // Handle options - support both formats: with <br> tokens and with \n in text
        if (token.tokens && Array.isArray(token.tokens)) {
          // Check if we have a single text token with newlines (new format)
          if (token.tokens.length === 1 && token.tokens[0].type === 'text') {
            const optionsText = decodeHtmlEntities(token.tokens[0].text || '');
            // Split by newlines and parse each line
            const lines = optionsText.split('\n').filter((line: string) => line.trim());
            const seenLetters = new Set<string>();
            
            lines.forEach((line: string) => {
              const trimmedLine = line.trim();
              const optionMatch = trimmedLine.match(/^([a-d])[\)\.\:]?\s*(.+)/i);
              if (optionMatch && currentMCQ && currentMCQ.options) {
                const letter = optionMatch[1].toLowerCase() as 'a' | 'b' | 'c' | 'd';
                const optionText = optionMatch[2].trim();
                
                // Check for duplicate option letters
                if (seenLetters.has(letter)) {
                  console.log(`Duplicate option letter "${letter}" found in word "${currentMCQ.word}". Skipping this MCQ.`);
                  currentMCQ = null; // Mark as invalid
                  return;
                }
                
                seenLetters.add(letter);
                currentMCQ.options[letter] = optionText;
              }
            });
          } else {
            // Handle the old format with <br> tokens
            let currentOptionText = '';
            let currentOptionLetter: string | null = null;
            const seenLetters = new Set<string>();
            
            token.tokens.forEach((subToken: MarkedToken) => {
              if (subToken.type === 'text') {
                const text = decodeHtmlEntities(subToken.text || '');
                // Check if this text starts with an option letter
                const optionMatch = text.match(/^([a-d])[\)\.\:]?\s*(.+)/i);
                if (optionMatch) {
                  // Save previous option if we have one
                  if (currentOptionLetter && currentOptionText && currentMCQ && currentMCQ.options) {
                    currentMCQ.options[currentOptionLetter as 'a' | 'b' | 'c' | 'd'] = currentOptionText.trim();
                  }
                  
                  // Check for duplicate option letters
                  const letter = optionMatch[1].toLowerCase();
                  if (seenLetters.has(letter)) {
                    console.log(`Duplicate option letter "${letter}" found in word "${currentMCQ?.word}". Skipping this MCQ.`);
                    currentMCQ = null; // Mark as invalid
                    return;
                  }
                  
                  // Start new option
                  seenLetters.add(letter);
                  currentOptionLetter = letter;
                  currentOptionText = optionMatch[2];
                } else if (currentOptionLetter) {
                  // Continue current option text
                  currentOptionText += text;
                }
              } else if (subToken.type === 'br') {
                // Line break - save current option and reset
                if (currentOptionLetter && currentOptionText && currentMCQ && currentMCQ.options) {
                  currentMCQ.options[currentOptionLetter as 'a' | 'b' | 'c' | 'd'] = currentOptionText.trim();
                  currentOptionLetter = null;
                  currentOptionText = '';
                }
              }
            });
            
            // Save final option if we have one
            if (currentOptionLetter && currentOptionText && currentMCQ && currentMCQ.options) {
              currentMCQ.options[currentOptionLetter as 'a' | 'b' | 'c' | 'd'] = currentOptionText.trim();
            }
          }
        }
      } else if (!currentSection && !currentMCQ.questionType) {
        // If no section specified and no question type, treat as question text
        const paragraphText = token.tokens 
          ? getTextFromMarkedTokens(token.tokens) 
          : decodeHtmlEntities(token.text || '').trim();
        currentMCQ.questionText += (currentMCQ.questionText ? ' ' : '') + paragraphText;
      }
    }
    
    // Handle list tokens (for options)
    else if (token.type === 'list' && currentMCQ && currentSection === 'options') {
      if (token.items && Array.isArray(token.items)) {
        const seenLetters = new Set<string>();
        
        token.items.forEach((item: MarkedToken) => {
          if (!currentMCQ || !currentMCQ.options) return;
          
          const itemText = item.tokens 
            ? getTextFromMarkedTokens(item.tokens) 
            : decodeHtmlEntities(item.text || '').trim();
          
          // Parse option format: "a) text" or "a. text" or just "text"
          // More relaxed regex that handles leading/trailing spaces
          const optionMatch = itemText.match(/([a-d])[\)\.\:]?\s*(.+)/i);
          if (optionMatch) {
            const letter = optionMatch[1].toLowerCase() as 'a' | 'b' | 'c' | 'd';
            const optionText = optionMatch[2].trim();
            
            // Check for duplicate option letters
            if (seenLetters.has(letter)) {
              console.log(`Duplicate option letter "${letter}" found in word "${currentMCQ.word}". Skipping this MCQ.`);
              currentMCQ = null; // Mark as invalid
              return;
            }
            
            seenLetters.add(letter);
            currentMCQ.options[letter] = optionText;
            if (!optionLetterOrder.includes(letter)) {
              optionLetterOrder.push(letter);
            }
          } else if (itemText) {
            // If no letter prefix, assign to next available option
            const availableLetters = ['a', 'b', 'c', 'd'].filter(
              l => !optionLetterOrder.includes(l) && !seenLetters.has(l)
            );
            if (availableLetters.length > 0) {
              const letter = availableLetters[0] as 'a' | 'b' | 'c' | 'd';
              seenLetters.add(letter);
              currentMCQ.options[letter] = itemText;
              optionLetterOrder.push(letter);
            }
          }
        });
      }
    }
    
    // Handle other content in explanation section
    else if (token.type === 'blockquote' && currentMCQ && currentSection === 'explanation') {
      const quoteText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      currentMCQ.explanation += (currentMCQ.explanation ? ' ' : '') + quoteText;
    }
    
    // Recursive traversal for nested tokens
    if (token.tokens && Array.isArray(token.tokens)) {
      token.tokens.forEach(traverse);
    }
    
    // Handle list items
    if (token.type === 'list' && token.items && Array.isArray(token.items)) {
      token.items.forEach(traverse);
    }
  };
  
  // Start traversal
  tokens.forEach(traverse);
  
  // Add final MCQ if valid
  if (currentMCQ && isValidMCQ(currentMCQ)) {
    mcqItems.push(currentMCQ as MCQItem);
  }
  
  return mcqItems;
}

/**
 * Check if MCQ has required fields and at least 2 options
 */
function isValidMCQ(mcq: Partial<MCQItem>): boolean {
  if (!mcq.word || !mcq.questionText || !mcq.correctAnswer || !mcq.explanation) {
    return false;
  }
  
  // Count non-empty options
  const optionCount = Object.values(mcq.options || {}).filter(option => option && option.trim()).length;
  
  // Must have at least 2 options and the correct answer must exist in the options
  if (optionCount < 2) {
    return false;
  }
  
  // Check if the correct answer exists in the options
  const correctAnswerOption = mcq.options?.[mcq.correctAnswer];
  const correctAnswerExists = correctAnswerOption && correctAnswerOption.trim();
  if (!correctAnswerExists) {
    return false;
  }
  
  return true;
}

/**
 * Generate unique ID for MCQ
 */
function generateMCQId(): string {
  return `mcq_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
} 