import type { Card } from '~/utils/card';
import type { CreativeWritingItem } from './CreativeWritingTypes';

// Interface for card data formatted for LLM input
export interface LLMCardInput {
  id: string; // Unique ID of the original card for echoing
  word: string;
  definition: string;
}

// Interface for multiple-choice question items returned by LLM
export interface MCQItem {
  id: string; // Generated unique ID for the MCQ
  llmSourceCardId?: string; // Card ID as echoed by LLM (e.g., "123")
  word: string;
  questionType: string;
  questionText: string;
  options: {
    a: string;
    b: string;
    c?: string; // Optional - MCQ can have 2-4 options
    d?: string; // Optional - MCQ can have 2-4 options
  };
  correctAnswer: 'a' | 'b' | 'c' | 'd';
  explanation: string;
  originalCardId?: string; // Reference to the original card (always string for consistent comparison)
}

// Single interface for all question types (unified approach)
export interface TypeInQuestionItem {
  id: string;
  llmSourceCardId?: string;
  word: string;
  questionType: 'type-in' | 'fill-blank' | 'synonym' | 'antonym';
  questionText: string; // Definition, sentence with _____, or natural question with synonym/antonym
  hint?: string; // Helpful hint or context clue (for fill-blank, synonym, and antonym questions)
  targetAnswer: string;
  explanation: string;
  originalCardId?: string;
}

// Union type for review items in local session
export type LocalSessionReviewItem = {
  type: 'basic';
  data: Card;
} | {
  type: 'mcq';
  data: MCQItem;
} | {
  type: 'type-in';
  data: TypeInQuestionItem;
} | {
  type: 'creative-writing';
  data: CreativeWritingItem;
};