// Import Card type for potential future use
// import type { Card } from '~/utils/card';

// Interface for creative writing exercise items
export interface CreativeWritingItem {
  id: string; // Generated unique ID for the exercise
  llmSourceCardId?: string; // Card ID as echoed by LLM (e.g., "123")
  word: string; // Target word (not shown to user initially)
  context: string; // Scenario description
  prompt: string; // Writing instruction
  hints?: string[]; // Optional usage hints
  originalCardId?: string; // Reference to the original card
}

// Interface for writing evaluation results
export interface WritingEvaluation {
  overallScore: number; // 0-100
  wordUsed: boolean;
  wordUsedCorrectly: boolean;
  criteria: {
    grammar: EvaluationResult;
    fluency: EvaluationResult;
    context: EvaluationResult;
    wordUsage: EvaluationResult;
  };
  feedback: string;
  suggestions: string[];
  examples?: string[]; // Generated but only shown on demand
  canProgress: boolean;
}

// Interface for individual evaluation criterion result
export interface EvaluationResult {
  score: number; // 0-100
  feedback: string;
}

// Enum for evaluation criteria
export enum EvaluationCriteria {
  GRAMMAR = 'grammar',
  FLUENCY = 'fluency',
  CONTEXT = 'context',
  WORD_USAGE = 'wordUsage'
}

// Interface for writing evaluation state
export interface WritingEvaluationState {
  isEvaluating: boolean;
  evaluation: WritingEvaluation | null;
  userText: string;
  showExamples: boolean;
  attemptCount: number;
}

// Calculate delta score for spaced repetition based on overall performance
export function calculateDeltaScore(overallScore: number): number {
  if (overallScore > 80) return 1;   // Good performance, increase interval
  if (overallScore >= 50) return 0;  // Adequate performance, maintain interval
  return -1;                         // Needs improvement, decrease interval
}