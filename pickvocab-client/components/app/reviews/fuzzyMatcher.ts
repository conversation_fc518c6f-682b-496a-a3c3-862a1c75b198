export interface FuzzyMatchResult {
  isMatch: boolean;
  confidence: number;
  corrections?: string[];
}

export function fuzzyMatch(target: string, input: string): FuzzyMatchResult {
  const targetLower = target.toLowerCase().trim();
  const inputLower = input.toLowerCase().trim();
  
  // Exact match
  if (targetLower === inputLower) {
    return { isMatch: true, confidence: 1.0 };
  }
  
  // Calculate Levenshtein distance
  const distance = levenshteinDistance(targetLower, inputLower);
  const maxLength = Math.max(targetLower.length, inputLower.length);
  const similarity = 1 - (distance / maxLength);
  
  // Accept if similarity is high enough and distance is small
  const isMatch = distance <= 2 && similarity >= 0.7;
  
  return {
    isMatch,
    confidence: similarity,
    corrections: isMatch ? [target] : undefined
  };
}

function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }
  
  return matrix[str2.length][str1.length];
}