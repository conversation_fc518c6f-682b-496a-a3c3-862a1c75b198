import { useLLMStore } from '~/stores/llm';

export interface AIValidationResult {
  isValid: boolean;
  explanation: string;
}

export async function validateAnswerWithAI(
  questionText: string,
  targetAnswer: string,
  userAnswer: string
): Promise<AIValidationResult> {
  const llmStore = useLLMStore();
  
  const prompt = `<instructions>
You are evaluating whether a user's answer to a vocabulary question is semantically valid, even if it's not the exact target word.

### Your Task:
1. Analyze if the user's answer is a valid synonym, alternative form, or semantically equivalent word
2. Consider the context of the question to determine appropriateness
3. Be strict about precision - general words should not replace specific ones
4. Respond with VALID or INVALID followed by a clear explanation

### Evaluation Criteria:
- VALID: Synonyms, alternative forms, or words with the same meaning in this context
- INVALID: Opposite meanings, too general/specific, wrong part of speech, or contextually inappropriate

### Response Format:
- Do not use bold, italic, or any text formatting (no **, *, _, etc.)
- Provide clear, plain text explanations only
</instructions>

<context>
Question: "${questionText}"
Target Answer: "${targetAnswer}"
User Answer: "${userAnswer}"

We want to check if the user's answer is semantically valid as an alternative to the target answer in this specific context.
</context>

<output_format>
VALID: [explanation of why the answer is acceptable]

OR

INVALID: [explanation of why the answer is not acceptable]
</output_format>

<example-output>
VALID: "Huge" and "enormous" are synonyms that both mean exceptionally large, and both fit perfectly in this context.

INVALID: "Careful" is too general and lacks the precision of "meticulous," which specifically means showing great attention to detail.

VALID: "Everywhere" is a valid alternative form of "ubiquitous" and conveys the same meaning of being present in all places.

INVALID: "Hidden" is the opposite of "surreptitious" - while both involve secrecy, surreptitious means doing something secretly, not being hidden.
</example-output>

<input>
Question: "${questionText}"
Target Answer: "${targetAnswer}"  
User Answer: "${userAnswer}"
</input>`;

  try {
    // Step 1: Determine model configuration (following the same pattern as useAiReviewGenerator.ts)
    let modelConfigToUse = llmStore.activeUserModel;

    if (!modelConfigToUse) {
      const defaultModelId = llmStore.pickvocabChatSource?.modelId;
      if (typeof defaultModelId === 'number') {
        modelConfigToUse = llmStore.getModelById(defaultModelId);
      }
    }

    if (!modelConfigToUse) {
      throw new Error('No language model configuration available for AI validation');
    }

    // Step 2: Create chat source and send message
    const chatSource = llmStore.createChatSource(modelConfigToUse);
    if (!chatSource) {
      throw new Error(`Failed to create ChatSource for model: ${modelConfigToUse.name}`);
    }

    // Step 3: Send prompt and get response
    const result = await chatSource.sendMessage(prompt);
    
    const isValid = !/INVALID/i.test(result.message);
    const explanation = result.message.replace(/.*?(VALID|INVALID):?\s*/i, '').trim();
    
    return {
      isValid,
      explanation: explanation || (isValid ? 'Answer validated by AI' : 'Answer not accepted by AI')
    };
  } catch (error) {
    console.error('AI validation error:', error);
    throw new Error('AI validation failed');
  }
}