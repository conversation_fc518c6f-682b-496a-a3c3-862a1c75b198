import type { LLMCardInput } from './reviewTypes';

/**
 * Constructs the LLM prompt for generating multiple-choice questions (MCQs).
 */
export function constructMcqPrompt(formattedCards: LLMCardInput[]): string {
  return `<instructions>
You are an expert English vocabulary tutor. Generate engaging multiple-choice questions for vocabulary review based on provided flashcard data.

### Core Requirements:
1. Generate 1 question per card
2. Randomly select question types from these categories:
   - Contextual Sentence Completion
   - Definition Matching
   - Synonym Identification
   - Antonym Selection
   - Register Recognition
   - Collocation Matching
3. Follow the exact output format below
4. Make questions engaging and pedagogically sound
5. Ensure the exact word or phrase from the flashcard is used in the question or the correct answer option.
6. Provide a concise and succinct explanation for the correct answer, clarifying why it is correct and why distractors are incorrect.
7. IMPORTANT: Include the card ID from the input in the heading format as shown below.

### Question Quality Guidelines:
- Distractors must be plausible but incorrect
- Vary difficulty based on word complexity
- Include 1 humorous option where appropriate
- Ensure correct answer is UNAMBIGUOUS
- Maintain consistent formatting
</instructions>

<output_format>
## [word] (id: [CARD_ID_FROM_INPUT])

### [question_type]
[question_text]

#### Options
a) [distractor1]
b) [distractor2]
c) [distractor3]
d) [correct_answer]

#### Answer
d

#### Explanation
[explanation_text]
</output_format>

<input>
Cards:
${JSON.stringify(formattedCards, null, 2)}
</input>`;
}