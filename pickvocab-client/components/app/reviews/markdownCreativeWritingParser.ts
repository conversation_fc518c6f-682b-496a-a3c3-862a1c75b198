import { marked } from 'marked';
import type { CreativeWritingItem } from './CreativeWritingTypes';
import { getTextFromMarkedTokens, decodeHtmlEntities, type MarkedToken } from '../write/markedUtils';

/**
 * Parse markdown response from LLM into structured CreativeWritingItem objects
 * Error-tolerant parser that handles case insensitive headings, redundant whitespace, etc.
 * Following patterns from existing parsers in the codebase.
 */
export function parseMarkdownCreativeWriting(markdown: string): CreativeWritingItem[] {
  const tokens = marked.lexer(markdown);
  
  const creativeWritingItems: CreativeWritingItem[] = [];
  
  let currentItem: Partial<CreativeWritingItem> | null = null;
  let currentSection: string | null = null;
  
  const traverse = (token: MarkedToken) => {
    // Handle heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      // Level 2 heading: Word (starts new creative writing item)
      if (token.depth === 2 && headingText) {
        // Save previous item if complete and valid
        if (currentItem && isValidCreativeWritingItem(currentItem)) {
          creativeWritingItems.push(currentItem as CreativeWritingItem);
        }
        
        // Parse heading to extract word and card ID with flexible regex
        // Handles: "word (id: 123)", "word (ID: 123)", "word(123)", "word (123)"
        const match = headingText.match(/^(.*?)\s*\(.*?(\d+).*?\)$/i);
        
        if (match && match[1] && match[2]) {
          const parsedWord = match[1].trim();
          const parsedCardId = match[2].trim(); // Numeric string "123"
          
          // Initialize new creative writing item with extracted data
          currentItem = {
            id: generateCreativeWritingId(),
            llmSourceCardId: parsedCardId,
            word: parsedWord,
            context: '',
            prompt: '',
            hints: []
          };
          currentSection = null;
        } else {
          console.warn(`Creative Writing Parser: Could not parse word and numeric card ID from heading: "${headingText}". Expected format: "word (123)" or "word (id: 123)".`);
          // Fallback logic - save previous item if valid and skip this one
          if (currentItem && isValidCreativeWritingItem(currentItem)) { 
            creativeWritingItems.push(currentItem as CreativeWritingItem);
          }
          currentItem = null; 
        }
      }
      // Level 3 heading: Section (Context, Prompt, Hint)
      else if (token.depth === 3 && currentItem) {
        const normalizedHeading = headingText.toLowerCase().trim();
        
        if (normalizedHeading.includes('context')) {
          currentSection = 'context';
        } else if (normalizedHeading.includes('prompt')) {
          currentSection = 'prompt';
        } else if (normalizedHeading.includes('hint')) {
          currentSection = 'hint';
        } else {
          // Unknown section, ignore but continue parsing
          currentSection = null;
        }
      }
    }
    
    // Handle paragraph tokens
    else if (token.type === 'paragraph' && currentItem && currentSection) {
      const paragraphText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      if (currentSection === 'context') {
        currentItem.context += (currentItem.context ? ' ' : '') + paragraphText;
      } else if (currentSection === 'prompt') {
        currentItem.prompt += (currentItem.prompt ? ' ' : '') + paragraphText;
      } else if (currentSection === 'hint') {
        // Hints can be multiple paragraphs, so we accumulate them
        if (!currentItem.hints) {
          currentItem.hints = [];
        }
        currentItem.hints.push(paragraphText);
      }
    }
    
    // Handle list tokens (for hints)
    else if (token.type === 'list' && currentItem && currentSection === 'hint') {
      if (token.items && Array.isArray(token.items)) {
        if (!currentItem.hints) {
          currentItem.hints = [];
        }
        
        token.items.forEach((item: MarkedToken) => {
          const itemText = item.tokens 
            ? getTextFromMarkedTokens(item.tokens) 
            : decodeHtmlEntities(item.text || '').trim();
          
          if (itemText && currentItem?.hints) {
            currentItem.hints.push(itemText);
          }
        });
      }
    }
    
    // Handle other content in hint section (like blockquotes)
    else if (token.type === 'blockquote' && currentItem && currentSection === 'hint') {
      const quoteText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      if (!currentItem.hints) {
        currentItem.hints = [];
      }
      currentItem.hints.push(quoteText);
    }
    
    // Recursive traversal for nested tokens
    if (token.tokens && Array.isArray(token.tokens)) {
      token.tokens.forEach(traverse);
    }
    
    // Handle list items
    if (token.type === 'list' && token.items && Array.isArray(token.items)) {
      token.items.forEach(traverse);
    }
  };
  
  // Start traversal
  tokens.forEach(traverse);
  
  // Add final item if valid
  if (currentItem && isValidCreativeWritingItem(currentItem)) {
    creativeWritingItems.push(currentItem as CreativeWritingItem);
  }
  
  return creativeWritingItems;
}

/**
 * Check if creative writing item has required fields
 */
function isValidCreativeWritingItem(item: Partial<CreativeWritingItem>): boolean {
  if (!item.word || !item.context || !item.prompt) {
    return false;
  }
  
  // Context and prompt should have meaningful content
  if (item.context.trim().length < 10 || item.prompt.trim().length < 10) {
    return false;
  }
  
  return true;
}

/**
 * Generate unique ID for creative writing item
 */
function generateCreativeWritingId(): string {
  return `cw_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}