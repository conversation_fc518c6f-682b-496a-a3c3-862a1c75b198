import type { Card } from '~/utils/card';

export interface ReviewSessionSummary {
  totalCards: number;
  correctAnswers: number;
  incorrectAnswers: number;
  accuracyPercentage: number;
  timeSpent?: number; // Future enhancement
  vocabularyResults: VocabularyResult[];
  reviewModes: ('basic' | 'mcq' | 'type-in' | 'creative-writing')[];
  sessionDate: Date;
  sessionId: string;
}

export interface VocabularyResult {
  card: Card;
  wasCorrect: boolean;
  deltaScore: number;
  previousMasteryLevel: string;
  newMasteryLevel: string;
  reviewType: 'basic' | 'mcq' | 'type-in' | 'creative-writing';
  questionText?: string; // For MCQ and type-in items
}

export interface ReviewSummaryDisplayData {
  celebrationMessage: string;
  performanceLevel: 'excellent' | 'good' | 'needs-practice';
  incorrectWords: VocabularyResult[];
}

// Mastery level mapping for progress_score
export const MASTERY_LEVELS = {
  0: 'New',
  1: 'Learning',
  2: 'Learning', 
  3: 'Familiar',
  4: 'Well Known',
  5: 'Mastered'
} as const;

export type MasteryLevel = typeof MASTERY_LEVELS[keyof typeof MASTERY_LEVELS];