import { marked } from 'marked';
import type { WritingEvaluation } from './CreativeWritingTypes';
import { getTextFromMarkedTokens, decodeHtmlEntities, type MarkedToken } from '../write/markedUtils';

/**
 * Parse markdown response from LLM into structured WritingEvaluation object
 * Error-tolerant parser following the same patterns as other parsers in the codebase
 */
export function parseMarkdownWritingEvaluation(markdown: string): WritingEvaluation {
  const tokens = marked.lexer(markdown);
  
  // Initialize with defaults
  let evaluation: Partial<WritingEvaluation> = {
    overallScore: 50,
    wordUsed: false,
    wordUsedCorrectly: false,
    criteria: {
      grammar: { score: 50, feedback: '' },
      fluency: { score: 50, feedback: '' },
      context: { score: 50, feedback: '' },
      wordUsage: { score: 50, feedback: '' }
    },
    feedback: '',
    suggestions: [],
    examples: [],
    canProgress: false
  };
  
  let currentSection: string | null = null;
  
  const traverse = (token: MarkedToken) => {
    // Handle heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      const normalizedHeading = headingText.toLowerCase().trim();
      
      // Level 1 or 2 heading: Main sections
      if (token.depth <= 2) {
        if (normalizedHeading.includes('evaluation') || normalizedHeading.includes('results')) {
          currentSection = 'main';
        } else if (normalizedHeading.includes('word usage')) {
          currentSection = 'word-usage';
        } else if (normalizedHeading.includes('scores')) {
          currentSection = 'scores';
        } else if (normalizedHeading.includes('feedback')) {
          currentSection = 'feedback';
        } else if (normalizedHeading.includes('issues')) {
          currentSection = 'issues';
        } else if (normalizedHeading.includes('suggestions')) {
          currentSection = 'suggestions';
        } else if (normalizedHeading.includes('examples')) {
          currentSection = 'examples';
        }
      }
    }
    
    // Handle paragraph content
    else if (token.type === 'paragraph' && currentSection) {
      const paragraphText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      
      if (currentSection === 'word-usage') {
        parseWordUsage(paragraphText, evaluation);
      } else if (currentSection === 'scores') {
        parseScores(paragraphText, evaluation);
      } else if (currentSection === 'feedback') {
        if (evaluation.feedback) {
          evaluation.feedback += ' ' + paragraphText;
        } else {
          evaluation.feedback = paragraphText;
        }
      } else if (currentSection === 'suggestions') {
        if (paragraphText.trim()) {
          evaluation.suggestions?.push(paragraphText);
        }
      } else if (currentSection === 'examples') {
        if (paragraphText.trim()) {
          evaluation.examples?.push(paragraphText);
        }
      }
    }
    
    // Handle list tokens
    else if (token.type === 'list') {
      if (token.items && Array.isArray(token.items)) {
        token.items.forEach((item: MarkedToken) => {
          const itemText = item.tokens 
            ? getTextFromMarkedTokens(item.tokens) 
            : decodeHtmlEntities(item.text || '').trim();
          
          
          if (currentSection === 'word-usage' && itemText.trim()) {
            parseWordUsage(itemText, evaluation);
          } else if (currentSection === 'suggestions' && itemText.trim()) {
            evaluation.suggestions?.push(itemText);
          } else if (currentSection === 'examples' && itemText.trim()) {
            evaluation.examples?.push(itemText);
          } else if (currentSection === 'scores') {
            parseScores(itemText, evaluation);
          }
        });
      }
    }
    
    // Recursive traversal for nested tokens
    if (token.tokens && Array.isArray(token.tokens)) {
      token.tokens.forEach(traverse);
    }
    
    // Handle list items
    if (token.type === 'list' && token.items && Array.isArray(token.items)) {
      token.items.forEach(traverse);
    }
  };
  
  // Start traversal
  tokens.forEach(traverse);
  
  // Calculate overall score from criteria if not explicitly found
  if (evaluation.criteria && evaluation.overallScore === 50) { // Only recalculate if still at default
    // If word is not used at all, overall score should be 0
    if (!evaluation.wordUsed) {
      evaluation.overallScore = 0;
    } else {
      const scores = Object.values(evaluation.criteria).map(c => c.score);
      if (scores.length > 0) {
        evaluation.overallScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
      }
    }
  }
  
  // Determine if user can progress
  evaluation.canProgress = evaluation.wordUsed && evaluation.overallScore! >= 50;
  
  return evaluation as WritingEvaluation;
}

/**
 * Parse word usage information from text
 */
function parseWordUsage(text: string, evaluation: Partial<WritingEvaluation>) {
  const normalizedText = text.toLowerCase();
  
  // Look for "- Used: Yes/No" pattern (with bullet point)
  const usedMatch = normalizedText.match(/[-•]\s*used:?\s*(yes|no)/i) || 
                   normalizedText.match(/used:?\s*(yes|no)/i);
  if (usedMatch) {
    evaluation.wordUsed = usedMatch[1].toLowerCase() === 'yes';
  }
  
  // Look for "- Used Correctly: Yes/No" pattern (with bullet point)
  const correctlyMatch = normalizedText.match(/[-•]\s*used\s+correctly:?\s*(yes|no)/i) || 
                        normalizedText.match(/used\s+correctly:?\s*(yes|no)/i);
  if (correctlyMatch) {
    evaluation.wordUsedCorrectly = correctlyMatch[1].toLowerCase() === 'yes';
  }
}

/**
 * Parse scoring information from text
 */
function parseScores(text: string, evaluation: Partial<WritingEvaluation>) {
  
  // Look for criterion scores with patterns that match both bold and plain text formats
  const scorePatterns = [
    { pattern: /(?:\*\*)?Grammar:?\*?\*?\s*(\d+)(?:\/100)?/i, criterion: 'grammar' },
    { pattern: /(?:\*\*)?Fluency:?\*?\*?\s*(\d+)(?:\/100)?/i, criterion: 'fluency' },
    { pattern: /(?:\*\*)?Context(?:\s+Fit)?:?\*?\*?\s*(\d+)(?:\/100)?/i, criterion: 'context' },
    { pattern: /(?:\*\*)?Word\s+Usage:?\*?\*?\s*(\d+)(?:\/100)?/i, criterion: 'wordUsage' }
  ];
  
  scorePatterns.forEach(({ pattern, criterion }) => {
    const match = text.match(pattern);
    if (match && evaluation.criteria) {
      const score = parseInt(match[1], 10);
      if (evaluation.criteria[criterion as keyof typeof evaluation.criteria]) {
        (evaluation.criteria[criterion as keyof typeof evaluation.criteria] as any).score = score;
      }
    }
  });
  
  // Look for overall score
  const overallMatch = text.match(/overall:?\s*(\d+)(?:\/100)?/i);
  if (overallMatch) {
    evaluation.overallScore = parseInt(overallMatch[1], 10);
  }
}

