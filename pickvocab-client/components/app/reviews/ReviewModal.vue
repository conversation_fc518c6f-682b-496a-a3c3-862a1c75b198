<script setup lang="ts">
// import { IconBolt, IconX } from '@tabler/icons-vue';
// @ts-ignore
import IconBolt from '@tabler/icons-vue/dist/esm/icons/IconBolt.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
// @ts-ignore
import IconNumber123 from '@tabler/icons-vue/dist/esm/icons/IconNumber123.mjs';
import type { Modal } from 'flowbite';
import SubmitButton from '../../ui/SubmitButton.vue';
import type { Card } from '~/utils/card';
import type { BaseReview } from '~/api/review/types';
import type { LocalSessionReviewItem } from './reviewTypes';
import { useAiReviewGenerator } from './useAiReviewGenerator';
import { useInfiniteDecksSearchQueryWithAbort } from '~/composables/useInfiniteDecksSearchQuery';
import { useIntersectionObserver } from '@vueuse/core';
import { shuffle } from 'lodash-es';

const store = useAppStore();
const authStore = useAuthStore();
const router = useRouter();
const { isShowReviewModal } = storeToRefs(store);

const selectedDecks = ref<Deck[]>([]);
const deckSelectedIdx = ref(0);
const searchDeckText = ref('');
const deckRefs = ref([]);
const isSubmitting = ref(false);

// Infinite scroll for deck search
const { 
  isPending: isDecksLoading, 
  isFetching: isDecksRefreshing,
  hasNextPage, 
  fetchNextPage, 
  isFetchingNextPage,
  error: decksError,
  allDecks,
  totalCount,
  cancelCurrentRequest 
} = useInfiniteDecksSearchQueryWithAbort(searchDeckText);

// Intersection observer for infinite scroll
const loadMoreRef = ref<HTMLElement | null>(null);
const scrollContainerRef = ref<HTMLElement | null>(null);

const { stop: stopIntersectionObserver } = useIntersectionObserver(
  loadMoreRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && hasNextPage.value && !isFetchingNextPage.value) {
      fetchNextPage();
    }
  },
  { 
    threshold: 0.1,
    // Use the scroll container as the root for intersection observation
    root: scrollContainerRef
  }
);

// Computed suggested decks with master notebook
const suggestedDecks = computed(() => {
  const masterNotebook = {
    id: -1,
    name: 'Master notebook',
    description: 'Contains all of your cards',
    cards: [],
    totalCards: 0,
    owner: authStore.currentUser?.id,
    isDemo: false,
  } as Deck;

  // Only show master notebook when search is empty
  if (!searchDeckText.value.trim()) {
    return [masterNotebook, ...allDecks.value];
  }
  
  return allDecks.value;
});

// Phase 1: Review Mode Selection State
const useBasicReview = ref(true);
const useAiReview = ref(false);
const useTypeInReview = ref(false);
const useCreativeWriting = ref(false);

// Phase 1: Card Division Variables
const sessionCardsFromBackend = ref<Card[]>([]);
const basicReviewCards = ref<Card[]>([]);
const aiQuestionCards = ref<Card[]>([]);
const typeInQuestionCards = ref<Card[]>([]);
const creativeWritingCards = ref<Card[]>([]);

// Phase 3: Local Session Review Items
const localSessionReviewItems = ref<LocalSessionReviewItem[]>([]);

// Initialize AI review generator
const { generateMcqs, generateTypeInQuestions, generateCreativeWritingExercises } = useAiReviewGenerator();

// Initialize review session composable
const { setLocalSessionReviewItems } = useReviewSession();

let modal: Modal;

// Added: user-selectable question count (1-10)
const questionCount = ref(10);

// Phase 1: Computed property for button disable logic
const isReviewButtonDisabled = computed(() => {
  return (!useBasicReview.value && !useAiReview.value && !useTypeInReview.value && !useCreativeWriting.value) ||
         questionCount.value < 1 || questionCount.value > 10;
});

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $modelEl = document.getElementById('review-modal');
    modal = new Modal($modelEl);

    modal.updateOnShow(async () => {
      searchDeckText.value = '';
      // Reset review mode selections
      useBasicReview.value = true;
      useAiReview.value = false;
      useTypeInReview.value = false;
      useCreativeWriting.value = false;
      // Reset question count
      questionCount.value = 10;
      
      // Initialize with master notebook selected
      const masterNotebook = {
        id: -1,
        name: 'Master notebook',
        description: 'Contains all of your cards',
        cards: [],
        totalCards: 0,
        owner: authStore.currentUser?.id,
        isDemo: false,
      } as Deck;

      selectedDecks.value = [masterNotebook];
      deckSelectedIdx.value = 0;
      window.addEventListener('keydown', handleKeyPress, true);
    });

    modal.updateOnHide(() => {
      store.hideReviewModal();
      window.removeEventListener('keydown', handleKeyPress, true);
      // Cancel any ongoing requests when modal closes
      cancelCurrentRequest();
      // Stop intersection observer
      stopIntersectionObserver();
    });
  });
});

watch(isShowReviewModal, (value) => {
  if (value === true) {
    modal.show();
  } else {
    modal.hide();
  }
});

watch(deckSelectedIdx, (value) => {
  (deckRefs.value[value] as any).scrollIntoView(false);
});

// Watch for changes in suggested decks to update selected index
watch(() => suggestedDecks.value.length, (newLength) => {
  if (deckSelectedIdx.value >= newLength && newLength > 0) {
    deckSelectedIdx.value = 0;
  }
});

function addSelectedDeck(idx: number) { // suggestedDecks idx
  const selectedDeckIdx = selectedDecks.value.findIndex((deck) => deck.id === suggestedDecks.value[idx].id);
  if (selectedDeckIdx !== -1) {
    removeDeck(selectedDeckIdx);
    return;
  }
  selectedDecks.value.push(suggestedDecks.value[idx]);
}

function removeDeck(idx: number) { // selectedDeck idx
  if (idx >= 0) {
    selectedDecks.value.splice(idx, 1);
  }
}

function isInSelectedDecks(deck: Deck) {
  return selectedDecks.value.find((d) => d.id === deck.id);
}

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'ArrowUp') {
    e.stopPropagation();
    e.preventDefault();
    deckSelectedIdx.value = Math.max(0, deckSelectedIdx.value - 1);
  } else if (e.key === 'ArrowDown') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDecks.value.length > 0) {
      deckSelectedIdx.value = Math.min(suggestedDecks.value.length - 1, deckSelectedIdx.value + 1);
    }
  } else if (e.key === 'Enter') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDecks.value.length > 0) {
      addSelectedDeck(deckSelectedIdx.value);
      searchDeckText.value = '';
    }
  }
}

// Phase 1: Card Division Logic
function divideCards() {
  const cards = [...sessionCardsFromBackend.value];
  basicReviewCards.value = [];
  aiQuestionCards.value = [];
  typeInQuestionCards.value = [];
  creativeWritingCards.value = [];

  const modes = [
    useBasicReview.value ? 'basic' : null,
    useAiReview.value ? 'mcq' : null,
    useTypeInReview.value ? 'type-in' : null,
    useCreativeWriting.value ? 'creative-writing' : null,
  ].filter(Boolean);

  if (modes.length === 0) return;

  const total = cards.length;
  const perMode = Math.floor(total / modes.length);
  let remainder = total % modes.length;

  for (const mode of modes) {
    let count = perMode + (remainder > 0 ? 1 : 0);
    if (remainder > 0) remainder--;

    const assignedCards = cards.splice(0, count);
    if (mode === 'basic') {
      basicReviewCards.value.push(...assignedCards);
    } else if (mode === 'mcq') {
      aiQuestionCards.value.push(...assignedCards);
    } else if (mode === 'type-in') {
      typeInQuestionCards.value.push(...assignedCards);
    } else if (mode === 'creative-writing') {
      creativeWritingCards.value.push(...assignedCards);
    }
  }
}

async function createSetOfReviewedCards() {
  if (isReviewButtonDisabled.value) {
    return;
  }
  
  isSubmitting.value = true;
  
  try {
    const baseReview: BaseReview = {
      deckIds: selectedDecks.value.filter((deck) => deck.id !== -1).map((deck) => deck.id),
      isMaster: selectedDecks.value.some((deck) => deck.id === -1),
      cards: [],
    }
    const review = await store.createReview(baseReview);
    
    // Phase 1: Store cards from backend and divide them
    const shuffledCards = shuffle(review.cards.map(rc => rc.card));
    sessionCardsFromBackend.value = shuffledCards.slice(0, Math.min(questionCount.value, shuffledCards.length));
    divideCards();
    
    // Phase 3: Consolidate Review Items
    localSessionReviewItems.value = [];
    
    // Add basic review cards to localSessionReviewItems
    for (const card of basicReviewCards.value) {
      localSessionReviewItems.value.push({
        type: 'basic',
        data: card
      });
    }
    
    // Generate AI MCQs if needed
    // Generate AI MCQs if needed
    if (aiQuestionCards.value.length > 0) {
      try {
        const parsedMcqList = await generateMcqs(aiQuestionCards.value);
        for (const mcq of parsedMcqList) {
          localSessionReviewItems.value.push({ type: 'mcq', data: mcq });
        }
      } catch (error) {
        console.error('Error generating AI MCQs, falling back to basic:', error);
        for (const card of aiQuestionCards.value) {
          localSessionReviewItems.value.push({ type: 'basic', data: card });
        }
      }
    }

    // Generate Type-in questions if needed
    if (typeInQuestionCards.value.length > 0) {
      try {
        const parsedList = await generateTypeInQuestions(typeInQuestionCards.value);
        for (const item of parsedList) {
          localSessionReviewItems.value.push({ type: 'type-in', data: item });
        }
      } catch (error) {
        console.error('Error generating Type-in questions, falling back to basic:', error);
        for (const card of typeInQuestionCards.value) {
          localSessionReviewItems.value.push({ type: 'basic', data: card });
        }
      }
    }

    // Generate Creative Writing exercises if needed
    if (creativeWritingCards.value.length > 0) {
      try {
        const parsedList = await generateCreativeWritingExercises(creativeWritingCards.value);
        for (const item of parsedList) {
          localSessionReviewItems.value.push({ type: 'creative-writing', data: item });
        }
      } catch (error) {
        console.error('Error generating Creative Writing exercises, falling back to basic:', error);
        for (const card of creativeWritingCards.value) {
          localSessionReviewItems.value.push({ type: 'basic', data: card });
        }
      }
    }
    
    // Shuffle if more than one review mode is active
    const activeModes = [useBasicReview.value, useAiReview.value, useTypeInReview.value, useCreativeWriting.value].filter(Boolean).length;
    if (activeModes > 1) {
      localSessionReviewItems.value = shuffle(localSessionReviewItems.value);
    }
    
    
    modal.hide();
    
    // Phase 3: Store localSessionReviewItems in composable and navigate
    setLocalSessionReviewItems(localSessionReviewItems.value);
    
    router.push({ 
      name: 'app-reviews-id', 
      params: { id: review.id }
    });
  } catch (error) {
    console.error('Error creating review:', error);
    // Optional: Show error message to user
  } finally {
    isSubmitting.value = false;
  }
}
</script>

<template>
  <!-- Main modal -->
  <div id="review-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <icon-bolt stroke-width="2.5" class="inline-block w-5 h-5 me-3"></icon-bolt>
            <span>Review</span>
          </h3>
          <button type="button" @click="store.hideReviewModal()"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <form class="p-6">
          <!-- Phase 1: Review Mode Selection UI -->
          <div class="mb-8">
            <div class="flex items-center mb-6">
              <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
              <h3 class="text-lg font-semibold text-gray-900">Review Mode</h3>
            </div>
            
            <div class="space-y-3 mb-4">
              <div class="relative">
                <input 
                  id="basic-review" 
                  type="checkbox" 
                  v-model="useBasicReview"
                  class="sr-only peer"
                >
                <label for="basic-review" class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all duration-200">
                  <div class="flex items-center justify-center w-5 h-5 mr-4">
                    <div class="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center transition-all duration-200"
                         :class="{ 'border-blue-500 bg-blue-500': useBasicReview }">
                      <svg v-if="useBasicReview" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center">
                      <span class="text-sm font-semibold text-gray-800 mr-2">Basic review</span>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Classic
                      </span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">Traditional flashcard-style review with front and back sides</p>
                  </div>
                </label>
              </div>

              <div class="relative">
                <input 
                  id="ai-review" 
                  type="checkbox" 
                  v-model="useAiReview"
                  class="sr-only peer"
                >
                <label for="ai-review" class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-purple-500 peer-checked:bg-purple-50 transition-all duration-200">
                  <div class="flex items-center justify-center w-5 h-5 mr-4">
                    <div class="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center transition-all duration-200"
                         :class="{ 'border-purple-500 bg-purple-500': useAiReview }">
                      <svg v-if="useAiReview" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center">
                      <span class="text-sm font-semibold text-gray-800 mr-2">Multiple-choice questions</span>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        ✨ AI
                      </span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">Interactive multiple-choice questions generated by AI</p>
                  </div>
                </label>
              </div>

              <div class="relative">
                <input
                  id="type-in-review"
                  type="checkbox"
                  v-model="useTypeInReview"
                  class="sr-only peer"
                >
                <label for="type-in-review" class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-green-500 peer-checked:bg-green-50 transition-all duration-200">
                  <div class="flex items-center justify-center w-5 h-5 mr-4">
                    <div class="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center transition-all duration-200"
                         :class="{ 'border-green-500 bg-green-500': useTypeInReview }">
                      <svg v-if="useTypeInReview" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center">
                      <span class="text-sm font-semibold text-gray-800 mr-2">Type-in questions</span>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        🎯 Active Recall
                      </span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">Type answers and fill in blanks for better retention</p>
                  </div>
                </label>
              </div>

              <div class="relative">
                <input
                  id="creative-writing-review"
                  type="checkbox"
                  v-model="useCreativeWriting"
                  class="sr-only peer"
                >
                <label for="creative-writing-review" class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-orange-500 peer-checked:bg-orange-50 transition-all duration-200">
                  <div class="flex items-center justify-center w-5 h-5 mr-4">
                    <div class="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center transition-all duration-200"
                         :class="{ 'border-orange-500 bg-orange-500': useCreativeWriting }">
                      <svg v-if="useCreativeWriting" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center">
                      <span class="text-sm font-semibold text-gray-800 mr-2">Creative writing</span>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        ✍️ Production
                      </span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">Write sentences and paragraphs using vocabulary in context</p>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <!-- Question Count Section -->
          <div class="mb-8">
            <div class="flex items-center mb-6">
              <icon-number123 class="w-5 h-5 text-orange-600 mr-3"></icon-number123>
              <h3 class="text-lg font-semibold text-gray-900">Number of questions</h3>
            </div>
            <input
              type="number"
              v-model.number="questionCount"
              min="1"
              max="10"
              class="bg-gray-50 border border-gray-300 text-gray-700 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-24 p-3"
            />
            <p class="text-xs text-gray-500 mt-2">Enter any value between 1 and 10</p>
          </div>

          <!-- Notebooks Selection Section -->
          <div class="mb-8">
            <div class="flex items-center mb-6">
              <svg class="w-5 h-5 text-gray-700 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <h3 class="text-lg font-semibold text-gray-900">Select Notebooks</h3>
            </div>

            <div class="space-y-4">
              <div>
                <input type="text"
                  class="bg-gray-50 border border-gray-300 text-gray-700 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-3 transition-colors duration-200"
                  v-model="searchDeckText" 
                  placeholder="Search notebooks...">
              </div>

              <div v-if="selectedDecks.length > 0" class="flex flex-wrap gap-2">
                <div v-for="(deck, idx) in selectedDecks"
                  class="flex items-center bg-blue-100 text-blue-800 py-2 px-3 rounded-lg text-sm font-medium">
                  <span class="mr-2" :class="{ 'font-bold': deck.id === -1 }">{{ deck.name }}</span>
                  <button @click="removeDeck(idx)" class="text-blue-600 hover:text-blue-800 transition-colors">
                    <icon-x class="w-4 h-4"></icon-x>
                  </button>
                </div>
              </div>

              <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div ref="scrollContainerRef" class="max-h-[300px] overflow-y-auto">
                  <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-700">Available notebooks</p>
                      <!-- Background sync indicator -->
                      <div 
                        v-if="isDecksRefreshing && !isDecksLoading"
                        class="flex items-center space-x-2"
                        aria-label="Syncing notebooks"
                      >
                        <div class="w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        <span class="text-xs text-gray-400">Syncing...</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Error state -->
                  <div v-if="decksError" class="p-4 text-center">
                    <p class="text-red-500 text-sm mb-2">Error loading notebooks: {{ decksError.message }}</p>
                    <button 
                      @click="() => { searchDeckText = searchDeckText + ' ' }" 
                      class="text-xs px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                    >
                      Retry
                    </button>
                  </div>
                  
                  <!-- Deck list -->
                  <ul v-else class="divide-y divide-gray-100">
                    <!-- Initial loading state -->
                    <li v-if="isDecksLoading && suggestedDecks.length === 0" class="p-4 text-center">
                      <div class="flex items-center justify-center space-x-2">
                        <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        <span class="text-sm text-gray-500">Loading notebooks...</span>
                      </div>
                    </li>
                    
                    <!-- Deck items -->
                    <li v-for="(deck, idx) in suggestedDecks" 
                        :key="`${deck.id}-${deck.name}`"
                        ref="deckRefs"
                        class="cursor-pointer p-4 transition-colors duration-150"
                        :class="{
                          'hover:bg-gray-50': !isInSelectedDecks(deck),
                        }" 
                        @click="addSelectedDeck(idx)">
                      <div class="flex items-center justify-between">
                        <div class="flex-1">
                          <div class="flex items-center">
                            <span v-if="deck.id === -1" class="mr-2">⭐</span>
                            <span class="text-sm font-semibold" 
                                  :class="isInSelectedDecks(deck) ? 'text-blue-800' : 'text-gray-800'">
                              {{ deck.name }}
                            </span>
                          </div>
                          <p v-if="deck.description" class="text-xs mt-1"
                             :class="isInSelectedDecks(deck) ? 'text-blue-600' : 'text-gray-600'">
                            {{ deck.description }}
                          </p>
                        </div>
                        <div v-if="isInSelectedDecks(deck)" class="ml-3">
                          <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </li>
                    
                    <!-- Infinite scroll trigger and load more -->
                    <li v-if="hasNextPage" 
                        ref="loadMoreRef" 
                        class="p-4 text-center">
                      <div v-if="isFetchingNextPage" class="flex items-center justify-center space-x-2">
                        <div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <span class="text-sm text-gray-500">Loading more...</span>
                      </div>
                      <button v-else 
                              @click="fetchNextPage()"
                              class="text-sm px-4 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors">
                        <span v-if="searchDeckText.trim()">
                          Load More ({{ totalCount - allDecks.length }} remaining)
                        </span>
                        <span v-else>
                          Load More Notebooks
                        </span>
                      </button>
                    </li>
                    
                    
                    <!-- No results state -->
                    <li v-if="!isDecksLoading && suggestedDecks.length === 0 && searchDeckText.trim()" 
                        class="p-4 text-center">
                      <p class="text-sm text-gray-500">No notebooks found for "{{ searchDeckText }}"</p>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Button -->
          <div class="flex justify-end">
            <SubmitButton 
              text="Start"
              loadingText="Creating review set..."
              :isLoading="isSubmitting" 
              :disabled="isReviewButtonDisabled"
              @click="createSetOfReviewedCards" 
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
