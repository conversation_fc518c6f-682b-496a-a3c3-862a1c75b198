<script setup lang="ts">
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';

import type { VocabularyResult } from './reviewSummaryTypes';
import { CardType } from '~/utils/card';

interface Props {
  result: VocabularyResult;
}

const props = defineProps<Props>();

// Computed properties for display
const cardWord = computed(() => {
  if (props.result.card.cardType === CardType.DefinitionCard) {
    return props.result.card.word;
  } else if (props.result.card.cardType === CardType.ContextCard) {
    return props.result.card.wordInContext.word;
  }
  return 'Unknown';
});

const cardDefinition = computed(() => {
  if (props.result.card.cardType === CardType.DefinitionCard) {
    return props.result.card.definition?.definition || 'No definition';
  } else if (props.result.card.cardType === CardType.ContextCard) {
    return props.result.card.wordInContext.definition?.definition || props.result.card.wordInContext.definitionShort?.explanation || 'No definition';
  }
  return 'No definition';
});

const progressionText = computed(() => {
  if (props.result.previousMasteryLevel === props.result.newMasteryLevel) {
    return props.result.newMasteryLevel;
  }
  return `${props.result.previousMasteryLevel} → ${props.result.newMasteryLevel}`;
});

const hasProgressed = computed(() => {
  return props.result.previousMasteryLevel !== props.result.newMasteryLevel && props.result.wasCorrect;
});

// Navigation function
function navigateToCard() {
  const cardUrl = `/app/cards/${props.result.card.id}`;
  window.open(cardUrl, '_blank');
}
</script>

<template>
  <div 
    @click="navigateToCard"
    class="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md cursor-pointer transition-all duration-200"
  >
    <!-- Top/Left side: Word and definition -->
    <div class="flex-1 min-w-0">
      <div class="flex items-start sm:items-center gap-3">
        <!-- Correct/Incorrect indicator -->
        <div class="flex-shrink-0 mt-1 sm:mt-0">
          <div 
            v-if="result.wasCorrect"
            class="flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 bg-green-100 rounded-full"
          >
            <IconCheck class="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
          </div>
          <div 
            v-else
            class="flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 bg-red-100 rounded-full"
          >
            <IconX class="w-3 h-3 sm:w-4 sm:h-4 text-red-600" />
          </div>
        </div>

        <!-- Word and definition -->
        <div class="min-w-0 flex-1">
          <div class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
            <h3 class="text-base sm:text-lg font-semibold text-gray-900 break-words">
              {{ cardWord }}
            </h3>
            <!-- Review type indicator -->
            <span 
              v-if="result.reviewType === 'mcq'"
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800 self-start sm:self-auto"
            >
              MCQ
            </span>
            <span 
              v-else-if="result.reviewType === 'type-in'"
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 self-start sm:self-auto"
            >
              Type-in
            </span>
            <span 
              v-else-if="result.reviewType === 'creative-writing'"
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 text-orange-800 self-start sm:self-auto"
            >
              Creative Writing
            </span>
          </div>
          <p class="text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2 sm:truncate">
            {{ cardDefinition }}
          </p>
          <!-- Show question text if available (for MCQ and Type-in) -->
          <p v-if="result.questionText" class="text-xs text-gray-500 mt-1 italic line-clamp-2">
            "{{ result.questionText }}"
          </p>
        </div>
      </div>
    </div>

    <!-- Bottom/Right side: Mastery progression -->
    <div class="flex-shrink-0 mt-3 sm:mt-0 sm:ml-4 flex justify-between sm:justify-end">
      <div class="flex items-center gap-2">
        <span 
          class="text-xs sm:text-sm font-medium"
          :class="{
            'text-green-600': hasProgressed,
            'text-gray-600': !hasProgressed
          }"
        >
          {{ progressionText }}
        </span>
      </div>
    </div>
  </div>
</template>