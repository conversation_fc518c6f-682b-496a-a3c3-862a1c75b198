<script setup lang="ts">
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
// @ts-ignore
import IconEye from '@tabler/icons-vue/dist/esm/icons/IconEye.mjs';
// @ts-ignore
import IconEdit from '@tabler/icons-vue/dist/esm/icons/IconEdit.mjs';
// @ts-ignore
import IconLoader from '@tabler/icons-vue/dist/esm/icons/IconLoader.mjs';
// @ts-ignore
import IconLightbulb from '@tabler/icons-vue/dist/esm/icons/IconBulb.mjs';
// @ts-ignore
import IconList from '@tabler/icons-vue/dist/esm/icons/IconList.mjs';
import type { CreativeWritingItem, WritingEvaluation } from './CreativeWritingTypes';
import type { Card } from '~/utils/card';
import { useWritingEvaluator } from './WritingEvaluator';
import ReviewCardBackView from './ReviewCardBackView.vue';

interface Props {
  writingData: CreativeWritingItem;
  originalCard?: Card;
}

interface Emits {
  (e: 'answer', deltaScore: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Initialize the writing evaluator
const { evaluateWriting, calculateDeltaScore } = useWritingEvaluator();

const userText = ref('');
const isSubmitted = ref(false);
const isEvaluating = ref(false);
const showCardView = ref(false);
const showHints = ref(false);
const textArea = ref<HTMLTextAreaElement | null>(null);

// State for evaluation results
const evaluationResult = ref<WritingEvaluation | null>(null);
const showExamples = ref(false);
const previousExamples = ref<string[]>([]);
const showPreviousExamples = ref(false);

// Character/word counter
const charCount = computed(() => userText.value.length);
const wordCount = computed(() => {
  if (!userText.value.trim()) return 0;
  return userText.value.trim().split(/\s+/).length;
});

// Allow submission with any content
const canSubmit = computed(() => {
  return true;
});

onMounted(() => {
  // Focus on text area when component mounts
  nextTick(() => {
    if (textArea.value) {
      textArea.value.focus();
    }
  });
});

async function submitWriting() {
  if (isEvaluating.value) {
    return;
  }

  isEvaluating.value = true;

  try {
    // Use the real AI evaluation
    const evaluation = await evaluateWriting(
      props.writingData.word,
      userText.value,
      props.writingData.context
    );
    
    evaluationResult.value = evaluation;
    isSubmitted.value = true;
    
    // Don't emit answer immediately - wait for user to click Continue
    // The delta score will be calculated when they proceed
  } catch (error) {
    console.error('Error evaluating writing:', error);
    // Show error message to user - no fallback evaluation
    // TODO: Show user-friendly error message
  } finally {
    isEvaluating.value = false;
  }
}

function tryAgain() {
  // Store examples from current evaluation before clearing
  if (evaluationResult.value?.examples) {
    previousExamples.value = [...evaluationResult.value.examples];
  }
  
  isSubmitted.value = false;
  evaluationResult.value = null;
  showExamples.value = false; // Reset examples toggle for feedback section
  // Keep user text for editing
  nextTick(() => {
    if (textArea.value) {
      textArea.value.focus();
    }
  });
}

function proceedToNext() {
  // Clear previous examples and reset hints when moving to next question
  previousExamples.value = [];
  showExamples.value = false;
  showHints.value = false;
  showPreviousExamples.value = false;
  
  if (evaluationResult.value) {
    // Calculate delta score for spaced repetition
    const deltaScore = calculateDeltaScore(evaluationResult.value.overallScore);
    emit('answer', deltaScore);
  } else {
    // Fallback if no evaluation result
    emit('answer', 0);
  }
}

function showCardBack() {
  showCardView.value = true;
}

function hideCardBack() {
  showCardView.value = false;
}

function toggleHints() {
  showHints.value = !showHints.value;
}

function toggleExamples() {
  showExamples.value = !showExamples.value;
}

function togglePreviousExamples() {
  showPreviousExamples.value = !showPreviousExamples.value;
}
</script>

<template>
  <div class="w-full h-full box-border">
    <!-- Show Card View if requested -->
    <div v-if="showCardView && originalCard" class="w-full h-full">
      <ReviewCardBackView :card="originalCard" :show-continue-only="true" @answer="() => hideCardBack()" />
    </div>
    
    <!-- Show Creative Writing View -->
    <div v-else class="w-full h-full box-border">
      <div class="sm:ml-64 pt-14 px-6 sm:px-10 pb-10 flex flex-col min-h-full">
        <!-- Question Container -->
        <div class="w-full flex justify-center pt-10">
          <div class="max-w-3xl w-full">
            <!-- Context and Prompt Section -->
            <div class="text-center mb-8">
              <div class="inline-block px-6 py-4 rounded-2xl border shadow-sm bg-gradient-to-r from-orange-50 to-amber-50 border-orange-200">
                <!-- Question Type Badge -->
                <div class="flex items-center justify-center gap-2 mb-3">
                  <div class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    <icon-edit class="w-3 h-3" />
                    <span>Creative Writing</span>
                  </div>
                </div>
                
                <!-- Context -->
                <div class="mb-4">
                  <p class="text-base text-gray-800 font-medium leading-relaxed mb-3">{{ writingData.context }}</p>
                  <p class="text-sm text-gray-700 leading-relaxed">{{ writingData.prompt }}</p>
                </div>
              </div>
            </div>

            <!-- Hints Section -->
            <div v-if="writingData.hints && writingData.hints.length > 0" class="flex justify-center mb-4">
              <div class="max-w-2xl">
                <button 
                  @click="toggleHints"
                  class="flex items-center gap-2 text-amber-800 hover:text-amber-900 transition-colors mb-2"
                >
                  <icon-lightbulb class="w-4 h-4"/>
                  <span class="text-sm font-medium">
                    {{ showHints ? 'Hide' : 'Show' }} Hints
                  </span>
                </button>
                
                <div v-if="showHints" class="bg-gradient-to-r from-amber-50 to-yellow-50 px-4 py-3 rounded-xl border border-amber-200 shadow-sm">
                  <div class="space-y-2">
                    <div v-for="hint in writingData.hints" :key="hint" class="flex items-center gap-2 text-amber-800">
                      <span class="text-sm">{{ hint }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Examples from Previous Feedback Section -->
            <div v-if="previousExamples.length > 0" class="flex justify-center mb-4">
              <div class="max-w-2xl">
                <button 
                  @click="togglePreviousExamples"
                  class="flex items-center gap-2 text-blue-800 hover:text-blue-900 transition-colors mb-2"
                >
                  <icon-list class="w-4 h-4"/>
                  <span class="text-sm font-medium">
                    {{ showPreviousExamples ? 'Hide' : 'Show' }} Examples from Previous Feedback
                  </span>
                </button>
                
                <div v-if="showPreviousExamples" class="space-y-3">
                  <div v-for="(example, index) in previousExamples" :key="example" class="flex items-start gap-3">
                    <div class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-200 text-blue-800 text-xs font-semibold flex-shrink-0 mt-0.5">
                      {{ index + 1 }}
                    </div>
                    <div class="flex-1 bg-blue-100 px-4 py-3 rounded-lg border border-blue-200">
                      <p class="text-sm text-blue-900 leading-relaxed">{{ example }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Spacer for questions without hints or examples -->
            <div v-else class="mb-8"></div>

            <!-- Writing Section -->
            <div class="mb-12 mt-12">
              <div class="flex justify-center">
                <div class="w-full max-w-4xl">
                  <!-- Word/Character Counter -->
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                      <span>{{ wordCount }} words</span>
                      <span>{{ charCount }} characters</span>
                    </div>
                    
                  </div>

                  <textarea
                    ref="textArea"
                    v-model="userText"
                    :disabled="isSubmitted"
                    placeholder="Start writing your response here..."
                    class="w-full h-40 p-3 text-sm rounded-2xl disabled:bg-gray-100 transition-all duration-300 resize-none focus:outline-none focus:ring-blue-500"
                    :class="{
                      // Writing state - thin border like InputSection
                      'border border-gray-300': !isSubmitted,
                      // Submitted states - thick borders and background colors for visual feedback
                      'border-2 border-green-400 bg-gradient-to-r from-green-50 to-emerald-50 text-green-900': isSubmitted && evaluationResult && evaluationResult.overallScore >= 80,
                      'border-2 border-red-400 bg-gradient-to-r from-red-50 to-rose-50 text-red-900': isSubmitted && evaluationResult && evaluationResult.overallScore < 80
                    }"
                  />
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-center items-center space-x-4 mb-8">
              <!-- Show card button -->
              <button
                v-if="originalCard"
                @click="showCardBack"
                class="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg border border-gray-300 transition-colors text-sm"
              >
                <icon-eye class="w-3 h-3"/>
                <span>Show Card</span>
              </button>

              <!-- Submit/Try Again/Next buttons -->
              <button
                v-if="!isSubmitted"
                @click="submitWriting"
                :disabled="isEvaluating"
                class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm font-medium"
              >
                <icon-loader v-if="isEvaluating" class="w-4 h-4 animate-spin"/>
                <icon-check v-else class="w-4 h-4"/>
                <span>{{ isEvaluating ? 'Evaluating...' : 'Submit Answer' }}</span>
              </button>

              <div v-else-if="evaluationResult" class="flex items-center space-x-3">
                <button
                  @click="tryAgain"
                  class="flex items-center space-x-2 px-4 py-2 text-orange-600 border border-orange-300 rounded-lg hover:bg-orange-50 transition-colors text-sm font-medium"
                >
                  <icon-edit class="w-4 h-4"/>
                  <span>Try Again</span>
                </button>

                <button
                  @click="proceedToNext"
                  class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  <icon-arrow-right class="w-4 h-4"/>
                  <span>Continue</span>
                </button>
              </div>
            </div>

            <!-- Feedback Section -->
            <div v-if="isSubmitted && evaluationResult" class="space-y-6 mb-20">
              <!-- Primary Feedback Card -->
              <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                <div class="p-8 text-center">
                  <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" :class="{
                    'bg-green-50': evaluationResult.overallScore >= 80,
                    'bg-orange-50': evaluationResult.overallScore < 80
                  }">
                    <icon-check v-if="evaluationResult.overallScore >= 80" class="w-8 h-8 text-green-500" />
                    <icon-edit v-else class="w-8 h-8 text-orange-500" />
                  </div>
                  
                  <!-- Dynamic feedback based on results -->
                  <div v-if="evaluationResult.overallScore > 80" class="space-y-2">
                    <h2 class="text-lg font-semibold text-green-900">Great work!</h2>
                    <div class="inline-flex items-center gap-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                      <icon-check class="w-4 h-4" />
                      {{ evaluationResult.overallScore }}/100
                    </div>
                  </div>
                  
                  <div v-else class="space-y-2">
                    <h2 class="text-lg font-semibold text-orange-900">Keep trying!</h2>
                    <div class="inline-flex items-center gap-2 bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                      <icon-edit class="w-4 h-4" />
                      {{ evaluationResult.overallScore }}/100
                    </div>
                  </div>
                  
                  <!-- Word usage status with prominent target word display -->
                  <div class="mt-4 pt-4 border-t border-gray-100">
                    <!-- Show target word prominently when not used -->
                    <div v-if="!evaluationResult.wordUsed" class="mb-4">
                      <div class="bg-gradient-to-r from-amber-50 to-orange-50 px-4 py-3 rounded-xl border border-amber-200 shadow-sm">
                        <div class="text-center space-y-2">
                          <div class="text-sm text-amber-700 font-medium">Learning word:</div>
                          <div class="inline-flex items-center gap-2 bg-amber-100 text-amber-800 px-4 py-2 rounded-full text-lg font-bold">
                            {{ writingData.word }}
                          </div>
                          <div class="text-xs text-amber-600">Try incorporating this word into your writing</div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Target word used successfully -->
                    <div v-else class="mb-4">
                      <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-4 py-3 rounded-xl border border-green-200 shadow-sm">
                        <div class="text-center space-y-2">
                          <div class="text-sm text-green-700 font-medium">Learning word:</div>
                          <div class="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-lg font-bold">
                            {{ writingData.word }}
                          </div>
                          <div class="text-xs" :class="evaluationResult.wordUsedCorrectly ? 'text-green-600' : 'text-orange-600'">
                            {{ evaluationResult.wordUsedCorrectly ? 'Used correctly!' : 'Used but could be improved' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Detailed scores section -->
                <div class="px-8 py-6 bg-gradient-to-br from-slate-50 to-gray-50 border-t border-gray-100">
                  <h3 class="text-sm font-semibold text-gray-900 mb-6">Scores</h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Grammar Score -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
                      <div class="flex items-center gap-3">
                        <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                        <span class="text-sm font-medium text-gray-700">Grammar</span>
                      </div>
                      <div class="flex items-center gap-2">
                        <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div class="h-full bg-blue-500 rounded-full transition-all duration-300" 
                               :style="{ width: evaluationResult.criteria.grammar.score + '%' }"></div>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 min-w-[3rem] text-right">{{ evaluationResult.criteria.grammar.score }}/100</span>
                      </div>
                    </div>
                    
                    <!-- Fluency Score -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
                      <div class="flex items-center gap-3">
                        <div class="w-2 h-2 rounded-full bg-emerald-500"></div>
                        <span class="text-sm font-medium text-gray-700">Fluency</span>
                      </div>
                      <div class="flex items-center gap-2">
                        <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div class="h-full bg-emerald-500 rounded-full transition-all duration-300" 
                               :style="{ width: evaluationResult.criteria.fluency.score + '%' }"></div>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 min-w-[3rem] text-right">{{ evaluationResult.criteria.fluency.score }}/100</span>
                      </div>
                    </div>
                    
                    <!-- Context Score -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
                      <div class="flex items-center gap-3">
                        <div class="w-2 h-2 rounded-full bg-purple-500"></div>
                        <span class="text-sm font-medium text-gray-700">Context</span>
                      </div>
                      <div class="flex items-center gap-2">
                        <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div class="h-full bg-purple-500 rounded-full transition-all duration-300" 
                               :style="{ width: evaluationResult.criteria.context.score + '%' }"></div>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 min-w-[3rem] text-right">{{ evaluationResult.criteria.context.score }}/100</span>
                      </div>
                    </div>
                    
                    <!-- Word Usage Score -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
                      <div class="flex items-center gap-3">
                        <div class="w-2 h-2 rounded-full bg-orange-500"></div>
                        <span class="text-sm font-medium text-gray-700">Word Usage</span>
                      </div>
                      <div class="flex items-center gap-2">
                        <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div class="h-full bg-orange-500 rounded-full transition-all duration-300" 
                               :style="{ width: evaluationResult.criteria.wordUsage.score + '%' }"></div>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 min-w-[3rem] text-right">{{ evaluationResult.criteria.wordUsage.score }}/100</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Feedback text -->
                <div class="px-8 py-6 border-t border-gray-100">
                  <h4 class="text-sm font-semibold text-gray-900 mb-3">Feedback</h4>
                  <p class="text-sm text-gray-700 leading-relaxed">{{ evaluationResult.feedback }}</p>
                </div>
                
                <!-- Suggestions -->
                <div v-if="evaluationResult.suggestions.length > 0" class="px-8 py-6 border-t border-gray-100">
                  <h4 class="text-sm font-semibold text-gray-900 mb-3">Suggestions for improvement:</h4>
                  <ul class="space-y-2">
                    <li v-for="suggestion in evaluationResult.suggestions" :key="suggestion" 
                        class="text-sm text-gray-600 flex items-start gap-2">
                      <span class="text-blue-500 mt-1 flex-shrink-0">•</span>
                      <span>{{ suggestion }}</span>
                    </li>
                  </ul>
                </div>
                
                <!-- Examples (expandable) -->
                <div v-if="evaluationResult.examples && evaluationResult.examples.length > 0" class="px-8 py-6 border-t border-gray-100">
                  <button 
                    @click="toggleExamples"
                    class="flex items-center justify-between w-full text-blue-600 hover:text-blue-700 transition-colors group"
                  >
                    <div class="flex items-center gap-3">
                      <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 group-hover:bg-blue-100 transition-colors">
                        <icon-lightbulb class="w-4 h-4"/>
                      </div>
                      <div class="text-left">
                        <div class="text-sm font-semibold text-gray-900">Usage Examples</div>
                        <div class="text-xs text-gray-500">
                          {{ showExamples ? 'Click to hide examples' : 'Click to see examples of correct usage' }}
                        </div>
                      </div>
                    </div>
                    <div class="transform transition-transform duration-200" :class="{ 'rotate-180': showExamples }">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </div>
                  </button>
                  
                  <div v-if="showExamples" class="mt-4 space-y-3">
                    <div v-for="(example, index) in evaluationResult.examples" :key="example" 
                         class="relative">
                      <div class="flex items-start gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm">
                        <div class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-xs font-semibold flex-shrink-0 mt-0.5">
                          {{ index + 1 }}
                        </div>
                        <div class="flex-1">
                          <p class="text-sm text-gray-800 leading-relaxed">{{ example }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<style scoped>
/* Add any component-specific styles here */
</style>