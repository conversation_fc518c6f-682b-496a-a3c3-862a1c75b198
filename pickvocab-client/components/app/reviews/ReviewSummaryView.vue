<script setup lang="ts">
// @ts-ignore
import IconHome from '@tabler/icons-vue/dist/esm/icons/IconHome.mjs';
// @ts-ignore  
import IconRefresh from '@tabler/icons-vue/dist/esm/icons/IconRefresh.mjs';
// @ts-ignore
import IconAlertTriangle from '@tabler/icons-vue/dist/esm/icons/IconAlertTriangle.mjs';

import type { ReviewSessionSummary } from './reviewSummaryTypes';
import { createDisplayData } from '~/utils/reviewAnalytics';
import SessionStats from './SessionStats.vue';
import VocabularyResultItem from './VocabularyResultItem.vue';

interface Props {
  sessionSummary: ReviewSessionSummary;
}

const props = defineProps<Props>();

// Define emits
const emit = defineEmits<{
  continueLearning: [];
  reviewAgain: [];
}>();

// Create display data for the summary
const displayData = computed(() => createDisplayData(props.sessionSummary));

// Handle action buttons
function handleContinueLearning() {
  emit('continueLearning');
}

function handleReviewAgain() {
  emit('reviewAgain');
}
</script>

<template>
  <div class="sm:ml-64 pt-14 px-4 sm:px-6 lg:px-10 pb-10">
    <div class="pt-6 sm:pt-10 max-w-4xl mx-auto">
      <!-- Header Section -->
      <div class="text-center mb-6 sm:mb-8">
        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
          {{ displayData.celebrationMessage }}
        </h1>
        <p class="text-base sm:text-lg text-gray-600">
          Review completed! Here's how you did:
        </p>
      </div>

      <!-- Session Statistics -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6 sm:mb-8">
        <SessionStats :session-summary="sessionSummary" />
      </div>

      <!-- Vocabulary Results -->
      <div v-if="sessionSummary.vocabularyResults.length > 0" class="mb-6 sm:mb-8">
        <h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6">Results</h2>
        <div class="space-y-2 sm:space-y-3">
          <VocabularyResultItem
            v-for="result in sessionSummary.vocabularyResults"
            :key="`${result.card.id}-${result.reviewType}`"
            :result="result"
          />
        </div>
      </div>

      <!-- Incorrect Words Highlight (if any) -->
      <div v-if="displayData.incorrectWords.length > 0" class="bg-orange-50 border border-orange-200 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
        <h3 class="text-base sm:text-lg font-semibold text-orange-800 mb-3 sm:mb-4 flex items-center">
          <IconAlertTriangle class="w-4 h-4 sm:w-5 sm:h-5 mr-2 flex-shrink-0" />
          Words to practice
        </h3>
        <p class="text-sm sm:text-base text-orange-700">
          Consider reviewing these {{ displayData.incorrectWords.length }} 
          {{ displayData.incorrectWords.length === 1 ? 'word' : 'words' }} 
          again to strengthen your understanding.
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
        <button
          @click="handleContinueLearning"
          class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          <IconHome class="w-5 h-5 mr-2" />
          Continue Learning
        </button>
        
        <button
          @click="handleReviewAgain"
          class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          <IconRefresh class="w-5 h-5 mr-2" />
          Review again
        </button>
      </div>

      <!-- Motivational Footer -->
      <div class="text-center mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200">
        <p class="text-sm sm:text-base text-gray-600">
          <span v-if="displayData.performanceLevel === 'excellent'">
            🌟 Excellent work! You're making great progress.
          </span>
          <span v-else-if="displayData.performanceLevel === 'good'">
            👏 Well done! Keep up the good work.
          </span>
          <span v-else>
            💪 Every review makes you stronger. Keep practicing!
          </span>
        </p>
      </div>
    </div>
  </div>
</template>