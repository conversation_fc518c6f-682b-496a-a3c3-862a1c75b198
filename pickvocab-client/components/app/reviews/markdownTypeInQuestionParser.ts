import { marked } from 'marked';
import type { TypeInQuestionItem } from './reviewTypes';
import { getTextFromMarkedTokens, decodeHtmlEntities, type MarkedToken } from '../write/markedUtils';

/**
 * Parse markdown response from LLM into structured type-in question objects
 * Error-tolerant parser that handles case insensitive headings, redundant whitespace, etc.
 */
export function parseMarkdownTypeInQuestions(markdown: string): TypeInQuestionItem[] {
  // Split markdown into sections by level 2 headings (## word)
  const sections = markdown.split(/(?=^## )/m).filter(s => s.trim());
  const questions: TypeInQuestionItem[] = [];
  
  console.log(`Type-in Parser: Found ${sections.length} sections to parse`);
  
  for (const section of sections) {
    try {
      const question = parseSingleQuestion(section.trim());
      if (question) {
        questions.push(question);
      }
    } catch (error) {
      console.warn('Type-in Parser: Failed to parse section:', error);
      console.warn('Section content:', section.substring(0, 200));
    }
  }
  
  console.log(`Type-in Parser: Successfully parsed ${questions.length} questions`);
  return questions;
}

function parseSingleQuestion(sectionMarkdown: string): TypeInQuestionItem | null {
  // Extract word and card ID from the ## heading
  const wordMatch = sectionMarkdown.match(/^##\s*(.*?)\s*\(.*?(\d+).*?\)\s*$/m);
  if (!wordMatch || !wordMatch[1] || !wordMatch[2]) {
    console.warn('Type-in Parser: Could not extract word and card ID from section');
    return null;
  }
  
  const word = wordMatch[1].trim();
  const cardId = wordMatch[2].trim();
  
  // Determine question type and extract question text
  let questionType: 'type-in' | 'fill-blank' | 'synonym' | 'antonym' | null = null;
  let questionText = '';
  
  const typeInMatch = sectionMarkdown.match(/###\s*Type-in Answer\s*\n(.*?)(?=###|$)/s);
  const fillBlankMatch = sectionMarkdown.match(/###\s*Fill-in-the-Blank\s*\n(.*?)(?=###|$)/s);
  const synonymMatch = sectionMarkdown.match(/###\s*Synonym\s*\n(.*?)(?=###|$)/s);
  const antonymMatch = sectionMarkdown.match(/###\s*Antonym\s*\n(.*?)(?=###|$)/s);
  
  if (typeInMatch && typeInMatch[1]) {
    questionType = 'type-in';
    questionText = typeInMatch[1].trim();
  } else if (fillBlankMatch && fillBlankMatch[1]) {
    questionType = 'fill-blank';
    questionText = fillBlankMatch[1].trim();
  } else if (synonymMatch && synonymMatch[1]) {
    questionType = 'synonym';
    questionText = synonymMatch[1].trim();
  } else if (antonymMatch && antonymMatch[1]) {
    questionType = 'antonym';
    questionText = antonymMatch[1].trim();
  }
  
  // Extract hint (optional, mainly for fill-blank questions)
  const hintMatch = sectionMarkdown.match(/###\s*Hint\s*\n(.*?)(?=###|$)/s);
  const hint = hintMatch && hintMatch[1] ? hintMatch[1].trim() : '';
  
  // Extract target answer
  const targetMatch = sectionMarkdown.match(/###\s*Target Answer\s*\n(.*?)(?=###|$)/s);
  const targetAnswer = targetMatch && targetMatch[1] ? targetMatch[1].trim() : '';
  
  // Extract explanation
  const explanationMatch = sectionMarkdown.match(/###\s*Explanation\s*\n(.*?)(?=###|---|$)/s);
  const explanation = explanationMatch && explanationMatch[1] ? explanationMatch[1].trim() : '';
  
  // Validate that we have all required fields
  if (!questionType || !questionText || !targetAnswer || !explanation) {
    console.warn('Type-in Parser: Missing required fields for question:', {
      word,
      questionType,
      hasQuestionText: !!questionText,
      hasTargetAnswer: !!targetAnswer,
      hasExplanation: !!explanation
    });
    return null;
  }
  
  return {
    id: generateQuestionId(),
    llmSourceCardId: cardId,
    word: word,
    questionType: questionType,
    questionText: questionText,
    hint: hint || undefined, // Only include hint if it exists and is not empty
    targetAnswer: targetAnswer,
    explanation: explanation,
    originalCardId: cardId // Map llmSourceCardId to originalCardId for consistency
  };
}

function isValidTypeInQuestion(question: Partial<TypeInQuestionItem>): boolean {
  return !!(
    question.word && 
    question.questionText && 
    question.targetAnswer && 
    question.explanation &&
    (question.questionType === 'type-in' || question.questionType === 'fill-blank' || question.questionType === 'synonym' || question.questionType === 'antonym')
  );
}

function generateQuestionId(): string {
  return `ti_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}