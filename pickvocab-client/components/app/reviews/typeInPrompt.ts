import type { LLMCardInput } from './reviewTypes';

/**
 * Constructs the unified LLM prompt for generating type-in questions with multiple formats.
 */
export function constructTypeInPrompt(formattedCards: LLMCardInput[]): string {
  return `<instructions>
You are an expert English vocabulary tutor. Generate engaging type-in questions for vocabulary review based on provided flashcard data.
### Core Requirements:
1. Generate 1 question per card
2. Randomly select between four question formats:
   - Type-in Answer: Provide definition/clue, user types the word
   - Fill-in-the-Blank: Provide sentence with _____, user types the missing word
   - Synonym: Ask for the target word using a synonym in natural question format
   - Antonym: Ask for the target word using an antonym in natural question format
3. Follow the exact output format below
4. For Fill-in-the-Blank questions, ALWAYS provide helpful hints or context clues
5. For Synonym and Antonym questions, use natural, conversational phrasing
6. IMPORTANT: Include the card ID from the input in the heading format as shown below.

### Fill-in-the-Blank Guidelines:
- Always include a hint after the sentence to help users identify the target word
- Hints can be: definitions, synonyms, word categories, or contextual descriptions
- Make sentences contextually rich so the blank word fits naturally
- Ensure the sentence provides enough context to narrow down the answer

### Synonym Guidelines:
- Use natural question format: "X is a synonym for which word that describes Y?"
- Choose simpler/more common synonyms when possible
- Provide context about the target word's meaning or usage
- Always include a helpful hint

### Antonym Guidelines:
- Use natural question format: "What word means the opposite of X and describes Y?"
- Choose clear, obvious antonyms
- Provide context about the target word's meaning or usage
- Always include a helpful hint explaining the contrast
</instructions>
<output_format>
## [word] (id: [CARD_ID_FROM_INPUT])
### [question_type]
[question_content]
### Hint
[helpful hint or context clue - only for Fill-in-the-Blank questions, leave empty for Type-in Answer]
### Target Answer
[target_word]
### Explanation
[brief explanation of the word's meaning and usage]
</output_format>
<example-output>
## enormous (id: 123)
### Type-in Answer
Very large in size, quantity, or extent; immense
### Hint

### Target Answer
enormous
### Explanation
"Enormous" means exceptionally large or huge. It's used to describe things that are much bigger than normal or expected.

## meticulous (id: 456)
### Fill-in-the-Blank
The scientist was _____ in recording every detail of the experiment.
### Hint
showing great attention to detail; very careful and precise
### Target Answer
meticulous
### Explanation
"Meticulous" means showing great attention to detail; very careful and precise. In this context, it describes someone who is thorough and careful in their work.

## ubiquitous (id: 789)
### Synonym
"Everywhere" is a synonym for which word that describes something present in all places at once?
### Hint
present or found everywhere; existing in all places at once
### Target Answer
ubiquitous
### Explanation
"Ubiquitous" means existing or being everywhere at the same time; constantly encountered. The synonym "everywhere" captures the essence but "ubiquitous" is more formal and precise.

## ephemeral (id: 101)
### Antonym
What word means the opposite of "permanent" and describes something that lasts only a very short time?
### Hint
lasting for a very short time; transitory (opposite of permanent)
### Target Answer
ephemeral
### Explanation
"Ephemeral" means lasting for a very short time; transient. It's the opposite of "permanent" - while permanent things last forever, ephemeral things disappear quickly.

## surreptitious (id: 112)
### Synonym
"Sneaky" is similar to which word that describes actions done secretly to avoid detection?
### Hint
done secretly or stealthily; hidden from others
### Target Answer
surreptitious
### Explanation
"Surreptitious" means done secretly or stealthily to avoid detection. The word "sneaky" captures the basic idea, but "surreptitious" implies more deliberate stealth and concealment.
</example-output>
<input>
Cards:
${JSON.stringify(formattedCards, null, 2)}
</input>`;
}