<script setup lang="ts">
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
// @ts-ignore
import IconEye from '@tabler/icons-vue/dist/esm/icons/IconEye.mjs';
// @ts-ignore
import IconSparkles from '@tabler/icons-vue/dist/esm/icons/IconSparkles.mjs';
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
// @ts-ignore
import IconInfoCircle from '@tabler/icons-vue/dist/esm/icons/IconInfoCircle.mjs';
// @ts-ignore
import IconAlertTriangle from '@tabler/icons-vue/dist/esm/icons/IconAlertTriangle.mjs';
// @ts-ignore
import IconLoader from '@tabler/icons-vue/dist/esm/icons/IconLoader.mjs';
// @ts-ignore
import IconArrowsLeftRight from '@tabler/icons-vue/dist/esm/icons/IconArrowsLeftRight.mjs';
// @ts-ignore
import IconArrowsUpDown from '@tabler/icons-vue/dist/esm/icons/IconArrowsUpDown.mjs';
import type { TypeInQuestionItem } from './reviewTypes';
import { fuzzyMatch } from './fuzzyMatcher';
import { validateAnswerWithAI } from './aiValidator';
import type { Card } from '~/utils/card';
import ReviewCardBackView from './ReviewCardBackView.vue';

interface Props {
  questionData: TypeInQuestionItem;
  originalCard?: Card;
}

interface Emits {
  (e: 'answer', deltaScore: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const userAnswer = ref('');
const isSubmitted = ref(false);
const isCorrect = ref(false);
const matchType = ref<'exact' | 'fuzzy' | 'ai-validated' | 'incorrect'>('incorrect');
const showAskAI = ref(false);
const isValidatingWithAI = ref(false);
const aiValidationResult = ref<string>('');
const showCardView = ref(false);
const answerInput = ref<HTMLInputElement | null>(null);

function resetState() {
  userAnswer.value = '';
  isSubmitted.value = false;
  isCorrect.value = false;
  matchType.value = 'incorrect';
  showAskAI.value = false;
  isValidatingWithAI.value = false;
  aiValidationResult.value = '';
  showCardView.value = false;
}

watch(() => props.questionData, () => {
  resetState();
  nextTick(() => {
    answerInput.value?.focus();
  });
}, { deep: true });

const feedback = computed(() => {
  if (!isSubmitted.value) return '';
  
  switch (matchType.value) {
    case 'exact':
      return 'Correct! 🎉';
    case 'fuzzy':
      return 'Correct! (Minor typo corrected) ✅';
    case 'ai-validated':
      return 'Correct! (Alternative answer validated by AI) 🤖';
    case 'incorrect':
      return 'Incorrect ❌';
    default:
      return '';
  }
});

const deltaScore = computed(() => {
  switch (matchType.value) {
    case 'exact': return 1;
    case 'fuzzy': return 1;
    case 'ai-validated': return 1;
    case 'incorrect': return -1;
    default: return -1;
  }
});


function submitAnswer() {
  if (!userAnswer.value.trim()) return;
  
  const trimmedAnswer = userAnswer.value.trim().toLowerCase();
  const targetAnswer = props.questionData.targetAnswer.toLowerCase();
  
  // Check exact match
  if (trimmedAnswer === targetAnswer) {
    isCorrect.value = true;
    matchType.value = 'exact';
    isSubmitted.value = true;
    return;
  }
  
  // Check fuzzy match
  const fuzzyResult = fuzzyMatch(targetAnswer, trimmedAnswer);
  if (fuzzyResult.isMatch) {
    isCorrect.value = true;
    matchType.value = 'fuzzy';
    isSubmitted.value = true;
    return;
  }
  
  // No match found
  isCorrect.value = false;
  matchType.value = 'incorrect';
  isSubmitted.value = true;
  showAskAI.value = true;
}

function showCard() {
  showCardView.value = true;
}

function proceedToNext() {
  // Emit score delta based on correctness
  emit('answer', deltaScore.value);
}

async function askAI() {
  if (isValidatingWithAI.value) return;
  
  isValidatingWithAI.value = true;
  try {
    const result = await validateAnswerWithAI(
      props.questionData.questionText,
      props.questionData.targetAnswer,
      userAnswer.value.trim()
    );
    
    aiValidationResult.value = result.explanation;
    
    if (result.isValid) {
      isCorrect.value = true;
      matchType.value = 'ai-validated';
    }
  } catch (error) {
    console.error('AI validation error:', error);
    aiValidationResult.value = 'AI validation failed. Please try again.';
  } finally {
    isValidatingWithAI.value = false;
  }
}

onMounted(() => {
  answerInput.value?.focus();
});
</script>

<template>
  <div class="w-full h-full box-border">
    <!-- Show Card View if requested -->
    <div v-if="showCardView && originalCard" class="w-full h-full">
      <ReviewCardBackView :card="originalCard" :show-continue-only="true" @answer="proceedToNext" />
    </div>
    
    <!-- Show Type-in Question View -->
    <div v-else class="w-full h-full box-border">
      <div class="sm:ml-64 pt-14 px-6 sm:px-10 flex flex-col h-full">
        <!-- Question Container -->
        <div class="w-full flex justify-center items-center flex-grow pt-10">
          <div class="max-w-3xl w-full">
            <!-- Question Text -->
            <div class="text-center mb-8">
              <div class="inline-block px-6 py-4 rounded-2xl border shadow-sm" :class="{
                'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-100': questionData.questionType === 'type-in',
                'bg-gradient-to-r from-purple-50 to-violet-50 border-purple-100': questionData.questionType === 'fill-blank',
                'bg-gradient-to-r from-green-50 to-emerald-50 border-green-100': questionData.questionType === 'synonym',
                'bg-gradient-to-r from-orange-50 to-red-50 border-orange-100': questionData.questionType === 'antonym'
              }">
                <!-- Question Type Badge -->
                <div class="flex items-center justify-center gap-2 mb-3">
                  <div class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="{
                    'bg-blue-100 text-blue-800': questionData.questionType === 'type-in',
                    'bg-purple-100 text-purple-800': questionData.questionType === 'fill-blank',
                    'bg-green-100 text-green-800': questionData.questionType === 'synonym',
                    'bg-orange-100 text-orange-800': questionData.questionType === 'antonym'
                  }">
                    <IconInfoCircle v-if="questionData.questionType === 'type-in'" class="w-3 h-3" />
                    <IconSettings v-else-if="questionData.questionType === 'fill-blank'" class="w-3 h-3" />
                    <IconArrowsLeftRight v-else-if="questionData.questionType === 'synonym'" class="w-3 h-3" />
                    <IconArrowsUpDown v-else-if="questionData.questionType === 'antonym'" class="w-3 h-3" />
                    <span v-if="questionData.questionType === 'type-in'">Definition</span>
                    <span v-else-if="questionData.questionType === 'fill-blank'">Fill-in-the-Blank</span>
                    <span v-else-if="questionData.questionType === 'synonym'">Synonym</span>
                    <span v-else-if="questionData.questionType === 'antonym'">Antonym</span>
                  </div>
                </div>
                <!-- Question Text -->
                <p class="text-lg text-gray-800 font-medium leading-relaxed">{{ questionData.questionText }}</p>
              </div>
            </div>

            <!-- Hint Section (for fill-blank, synonym, and antonym questions) -->
            <div v-if="questionData.hint && (questionData.questionType === 'fill-blank' || questionData.questionType === 'synonym' || questionData.questionType === 'antonym')" class="flex justify-center mb-16">
              <div class="max-w-2xl bg-gradient-to-r from-amber-50 to-yellow-50 px-4 py-3 rounded-xl border border-amber-200 shadow-sm">
                <div class="flex items-center gap-2 text-amber-800">
                  <IconInfoCircle class="w-4 h-4 flex-shrink-0" />
                  <div class="flex-1">
                    <span class="text-sm font-medium">Hint:</span>
                    <span class="text-sm ml-1">{{ questionData.hint }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Spacer for type-in questions without hints -->
            <div v-else class="mb-8"></div>

            <!-- Input Section -->
            <div class="mb-12">
              <div class="flex justify-center">
                <div class="w-full max-w-md">
                  <input 
                    v-model="userAnswer"
                    @keyup.enter="submitAnswer"
                    :disabled="isSubmitted"
                    type="text"
                    class="w-full px-4 py-3 text-lg text-center border-2 border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 transition-all duration-300"
                    :class="{ 
                      'border-green-400 bg-gradient-to-r from-green-50 to-emerald-50 text-green-900': isSubmitted && isCorrect,
                      'border-red-400 bg-gradient-to-r from-red-50 to-rose-50 text-red-900': isSubmitted && !isCorrect
                    }"
                    placeholder="Type your answer here..."
                    autocomplete="off"
                    ref="answerInput"
                  />
                </div>
              </div>
            </div>

            <!-- Feedback Section -->
            <div v-if="isSubmitted" class="space-y-6 mb-12">
              <!-- Primary Feedback Card -->
              <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                <div class="p-8 text-center">
                  <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" :class="{
                    'bg-green-50': isCorrect,
                    'bg-red-50': !isCorrect
                  }">
                    <IconCheck v-if="isCorrect" class="w-8 h-8 text-green-500" />
                    <IconX v-else class="w-8 h-8 text-red-500" />
                  </div>
                  <!-- Dynamic feedback based on match type -->
                  <div v-if="matchType === 'exact'" class="space-y-2">
                    <h2 class="text-xl font-semibold text-green-900">Perfect!</h2>
                  </div>
                  
                  <div v-else-if="matchType === 'fuzzy'" class="space-y-2">
                    <h2 class="text-xl font-semibold text-green-900">Correct!</h2>
                    <div class="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      <IconSettings class="w-4 h-4" />
                      Minor typo fixed
                    </div>
                  </div>
                  
                  <div v-else-if="matchType === 'ai-validated'" class="space-y-2">
                    <h2 class="text-xl font-semibold text-green-900">Great thinking!</h2>
                    <div class="inline-flex items-center gap-2 bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                      <IconSparkles class="w-4 h-4" />
                      Alternative accepted
                    </div>
                  </div>
                  
                  <div v-else class="space-y-2">
                    <h2 class="text-xl font-semibold text-red-900">Not quite right</h2>
                  </div>
                  
                  <!-- Show correct answer when incorrect -->
                  <div v-if="!isCorrect" class="space-y-3 mt-4">
                    <div class="inline-flex items-center gap-3 bg-gray-50 px-4 py-3 rounded-xl">
                      <span class="text-sm text-gray-600">Correct answer:</span>
                      <span class="font-semibold text-gray-900">{{ questionData.targetAnswer }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Explanation Card -->
              <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                <div class="flex items-start gap-4">
                  <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <IconInfoCircle class="w-4 h-4 text-blue-600" />
                  </div>
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Explanation</h3>
                    <p class="text-gray-700 leading-relaxed">{{ questionData.explanation }}</p>
                  </div>
                </div>
              </div>

              <!-- AI Validation Card (when needed) -->
              <div v-if="showAskAI" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                <div v-if="!aiValidationResult" class="text-center">
                  <div class="w-12 h-12 bg-amber-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconAlertTriangle class="w-6 h-6 text-amber-600" />
                  </div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">Think you got it right?</h3>
                  <p class="text-gray-600 mb-6 max-w-md mx-auto">
                    Your answer might be right in a different way. Let's find out!
                  </p>
                  <button 
                    @click="askAI"
                    :disabled="isValidatingWithAI"
                    class="inline-flex items-center gap-1.5 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-all duration-200 disabled:opacity-50"
                  >
                    <IconLoader v-if="isValidatingWithAI" class="w-3.5 h-3.5 animate-spin" />
                    <IconSparkles v-else class="w-3.5 h-3.5" />
                    {{ isValidatingWithAI ? 'Checking...' : 'Check with AI →' }}
                  </button>
                </div>

                <!-- AI Result -->
                <div v-if="aiValidationResult" class="flex items-start gap-4">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" :class="{
                    'bg-green-50': matchType === 'ai-validated',
                    'bg-red-50': matchType !== 'ai-validated'
                  }">
                    <IconCheck v-if="matchType === 'ai-validated'" class="w-4 h-4 text-green-600" />
                    <IconX v-else class="w-4 h-4 text-red-600" />
                  </div>
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">AI Review</h3>
                    <p class="text-gray-700 leading-relaxed">{{ aiValidationResult }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="sticky w-full bottom-4 flex justify-center">
          <div class="flex text-sm text-gray-700 py-2 px-2 rounded-md border border-gray-100 shadow-md bg-white">
            <!-- Submit Answer Button -->
            <button 
              v-if="!isSubmitted"
              @click="submitAnswer"
              :disabled="!userAnswer.trim()"
              class="px-2 py-1 border rounded-md border-gray-300 flex items-center hover:bg-gray-100 transition-all duration-200"
              :class="userAnswer.trim() 
                ? 'text-gray-700' 
                : 'text-gray-400 cursor-not-allowed'"
            >
              <IconArrowRight class="inline-block w-5 h-5 me-2" />
              <span>Submit Answer</span>
            </button>

            <!-- View Card Button (only button shown after feedback) -->
            <button 
              v-if="isSubmitted && originalCard"
              @click="showCard"
              class="px-2 py-1 border rounded-md border-gray-300 flex items-center hover:bg-gray-100 transition-all duration-200"
            >
              <IconEye class="inline-block w-5 h-5 me-2" />
              <span>View Card</span>
            </button>
            
            <!-- Continue Button (shown after feedback when no card to show) -->
            <button 
              v-if="isSubmitted && !originalCard"
              @click="proceedToNext"
              class="px-2 py-1 border rounded-md border-gray-300 flex items-center hover:bg-gray-100 transition-all duration-200"
            >
              <IconArrowRight class="inline-block w-5 h-5 me-2" />
              <span>Continue</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>