import type { Review } from '~/utils/review';
import type { LocalSessionReviewItem, MCQItem, TypeInQuestionItem, CreativeWritingItem } from '~/components/app/reviews/reviewTypes';
import type { Card } from '~/utils/card';
import type { 
  ReviewSessionSummary, 
  VocabularyResult, 
  ReviewSummaryDisplayData,
  MasteryLevel 
} from '~/components/app/reviews/reviewSummaryTypes';
import { MASTERY_LEVELS } from '~/components/app/reviews/reviewSummaryTypes';

export function calculateSessionSummary(
  review: Review,
  localSessionItems?: LocalSessionReviewItem[]
): ReviewSessionSummary {
  const vocabularyResults: VocabularyResult[] = [];
  const reviewModes: ('basic' | 'mcq' | 'type-in' | 'creative-writing')[] = [];
  
  // Process local session items if available (mixed AI + basic reviews)
  if (localSessionItems && localSessionItems.length > 0) {
    for (const item of localSessionItems) {
      reviewModes.push(item.type);
      
      if (item.type === 'basic') {
        const card = item.data as Card;
        const reviewCard = review.cards.find(rc => String(rc.card.id) === String(card.id));
        if (reviewCard && reviewCard.deltaScore !== undefined) {
          vocabularyResults.push(createVocabularyResult(card, reviewCard.deltaScore, 'basic'));
        }
      } else if (item.type === 'mcq') {
        const mcqData = item.data as MCQItem;
        if (mcqData.originalCardId) {
          const reviewCard = review.cards.find(rc => String(rc.card.id) === String(mcqData.originalCardId));
          if (reviewCard && reviewCard.deltaScore !== undefined) {
            vocabularyResults.push(createVocabularyResult(
              reviewCard.card, 
              reviewCard.deltaScore, 
              'mcq',
              mcqData.questionText
            ));
          }
        }
      } else if (item.type === 'type-in') {
        const typeInData = item.data as TypeInQuestionItem;
        if (typeInData.originalCardId) {
          const reviewCard = review.cards.find(rc => String(rc.card.id) === String(typeInData.originalCardId));
          if (reviewCard && reviewCard.deltaScore !== undefined) {
            vocabularyResults.push(createVocabularyResult(
              reviewCard.card, 
              reviewCard.deltaScore, 
              'type-in',
              typeInData.questionText
            ));
          }
        }
      } else if (item.type === 'creative-writing') {
        const creativeWritingData = item.data as CreativeWritingItem;
        if (creativeWritingData.originalCardId) {
          const reviewCard = review.cards.find(rc => String(rc.card.id) === String(creativeWritingData.originalCardId));
          if (reviewCard && reviewCard.deltaScore !== undefined) {
            vocabularyResults.push(createVocabularyResult(
              reviewCard.card, 
              reviewCard.deltaScore, 
              'creative-writing',
              creativeWritingData.prompt
            ));
          }
        }
      }
    }
  } else {
    // Regular review flow (backend cards only)
    reviewModes.push('basic');
    for (const reviewCard of review.cards) {
      if (reviewCard.deltaScore !== undefined) {
        vocabularyResults.push(createVocabularyResult(reviewCard.card, reviewCard.deltaScore, 'basic'));
      }
    }
  }

  const correctAnswers = vocabularyResults.filter(result => result.wasCorrect).length;
  const incorrectAnswers = vocabularyResults.filter(result => !result.wasCorrect).length;
  const totalCards = vocabularyResults.length;
  const accuracyPercentage = totalCards > 0 ? Math.round((correctAnswers / totalCards) * 100) : 0;

  return {
    totalCards,
    correctAnswers,
    incorrectAnswers,
    accuracyPercentage,
    vocabularyResults,
    reviewModes: [...new Set(reviewModes)], // Remove duplicates
    sessionDate: new Date(),
    sessionId: review.id.toString()
  };
}

function createVocabularyResult(
  card: Card, 
  deltaScore: number, 
  reviewType: 'basic' | 'mcq' | 'type-in' | 'creative-writing',
  questionText?: string
): VocabularyResult {
  // Get the original progress_score from card before the review
  let originalScore = card.progress_score;
  
  if (originalScore === undefined || originalScore === null) {
    // If no progress_score available, assume starting at 3 (Familiar)
    originalScore = 3;
  }
  
  // Calculate new score after applying delta (+1 for correct, -1 for incorrect)
  const newScore = Math.max(0, Math.min(5, originalScore + deltaScore));
  
  return {
    card,
    wasCorrect: deltaScore > 0,
    deltaScore,
    previousMasteryLevel: getMasteryLevel(originalScore),
    newMasteryLevel: getMasteryLevel(newScore),
    reviewType,
    questionText
  };
}

export function determineMasteryLevelChange(
  card: Card,
  deltaScore: number
): { previous: string; new: string } {
  // Get the original progress_score from card before the review
  let originalScore = card.progress_score;
  
  if (originalScore === undefined || originalScore === null) {
    // If no progress_score available, assume starting at 3 (Familiar)
    originalScore = 3;
  }
  
  // Calculate new score after applying delta (+1 for correct, -1 for incorrect)
  const newScore = Math.max(0, Math.min(5, originalScore + deltaScore));
  
  return {
    previous: getMasteryLevel(originalScore),
    new: getMasteryLevel(newScore)
  };
}

function getMasteryLevel(score: number): MasteryLevel {
  const clampedScore = Math.max(0, Math.min(5, Math.round(score))) as keyof typeof MASTERY_LEVELS;
  return MASTERY_LEVELS[clampedScore];
}

export function createDisplayData(
  summary: ReviewSessionSummary
): ReviewSummaryDisplayData {
  const { accuracyPercentage, vocabularyResults } = summary;
  
  let celebrationMessage: string;
  let performanceLevel: 'excellent' | 'good' | 'needs-practice';
  
  if (accuracyPercentage >= 90) {
    celebrationMessage = accuracyPercentage === 100 ? "Perfect! 🎉" : "Excellent work! 🌟";
    performanceLevel = 'excellent';
  } else if (accuracyPercentage >= 70) {
    celebrationMessage = "Great job! 👏";
    performanceLevel = 'good';
  } else {
    celebrationMessage = "Keep practicing! 📚";
    performanceLevel = 'needs-practice';
  }
  
  const incorrectWords = vocabularyResults.filter(result => !result.wasCorrect);
  
  return {
    celebrationMessage,
    performanceLevel,
    incorrectWords
  };
}