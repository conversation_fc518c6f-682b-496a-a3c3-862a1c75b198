import { VueQueryPlugin, QueryClient } from '@tanstack/vue-query'

export default defineNuxtPlugin((nuxtApp) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: Infinity, // Never automatically stale
        gcTime: 1000 * 60 * 30, // 30 minutes - data kept in cache (renamed from cacheTime in v5)
        refetchOnWindowFocus: false,
        retry: 3,
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
    },
  })

  nuxtApp.vueApp.use(VueQueryPlugin, { queryClient })
})