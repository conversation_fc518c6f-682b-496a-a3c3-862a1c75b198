import { toCard, type ApiGenericCard } from "../genericCard";

interface ApiReviewCard {
  card: ApiGenericCard;
  delta_score: number;
}

interface ApiReview {
  id: number;
  cards: ApiReviewCard[];
  decks: DeckId[];
  is_master: boolean;
}

interface ApiBaseReview {
  decks: DeckId[];
  is_master: boolean;
}

function toReviewCard(apiReviewCard: ApiReviewCard): ReviewCard {
  return {
    card: toCard(apiReviewCard.card),
    deltaScore: apiReviewCard.delta_score,
  }
}

function toReview(apiReview: ApiReview): Review {
  return {
    id: apiReview.id,
    cards: apiReview.cards.map(toReviewCard),
    deckIds: apiReview.decks,
    isMaster: apiReview.is_master,
  }
}

function toApiBaseReview(review: BaseReview): ApiBaseReview {
  return {
    decks: review.deckIds,
    is_master: review.isMaster,
  }
}

export class RemoteReviewApi {
  async list(page?: number): Promise<Review[]> {
    const axios = getAxiosInstance();
    const response = await axios.get('/reviews2/', { params: { page } });
    return response.data.results.map(toReview);
  }
  async get(id: ReviewId): Promise<Review | undefined> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/reviews2/${id}/`);
    return toReview(response.data);
  }
  async create(review: BaseReview): Promise<Review> {
    const axios = getAxiosInstance();
    const response = await axios.post('/reviews2/', toApiBaseReview(review));
    return toReview(response.data);
  }
  async updateScore(review: Review): Promise<Review> {
    const axios = getAxiosInstance();
    const payload = {
      review_cards: review.cards.map(card => ({
        card_id: card.card.id,
        delta_score: card.deltaScore
      }))
    };
    const response = await axios.put(`/reviews2/${review.id}/update_score/`, payload);
    return toReview(response.data);
  }
  async delete(id: ReviewId): Promise<void> {
    const axios = getAxiosInstance();
    await axios.delete(`/reviews2/${id}/`);
  }
}