export async function authenticate<PERSON><PERSON>gle<PERSON>ser(code: string): Promise<{ user: User, key: string }> {
  const axios = getAxiosInstance();
  const response = await axios.post(`/dj-rest-auth/google/`, {
    code
  });

  return { user: response.data.user, key: response.data.key };
}

export async function registerAnnonymousUser(): Promise<{ user: User, key: string }> {
  const axios = getAxiosInstance();
  try {
    const response = await axios.post('/accounts/annonymous-registration/', {}, {
      headers: {
        Authorization: undefined
      }
    });
    return { user: response.data.user, key: response.data.key };
  } catch (error) {
    throw new Error('Failed to create anonymous user');
  }
}

export async function getUser(token: string): Promise<User | undefined> {
  const axios = getAxiosInstance();
  try {
    const response = await axios.get('/dj-rest-auth/user/', {
      headers: {
        Authorization: `Token ${token}`
      }
    });
    if (response.status === 200) {
      return response.data;
    } else {
      return undefined;
    }
  } catch (error) {
    return undefined;
  }
}