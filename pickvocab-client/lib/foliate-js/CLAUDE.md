# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

foliate-js is a JavaScript library for rendering e-books in the browser. It supports multiple formats (EPUB, MOBI, KF8/AZW3, FB2, CBZ, PDF) and uses native ES modules with no build step required for basic usage.

## Development Commands

- `npm run build` - Build vendor dependencies using Rollup (copies PDF.js files and bundles fflate/zip.js)
- Open `tests/tests.html` in a browser to run tests
- Open `reader.html` in a browser to test the demo reader (requires local server for file access)

## Architecture

The codebase follows a modular architecture with three main categories:

### Book Format Parsers
These implement the "book" interface for different e-book formats:
- `epub.js` + `epubcfi.js` - EPUB format with CFI support
- `mobi.js` - Mobipocket and KF8 (AZW3) files
- `fb2.js` - FictionBook 2 format
- `comic-book.js` - Comic book archives (CBZ)
- `pdf.js` - PDF support (experimental, requires PDF.js)

### Renderers
These implement the "renderer" interface for displaying content:
- `paginator.js` - Reflowable books using CSS multi-column
- `fixed-layout.js` - Fixed layout books (comics, PDFs)

### Core Components
- `view.js` - Main entry point, high-level renderer that coordinates everything
- `reader.js` - Complete reader implementation with UI (demo/reference)
- `overlayer.js` - SVG overlay system for annotations and highlights
- `progress.js` - Reading progress tracking
- `search.js` - Full-text search across book content
- `text-walker.js` - DOM utility for text manipulation
- `tts.js` - Text-to-speech SSML generation

### Auxiliary Modules
- `dict.js` - StarDict/dictd dictionary support
- `opds.js` - OPDS catalog client
- `quote-image.js` - Generate shareable quote images
- `footnotes.js` - Footnote handling
- `uri-template.js` - URI template expansion

## Key Interfaces

### Book Interface
All book parsers must implement:
- `.sections` - Array of book sections with `.load()`, `.createDocument()`, `.size` properties
- `.metadata` - Book metadata (title, author, etc.)
- `.toc` - Table of contents structure
- `.resolveHref(href)` - Convert href to section index + anchor
- `.resolveCFI(cfi)` - Convert CFI to section index + anchor

### Renderer Interface  
Both paginator and fixed-layout renderers provide:
- `.open(book)` - Open a book object
- `.goTo({index, anchor})` - Navigate to location
- `.prev()` / `.next()` - Page navigation
- Events: `load`, `relocate`, `create-overlayer`

## File Structure

- Root files are the core modules (no subdirectories for main functionality)
- `ui/` - UI components (menu.js, tree.js) used by reader.js
- `tests/` - Test files (mainly CFI tests)
- `vendor/` - Built dependencies (created by build process)
- `rollup/` - Rollup input files for building vendor deps

## Development Notes

- Uses ES modules exclusively - import/export syntax throughout
- No TypeScript - pure JavaScript with JSDoc for some functions
- Custom elements (web components) for renderers: `<foliate-view>`, `<foliate-paginator>`
- CSS custom properties and `::part()` selectors for styling
- Relies on modern browser APIs (WebCrypto, Intl.Segmenter, CSS multi-column)
- ZIP file handling requires external zip.js library for random access
- MOBI/KF8 decompression requires fflate for zlib support
- PDF support requires PDF.js integration

## Testing

Tests are browser-based - serve the directory and open `tests/tests.html`. Main focus is on EPUB CFI parsing and generation.

## Security Considerations

EPUB files can contain JavaScript which is blocked by design. The library expects Content Security Policy (CSP) to prevent script execution from e-book content. Never render untrusted e-books without proper CSP headers.