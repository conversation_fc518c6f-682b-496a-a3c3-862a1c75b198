export class SelectionPopup {
    #popup = null
    #isVisible = false
    #onLookup = null
    #currentSelectedText = null

    constructor(onLookup) {
        this.#onLookup = onLookup
        this.#createPopup()
        this.#bindEvents()
    }

    #createPopup() {
        this.#popup = document.createElement('div')
        this.#popup.className = 'selection-popup popover'
        this.#popup.style.cssText = `
            position: absolute;
            visibility: hidden;
            z-index: 1000;
            padding: 6px;
            display: flex;
            gap: 6px;
        `

        const lookupButton = document.createElement('button')
        lookupButton.textContent = 'Lookup'
        lookupButton.className = 'lookup-button'
        lookupButton.style.cssText = `
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            background: #007acc;
            color: white;
            cursor: pointer;
            font-size: 14px;
        `
        lookupButton.addEventListener('click', this.#handleLookup.bind(this))

        this.#popup.appendChild(lookupButton)
        document.body.appendChild(this.#popup)
    }

    #bindEvents() {
        // Hide popup when clicking elsewhere
        document.addEventListener('click', (e) => {
            if (!this.#popup.contains(e.target)) {
                this.hide()
            }
        })

        // Hide popup on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hide()
            }
        })
    }

    #handleLookup() {
        if (this.#currentSelectedText && this.#onLookup) {
            this.#onLookup(this.#currentSelectedText)
        }
        this.hide()
    }

    #getCurrentSelection() {
        const selection = window.getSelection()
        if (!selection || selection.rangeCount === 0) return null
        
        const text = selection.toString().trim()
        if (!text) return null

        const range = selection.getRangeAt(0)
        return { text, range, selection }
    }

    #positionPopup(rect) {
        const popupRect = this.#popup.getBoundingClientRect()
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight
        
        // Calculate position above the selection
        let left = rect.left + (rect.width / 2) - (popupRect.width / 2)
        let top = rect.top - popupRect.height - 8 // 8px gap above selection

        // Adjust horizontal position to stay within viewport
        if (left < 8) {
            left = 8
        } else if (left + popupRect.width > viewportWidth - 8) {
            left = viewportWidth - popupRect.width - 8
        }

        // If not enough space above, position below the selection
        if (top < 8) {
            top = rect.bottom + 8
        }

        // Ensure popup doesn't go below viewport
        if (top + popupRect.height > viewportHeight - 8) {
            top = viewportHeight - popupRect.height - 8
        }

        this.#popup.style.left = `${left}px`
        this.#popup.style.top = `${top}px`
    }

    show(selectionRect, selectedText) {
        if (!selectionRect) {
            const selection = this.#getCurrentSelection()
            if (!selection) return
            selectionRect = selection.range.getBoundingClientRect()
            selectedText = selection.text
        }

        // Store the selected text
        this.#currentSelectedText = selectedText

        this.#popup.style.visibility = 'visible'
        this.#isVisible = true
        
        // Position after making visible so we can get correct dimensions
        requestAnimationFrame(() => {
            this.#positionPopup(selectionRect)
        })
    }

    hide() {
        this.#popup.style.visibility = 'hidden'
        this.#isVisible = false
    }

    get isVisible() {
        return this.#isVisible
    }

    destroy() {
        if (this.#popup && this.#popup.parentNode) {
            this.#popup.parentNode.removeChild(this.#popup)
        }
    }
}