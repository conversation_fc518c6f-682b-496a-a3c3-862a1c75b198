import '../view.js'
import { createTOCView } from '../ui/tree.js'
import { createMenu } from '../ui/menu.js'
import { Overlayer } from '../overlayer.js'
import { SelectionPopup } from './selection-popup.js'

const getCSS = ({ spacing, justify, hyphenate }) => `
    @namespace epub "http://www.idpf.org/2007/ops";
    html {
        color-scheme: light dark;
    }
    /* https://github.com/whatwg/html/issues/5426 */
    @media (prefers-color-scheme: dark) {
        a:link {
            color: lightblue;
        }
    }
    p, li, blockquote, dd {
        line-height: ${spacing};
        text-align: ${justify ? 'justify' : 'start'};
        -webkit-hyphens: ${hyphenate ? 'auto' : 'manual'};
        hyphens: ${hyphenate ? 'auto' : 'manual'};
        -webkit-hyphenate-limit-before: 3;
        -webkit-hyphenate-limit-after: 2;
        -webkit-hyphenate-limit-lines: 2;
        hanging-punctuation: allow-end last;
        widows: 2;
    }
    /* prevent the above from overriding the align attribute */
    [align="left"] { text-align: left; }
    [align="right"] { text-align: right; }
    [align="center"] { text-align: center; }
    [align="justify"] { text-align: justify; }

    pre {
        white-space: pre-wrap !important;
    }
    aside[epub|type~="endnote"],
    aside[epub|type~="footnote"],
    aside[epub|type~="note"],
    aside[epub|type~="rearnote"] {
        display: none;
    }
`

const $ = document.querySelector.bind(document)

const locales = 'en'
const percentFormat = new Intl.NumberFormat(locales, { style: 'percent' })
const listFormat = new Intl.ListFormat(locales, { style: 'short', type: 'conjunction' })

const formatLanguageMap = x => {
    if (!x) return ''
    if (typeof x === 'string') return x
    const keys = Object.keys(x)
    return x[keys[0]]
}

const formatOneContributor = contributor => typeof contributor === 'string'
    ? contributor : formatLanguageMap(contributor?.name)

const formatContributor = contributor => Array.isArray(contributor)
    ? listFormat.format(contributor.map(formatOneContributor))
    : formatOneContributor(contributor)

class Reader {
    #tocView
    #selectionPopup
    style = {
        spacing: 1.4,
        justify: true,
        hyphenate: true,
    }
    annotations = new Map()
    annotationsByValue = new Map()
    closeSideBar() {
        $('#dimming-overlay').classList.remove('show')
        $('#side-bar').classList.remove('show')
    }
    #handleLookup(selectedText) {
        // Log selection info when lookup is clicked
        console.log('📖 Text Lookup:', {
            text: selectedText,
            length: selectedText.length,
            wordCount: selectedText.split(/\s+/).length,
        })
        
        // Simple lookup demo - in a real app, this would call a dictionary API
        const result = `Lookup for: "${selectedText}"\n\nThis is a demo lookup result. In a real application, this would show dictionary definitions, translations, or other lookup results.`
        alert(result)
    }
    constructor() {
        $('#side-bar-button').addEventListener('click', () => {
            $('#dimming-overlay').classList.add('show')
            $('#side-bar').classList.add('show')
        })
        $('#dimming-overlay').addEventListener('click', () => this.closeSideBar())
        
        // Initialize selection popup
        this.#selectionPopup = new SelectionPopup((selectedText) => {
            this.#handleLookup(selectedText)
        })

        this.menu = createMenu([
            {
                name: 'layout',
                label: 'Layout',
                type: 'radio',
                items: [
                    ['Paginated', 'paginated'],
                    ['Scrolled', 'scrolled'],
                ],
                onclick: value => {
                    this.view?.renderer.setAttribute('flow', value)
                },
            },
            {
                name: 'zoom',
                label: 'Zoom',
                type: 'radio',
                items: [
                    ['Fit Width', 'fit-width'],
                    ['Fit Page', 'fit-page'],
                    ['50%', '0.5'],
                    ['75%', '0.75'],
                    ['100%', '1'],
                    ['125%', '1.25'],
                    ['150%', '1.5'],
                    ['200%', '2'],
                ],
                onclick: value => {
                    this.view?.renderer.setAttribute('zoom', value)
                },
            },
        ])
        this.menu.element.classList.add('menu')

        $('#menu-button').append(this.menu.element)
        $('#menu-button > button').addEventListener('click', () =>
            this.menu.element.classList.toggle('show'))
        this.menu.groups.layout.select('paginated')
        this.menu.groups.zoom.select('1')
    }
    async open(file) {
        this.view = document.createElement('foliate-view')
        document.body.append(this.view)
        
        
        await this.view.open(file)
        
        // Force single column/page layout
        if (!this.view.isFixedLayout && this.view.renderer) {
            // For reflowable content: force single column
            this.view.renderer.setAttribute('max-column-count', '1')
        } else if (this.view.isFixedLayout && this.view.renderer) {
            // For fixed-layout content: force single page
            this.view.renderer.spread = 'none'
            if (this.view.book.rendition) {
                this.view.book.rendition.spread = 'none'
            }
            this.view.renderer.open(this.view.book)
        }
        
        this.view.addEventListener('load', this.#onLoad.bind(this))
        this.view.addEventListener('relocate', this.#onRelocate.bind(this))

        const { book } = this.view
        book.transformTarget?.addEventListener('data', ({ detail }) => {
            detail.data = Promise.resolve(detail.data).catch(e => {
                console.error(new Error(`Failed to load ${detail.name}`, { cause: e }))
                return ''
            })
        })
        this.view.renderer.setStyles?.(getCSS(this.style))
        
        // Show/hide menu options based on layout type
        if (this.view.isFixedLayout) {
            // Fixed-layout: show zoom, hide layout options
            this.menu.groups.zoom.element.style.display = 'block'
            this.menu.groups.layout.element.style.display = 'none'
        } else {
            // Reflowable: hide zoom, show layout options
            this.menu.groups.zoom.element.style.display = 'none'
            this.menu.groups.layout.element.style.display = 'block'
        }
        
        this.view.renderer.next()

        $('#header-bar').style.visibility = 'visible'
        $('#nav-bar').style.visibility = 'visible'
        $('#left-button').addEventListener('click', () => this.view.goLeft())
        $('#right-button').addEventListener('click', () => this.view.goRight())

        const slider = $('#progress-slider')
        slider.dir = book.dir
        slider.addEventListener('input', e =>
            this.view.goToFraction(parseFloat(e.target.value)))
        for (const fraction of this.view.getSectionFractions()) {
            const option = document.createElement('option')
            option.value = fraction
            $('#tick-marks').append(option)
        }

        document.addEventListener('keydown', this.#handleKeydown.bind(this))

        const title = formatLanguageMap(book.metadata?.title) || 'Untitled Book'
        document.title = title
        $('#side-bar-title').innerText = title
        $('#side-bar-author').innerText = formatContributor(book.metadata?.author)
        Promise.resolve(book.getCover?.())?.then(blob =>
            blob ? $('#side-bar-cover').src = URL.createObjectURL(blob) : null)

        const toc = book.toc
        if (toc) {
            this.#tocView = createTOCView(toc, href => {
                this.view.goTo(href).catch(e => console.error(e))
                this.closeSideBar()
            })
            $('#toc-view').append(this.#tocView.element)
        }

        // load and show highlights embedded in the file by Calibre
        const bookmarks = await book.getCalibreBookmarks?.()
        if (bookmarks) {
            const { fromCalibreHighlight } = await import('./epubcfi.js')
            for (const obj of bookmarks) {
                if (obj.type === 'highlight') {
                    const value = fromCalibreHighlight(obj)
                    const color = obj.style.which
                    const note = obj.notes
                    const annotation = { value, color, note }
                    const list = this.annotations.get(obj.spine_index)
                    if (list) list.push(annotation)
                    else this.annotations.set(obj.spine_index, [annotation])
                    this.annotationsByValue.set(value, annotation)
                }
            }
            this.view.addEventListener('create-overlay', e => {
                const { index } = e.detail
                const list = this.annotations.get(index)
                if (list) for (const annotation of list)
                    this.view.addAnnotation(annotation)
            })
            this.view.addEventListener('draw-annotation', e => {
                const { draw, annotation } = e.detail
                const { color } = annotation
                draw(Overlayer.highlight, { color })
            })
            this.view.addEventListener('show-annotation', e => {
                const annotation = this.annotationsByValue.get(e.detail.value)
                if (annotation.note) alert(annotation.note)
            })
        }
    }
    #handleKeydown(event) {
        const k = event.key
        if (k === 'ArrowLeft' || k === 'h') this.view.goLeft()
        else if(k === 'ArrowRight' || k === 'l') this.view.goRight()
    }
    #handleTextSelection(doc) {
        const selection = doc.getSelection()
        if (!selection || selection.rangeCount === 0) {
            this.#selectionPopup.hide()
            return
        }
        
        const selectedText = selection.toString().trim()
        if (selectedText.length === 0) {
            this.#selectionPopup.hide()
            return
        }
        
        // Get the bounding rect of the selection
        const range = selection.getRangeAt(0)
        const rect = range.getBoundingClientRect()
        
        // Convert iframe coordinates to window coordinates
        const iframe = doc.defaultView.frameElement
        let adjustedRect = rect
        
        if (iframe) {
            const iframeRect = iframe.getBoundingClientRect()
            adjustedRect = {
                left: rect.left + iframeRect.left,
                top: rect.top + iframeRect.top,
                right: rect.right + iframeRect.left,
                bottom: rect.bottom + iframeRect.top,
                width: rect.width,
                height: rect.height
            }
        }
        
        // Show the popup above the selection
        this.#selectionPopup.show(adjustedRect, selectedText)
    }
    #onLoad({ detail: { doc } }) {
        doc.addEventListener('keydown', this.#handleKeydown.bind(this))
        
        // Handle text selection to show lookup popup
        doc.addEventListener('selectionchange', () => {
            // Use a small delay to ensure selection is finalized
            setTimeout(() => this.#handleTextSelection(doc), 10)
        })
        
        // Also handle mouseup to catch drag selections
        doc.addEventListener('mouseup', () => {
            setTimeout(() => this.#handleTextSelection(doc), 10)
        })
    }
    #onRelocate({ detail }) {
        const { fraction, location, tocItem, pageItem } = detail
        const percent = percentFormat.format(fraction)
        const loc = pageItem
            ? `Page ${pageItem.label}`
            : `Loc ${location.current}`
        const slider = $('#progress-slider')
        slider.style.visibility = 'visible'
        slider.value = fraction
        slider.title = `${percent} · ${loc}`
        if (tocItem?.href) this.#tocView?.setCurrentHref?.(tocItem.href)
    }
}

const open = async file => {
    document.body.removeChild($('#drop-target'))
    const reader = new Reader()
    globalThis.reader = reader
    await reader.open(file)
}

const dragOverHandler = e => e.preventDefault()
const dropHandler = e => {
    e.preventDefault()
    const item = Array.from(e.dataTransfer.items)
        .find(item => item.kind === 'file')
    if (item) {
        const entry = item.webkitGetAsEntry()
        open(entry.isFile ? item.getAsFile() : entry).catch(e => console.error(e))
    }
}
const dropTarget = $('#drop-target')
dropTarget.addEventListener('drop', dropHandler)
dropTarget.addEventListener('dragover', dragOverHandler)

$('#file-input').addEventListener('change', e =>
    open(e.target.files[0]).catch(e => console.error(e)))
$('#file-button').addEventListener('click', () => $('#file-input').click())

const params = new URLSearchParams(location.search)
const url = params.get('url')
if (url) open(url).catch(e => console.error(e))
else dropTarget.style.visibility = 'visible'
