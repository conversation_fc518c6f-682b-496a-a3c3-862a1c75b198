# Text Selection Logging Example

This is a modified version of the foliate-js reader that logs text selections to the browser console.

## How to Use

1. Start a local server in the project root directory:
   ```bash
   # From the foliate-js root directory
   python -m http.server 8000
   # or
   npx serve .
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:8000/examples/reader.html
   ```

3. Load an e-book (EPUB, PDF, etc.) by:
   - Dragging and dropping the file onto the page
   - Or clicking "choose a file" to select one

4. Once the book is loaded, select any text by:
   - Click and drag to select text with your mouse
   - Use keyboard shortcuts to select text

5. Open the browser's Developer Console (F12) to see the logged selections

## What Gets Logged

When you select text, the console will show:
- 📖 **Text Selection:** with details including:
  - `text`: The actual selected text
  - `length`: Number of characters selected
  - `wordCount`: Number of words in the selection
  - `element`: HTML element type containing the selection
  - `className`: CSS class of the containing element
  - `rangeCount`: Number of selection ranges

## Features

- Works with all supported e-book formats
- Logs selections in both paginated and scrolled view modes
- Provides rich context information about the selection
- Non-intrusive - doesn't interfere with normal reading experience