import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { WriteHistoryApi, type WritingRevisionHistory } from '~/api/writeHistory'
import { useAuthStore } from '~/stores/auth'

export function useWriteHistoryQuery(page: Ref<number>) {
  const authStore = useAuthStore()
  const historyApi = new WriteHistoryApi()

  const queryKey = computed(() => [
    'write_history', 
    { 
      page: page.value, 
      owner: authStore.currentUser?.id 
    }
  ])

  return useQuery({
    queryKey,
    queryFn: async () => {
      const params = { page: page.value, owner: authStore.currentUser?.id }
      return await historyApi.list(params)
    },
    staleTime: Infinity, // Never automatically stale - manual control
    enabled: computed(() => authStore.currentUser?.id !== undefined)
  })
}

export function useWriteHistoryEntryQuery(historyId: Ref<number>) {
  const historyApi = new WriteHistoryApi()

  return useQuery({
    queryKey: ['write_history_entry', historyId.value],
    queryFn: async () => await historyApi.get(historyId.value),
    staleTime: Infinity,
    enabled: computed(() => historyId.value !== undefined)
  })
}

export function useInvalidateWriteHistoryQuery() {
  const queryClient = useQueryClient()
  
  return {
    invalidateAllWriteHistory: () => queryClient.invalidateQueries({ queryKey: ['write_history'] }),
    invalidateWriteHistoryPage: (page: number) => {
      const authStore = useAuthStore()
      const key = ['write_history', { page, owner: authStore.currentUser?.id }]
      return queryClient.invalidateQueries({ queryKey: key })
    },
    invalidateWriteHistoryEntry: (historyId: number) => {
      return queryClient.invalidateQueries({ queryKey: ['write_history_entry', historyId] })
    },
    refetchAllWriteHistory: () => queryClient.refetchQueries({ queryKey: ['write_history'] }),
    refetchWriteHistoryPage: (page: number) => {
      const authStore = useAuthStore()
      const key = ['write_history', { page, owner: authStore.currentUser?.id }]
      return queryClient.refetchQueries({ queryKey: key })
    }
  }
}