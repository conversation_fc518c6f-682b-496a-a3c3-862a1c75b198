import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'
import { useInvalidateDecksQuery } from '~/composables/useDecksQuery'
import { useInvalidateDeckQuery } from '~/composables/useDeckQuery'

export function useDeleteDeck() {
  const store = useAppStore()
  const queryClient = useQueryClient()
  const { invalidateAllDecks } = useInvalidateDecksQuery()

  return useMutation({
    mutationFn: async (deckId: DeckId) => {
      return await store.deleteDeck(deckId)
    },
    onMutate: async (deckId: DeckId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['deck', deckId] })
      await queryClient.cancelQueries({ queryKey: ['all_decks'] })

      // Snapshot the previous values
      const previousDeck = queryClient.getQueryData(['deck', deckId])
      const previousListQueries = queryClient.getQueriesData({ queryKey: ['all_decks'] })

      // Optimistically remove from all deck list caches
      previousListQueries.forEach(([queryKey, queryData]) => {
        if (queryData && Array.isArray(queryData)) {
          const updatedDecks = queryData.filter((deck: Deck) => deck.id !== deckId)
          queryClient.setQueryData(queryKey, updatedDecks)
        }
      })

      // Remove individual deck cache
      queryClient.removeQueries({ queryKey: ['deck', deckId] })

      return { previousDeck, previousListQueries }
    },
    onError: (error, deckId, context) => {
      // Rollback optimistic updates
      if (context?.previousDeck) {
        queryClient.setQueryData(['deck', deckId], context.previousDeck)
      }

      // Rollback list caches
      if (context?.previousListQueries) {
        context.previousListQueries.forEach(([queryKey, queryData]) => {
          queryClient.setQueryData(queryKey, queryData)
        })
      }

      console.error('Failed to delete deck:', error)
    },
    onSuccess: () => {
      // Invalidate to ensure fresh data
      invalidateAllDecks()
    }
  })
}

export function useRemoveCardFromDeck() {
  const store = useAppStore()
  const queryClient = useQueryClient()
  const { invalidateDeck } = useInvalidateDeckQuery()

  return useMutation({
    mutationFn: async ({ deckId, cardId }: { deckId: DeckId, cardId: CardId }) => {
      return await store.removeCardFromDeck(deckId, cardId)
    },
    onMutate: async ({ deckId, cardId }) => {
      // Cancel any outgoing refetches for this deck
      await queryClient.cancelQueries({ queryKey: ['deck', deckId] })

      // Snapshot the previous deck queries
      const previousDeckQueries = queryClient.getQueriesData({ queryKey: ['deck', deckId] })

      // Optimistically remove card from all deck caches
      previousDeckQueries.forEach(([queryKey, queryData]) => {
        if (queryData && typeof queryData === 'object' && 'cards' in queryData) {
          const deckData = queryData as Deck
          const updatedCards = deckData.cards.filter(card => card.id !== cardId)
          queryClient.setQueryData(queryKey, {
            ...deckData,
            cards: updatedCards,
            totalCards: Math.max(0, (deckData.totalCards || deckData.cards.length) - 1)
          })
        }
      })

      return { previousDeckQueries }
    },
    onError: (error, _, context) => {
      // Rollback optimistic updates
      if (context?.previousDeckQueries) {
        context.previousDeckQueries.forEach(([queryKey, queryData]) => {
          queryClient.setQueryData(queryKey, queryData)
        })
      }

      console.error('Failed to remove card from deck:', error)
    },
    onSuccess: (_, { deckId }) => {
      // Invalidate all deck pages for this deck
      const numericDeckId = typeof deckId === 'string' ? parseInt(deckId) : deckId
      queryClient.invalidateQueries({ 
        queryKey: ['deck', numericDeckId],
        exact: false // This will match ['deck', deckId, page] patterns
      })
    }
  })
}

export function useAddCardToDeck() {
  const store = useAppStore()
  const queryClient = useQueryClient()
  const { invalidateDeck } = useInvalidateDeckQuery()

  return useMutation({
    mutationFn: async ({ deckId, cardId }: { deckId: DeckId, cardId: CardId }) => {
      return await store.addCardToDeck(deckId, cardId)
    },
    onMutate: async ({ deckId, cardId }) => {
      // Cancel any outgoing refetches for this deck
      await queryClient.cancelQueries({ queryKey: ['deck', deckId] })

      // Get the card data to add optimistically
      let cardData = queryClient.getQueryData(['card', cardId]) as Card | undefined
      
      // If card data is not cached, fetch it for optimistic update
      if (!cardData) {
        try {
          cardData = await store.getGenericCard(cardId)
          // Cache the fetched card data for future use
          if (cardData) {
            queryClient.setQueryData(['card', cardId], cardData)
          }
        } catch (error) {
          console.warn('Failed to fetch card data for optimistic update:', error)
          // Continue without optimistic update
        }
      }
      
      // Snapshot the previous deck queries
      const previousDeckQueries = queryClient.getQueriesData({ queryKey: ['deck', deckId] })

      // Optimistically add card to deck caches (if we have card data)
      if (cardData) {
        previousDeckQueries.forEach(([queryKey, queryData]) => {
          if (queryData && typeof queryData === 'object' && 'cards' in queryData) {
            const deckData = queryData as Deck
            // Check if card is already in deck to avoid duplicates
            const cardExists = deckData.cards.some(card => card.id === cardId)
            if (!cardExists) {
              // Add to beginning since cards are sorted by created_at (newest first)
              const updatedCards = [cardData, ...deckData.cards]
              queryClient.setQueryData(queryKey, {
                ...deckData,
                cards: updatedCards,
                totalCards: (deckData.totalCards || deckData.cards.length) + 1
              })
            }
          }
        })
      }

      return { previousDeckQueries, cardData }
    },
    onError: (error, _, context) => {
      // Rollback optimistic updates
      if (context?.previousDeckQueries) {
        context.previousDeckQueries.forEach(([queryKey, queryData]) => {
          queryClient.setQueryData(queryKey, queryData)
        })
      }

      console.error('Failed to add card to deck:', error)
    },
    onSuccess: (_, { deckId }) => {
      // Invalidate all deck pages for this deck immediately
      // Use partial matching to invalidate all pages: ['deck', deckId, *]
      const numericDeckId = typeof deckId === 'string' ? parseInt(deckId) : deckId
      queryClient.invalidateQueries({ 
        queryKey: ['deck', numericDeckId],
        exact: false // This will match ['deck', deckId, page] patterns
      })
    }
  })
}

export function useCreateDeck() {
  const store = useAppStore()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (baseDeck: BaseDeck) => {
      return await store.createDeck(baseDeck)
    },
    onSuccess: (createdDeck) => {
      // Invalidate all deck list caches (both demo and user decks)
      queryClient.invalidateQueries({ queryKey: ['all_decks'] })
      
      // Optionally cache the created deck for immediate access
      queryClient.setQueryData(['deck', createdDeck.id, 1], createdDeck)
    },
    onError: (error) => {
      console.error('Failed to create deck:', error)
    }
  })
}