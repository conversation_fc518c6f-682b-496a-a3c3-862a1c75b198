import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'
import { useInvalidateCardsQuery } from '~/composables/useCardsQuery'
import { useInvalidateCardQuery } from '~/composables/useCardQuery'

export function useDeleteCard() {
  const store = useAppStore()
  const queryClient = useQueryClient()
  const { invalidateAllCards } = useInvalidateCardsQuery()
  const { removeCard } = useInvalidateCardQuery()

  return useMutation({
    mutationFn: async (cardId: CardId) => {
      return await store.deleteGenericCard(cardId)
    },
    onMutate: async (cardId: CardId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['card', cardId] })
      await queryClient.cancelQueries({ queryKey: ['all_cards'] })
      // Also cancel deck queries that might contain this card
      await queryClient.cancelQueries({ queryKey: ['deck'] })

      // Snapshot the previous values
      const previousCard = queryClient.getQueryData(['card', cardId])
      const previousListQueries = queryClient.getQueriesData({ queryKey: ['all_cards'] })
      const previousDeckQueries = queryClient.getQueriesData({ queryKey: ['deck'] })

      // Optimistically update: remove the card from cache
      removeCard(typeof cardId === 'string' ? parseInt(cardId) : cardId)

      // Optimistically remove from all list caches
      previousListQueries.forEach(([queryKey, queryData]) => {
        if (queryData && typeof queryData === 'object' && 'result' in queryData) {
          const listData = queryData as { result: Card[], totalPages: number, count: number }
          const updatedResult = listData.result.filter(card => card.id !== cardId)
          queryClient.setQueryData(queryKey, {
            ...listData,
            result: updatedResult,
            count: Math.max(0, listData.count - 1)
          })
        }
      })

      // Optimistically remove from all deck caches that contain this card
      previousDeckQueries.forEach(([queryKey, queryData]) => {
        if (queryData && typeof queryData === 'object' && 'cards' in queryData) {
          const deckData = queryData as Deck
          const cardExists = deckData.cards.some(card => card.id === cardId)
          if (cardExists) {
            const updatedCards = deckData.cards.filter(card => card.id !== cardId)
            queryClient.setQueryData(queryKey, {
              ...deckData,
              cards: updatedCards,
              totalCards: Math.max(0, (deckData.totalCards || deckData.cards.length) - 1)
            })
          }
        }
      })

      return { previousCard, previousListQueries, previousDeckQueries }
    },
    onError: (error, cardId, context) => {
      // Rollback optimistic updates
      if (context?.previousCard) {
        queryClient.setQueryData(['card', cardId], context.previousCard)
      }

      // Rollback list caches
      if (context?.previousListQueries) {
        context.previousListQueries.forEach(([queryKey, queryData]) => {
          queryClient.setQueryData(queryKey, queryData)
        })
      }

      // Rollback deck caches
      if (context?.previousDeckQueries) {
        context.previousDeckQueries.forEach(([queryKey, queryData]) => {
          queryClient.setQueryData(queryKey, queryData)
        })
      }

      console.error('Failed to delete card:', error)
    },
    onSuccess: () => {
      // Invalidate to ensure fresh data
      invalidateAllCards()
      
      // Also invalidate all deck caches since the card could have been in any deck
      queryClient.invalidateQueries({ queryKey: ['deck'] })
    }
  })
}

export function useCreateDefinitionCard() {
  const store = useAppStore()
  const queryClient = useQueryClient()
  const { invalidateAllCards } = useInvalidateCardsQuery()

  return useMutation({
    mutationFn: async (baseCard: BaseDefinitionCard) => {
      return await store.createDefinitionCard(baseCard)
    },
    onSuccess: (newCard: DefinitionCard) => {
      // Immediately add new card to individual card cache
      queryClient.setQueryData(['card', newCard.id], newCard)
      
      // Add new card to the beginning of all list caches
      const listQueries = queryClient.getQueriesData({ queryKey: ['all_cards'] })
      listQueries.forEach(([queryKey, queryData]) => {
        if (queryData && typeof queryData === 'object' && 'result' in queryData) {
          const listData = queryData as { result: Card[], totalPages: number, count: number }
          const updatedResult = [newCard, ...listData.result]
          queryClient.setQueryData(queryKey, {
            ...listData,
            result: updatedResult,
            count: listData.count + 1
          })
        }
      })
      
      // Still invalidate to ensure data consistency across all pages
      invalidateAllCards()
    }
  })
}

export function useCreateContextCard() {
  const store = useAppStore()
  const queryClient = useQueryClient()
  const { invalidateAllCards } = useInvalidateCardsQuery()

  return useMutation({
    mutationFn: async (baseCard: BaseContextCard) => {
      return await store.createContextCard(baseCard)
    },
    onSuccess: (newCard: ContextCard) => {
      // Immediately add new card to individual card cache
      queryClient.setQueryData(['card', newCard.id], newCard)
      
      // Add new card to the beginning of all list caches
      const listQueries = queryClient.getQueriesData({ queryKey: ['all_cards'] })
      listQueries.forEach(([queryKey, queryData]) => {
        if (queryData && typeof queryData === 'object' && 'result' in queryData) {
          const listData = queryData as { result: Card[], totalPages: number, count: number }
          const updatedResult = [newCard, ...listData.result]
          queryClient.setQueryData(queryKey, {
            ...listData,
            result: updatedResult,
            count: listData.count + 1
          })
        }
      })
      
      // Still invalidate to ensure data consistency across all pages
      invalidateAllCards()
    }
  })
}

export function useUpdateDefinitionCard() {
  const store = useAppStore()
  const queryClient = useQueryClient()
  const { invalidateCard } = useInvalidateCardQuery()

  return useMutation({
    mutationFn: async (updatedCard: DefinitionCard) => {
      return await store.updateDefinitionCard(updatedCard)
    },
    onMutate: async (updatedCard: DefinitionCard) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['card', updatedCard.id] })

      // Snapshot the previous card
      const previousCard = queryClient.getQueryData(['card', updatedCard.id]) as Card | undefined

      // Optimistically update the card cache
      const optimisticCard = {
        ...updatedCard,
        updatedAt: new Date().toISOString()
      }
      queryClient.setQueryData(['card', updatedCard.id], optimisticCard)

      // Also update any list caches that contain this card
      const listQueries = queryClient.getQueriesData({ queryKey: ['all_cards'] })
      listQueries.forEach(([queryKey, queryData]) => {
        if (queryData && typeof queryData === 'object' && 'result' in queryData) {
          const listData = queryData as { result: Card[], totalPages: number, count: number }
          const updatedResult = listData.result.map(card => 
            card.id === updatedCard.id ? optimisticCard : card
          )
          queryClient.setQueryData(queryKey, {
            ...listData,
            result: updatedResult
          })
        }
      })

      return { previousCard }
    },
    onError: (error, updatedCard, context) => {
      // Rollback optimistic updates
      if (context?.previousCard) {
        queryClient.setQueryData(['card', updatedCard.id], context.previousCard)
      }

      // Invalidate to restore correct state
      invalidateCard(typeof updatedCard.id === 'string' ? parseInt(updatedCard.id) : updatedCard.id)
      
      console.error('Failed to update card:', error)
    },
    onSuccess: (serverUpdatedCard, clientUpdatedCard) => {
      // Update cache with server response
      if (serverUpdatedCard) {
        queryClient.setQueryData(['card', clientUpdatedCard.id], serverUpdatedCard)
        
        // Update list caches with fresh data
        const listQueries = queryClient.getQueriesData({ queryKey: ['all_cards'] })
        listQueries.forEach(([queryKey, queryData]) => {
          if (queryData && typeof queryData === 'object' && 'result' in queryData) {
            const listData = queryData as { result: Card[], totalPages: number, count: number }
            const updatedResult = listData.result.map(card => 
              card.id === clientUpdatedCard.id ? serverUpdatedCard : card
            )
            queryClient.setQueryData(queryKey, {
              ...listData,
              result: updatedResult
            })
          }
        })
      }
    }
  })
}