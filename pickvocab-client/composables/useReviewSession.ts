import type { LocalSessionReviewItem } from '~/components/app/reviews/reviewTypes';

// Global state for local session review items
const localSessionReviewItems = ref<LocalSessionReviewItem[]>([]);

export const useReviewSession = () => {
  const setLocalSessionReviewItems = (items: LocalSessionReviewItem[]) => {
    localSessionReviewItems.value = items;
  };

  const getLocalSessionReviewItems = () => {
    return localSessionReviewItems.value;
  };

  const clearLocalSessionReviewItems = () => {
    localSessionReviewItems.value = [];
  };

  const hasLocalSessionReviewItems = computed(() => {
    return localSessionReviewItems.value.length > 0;
  });

  return {
    localSessionReviewItems: readonly(localSessionReviewItems),
    setLocalSessionReviewItems,
    getLocalSessionReviewItems,
    clearLocalSessionReviewItems,
    hasLocalSessionReviewItems
  };
}; 