import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'

export function useCardQuery(cardId: Ref<number | undefined>, options?: {
  enabled?: ComputedRef<boolean>
}) {
  const store = useAppStore()

  const queryKey = computed(() => ['card', cardId.value])

  return useQuery({
    queryKey,
    queryFn: async () => {
      if (!cardId.value) throw new Error('Card ID is required')
      return await store.getGenericCard(cardId.value)
    },
    staleTime: Infinity, // Never automatically stale - we control refetching manually
    enabled: computed(() => {
      const baseEnabled = cardId.value !== undefined
      return options?.enabled ? baseEnabled && options.enabled.value : baseEnabled
    })
  })
}

export function useCardEmbeddingQuery(cardId: Ref<number | undefined>, options?: {
  enabled?: ComputedRef<boolean>
}) {
  const store = useAppStore()

  const queryKey = computed(() => ['card_embedding', cardId.value])

  return useQuery({
    queryKey,
    queryFn: async () => {
      if (!cardId.value) throw new Error('Card ID is required')
      return await store.checkCardEmbeddingStatus(cardId.value)
    },
    staleTime: 1000 * 60 * 5, // 5 minutes stale time for embedding status
    enabled: computed(() => {
      const baseEnabled = cardId.value !== undefined
      return options?.enabled ? baseEnabled && options.enabled.value : baseEnabled
    })
  })
}

export function useInvalidateCardQuery() {
  const queryClient = useQueryClient()
  
  return {
    invalidateCard: (cardId: number) => {
      return queryClient.invalidateQueries({ queryKey: ['card', cardId] })
    },
    invalidateCardEmbedding: (cardId: number) => {
      return queryClient.invalidateQueries({ queryKey: ['card_embedding', cardId] })
    },
    invalidateAllCards: () => {
      return queryClient.invalidateQueries({ queryKey: ['card'] })
    },
    refetchCard: (cardId: number) => {
      return queryClient.refetchQueries({ queryKey: ['card', cardId] })
    },
    removeCard: (cardId: number) => {
      queryClient.removeQueries({ queryKey: ['card', cardId] })
      queryClient.removeQueries({ queryKey: ['card_embedding', cardId] })
    },
    setCardData: (cardId: number, data: Card) => {
      queryClient.setQueryData(['card', cardId], data)
    }
  }
}

export function usePrefetchCard() {
  const queryClient = useQueryClient()
  const store = useAppStore()

  return {
    prefetchCard: async (cardId: number) => {
      await queryClient.prefetchQuery({
        queryKey: ['card', cardId],
        queryFn: async () => await store.getGenericCard(cardId),
        staleTime: Infinity
      })
    },
    prefetchCardEmbedding: async (cardId: number) => {
      await queryClient.prefetchQuery({
        queryKey: ['card_embedding', cardId],
        queryFn: async () => await store.checkCardEmbeddingStatus(cardId),
        staleTime: 1000 * 60 * 5
      })
    }
  }
}