import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { WriteHistoryApi, type WritingRevisionHistory } from '~/api/writeHistory'
import { useAuthStore } from '~/stores/auth'

export function useCreateWriteHistory() {
  const historyApi = new WriteHistoryApi()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: { history: Partial<WritingRevisionHistory>, cardIds?: string[] }) => {
      return await historyApi.create(data.history, data.cardIds)
    },
    onSuccess: (createdHistory) => {
      // Update individual cache
      queryClient.setQueryData(['write_history_entry', createdHistory.id], createdHistory)
      
      // Invalidate list caches to include new entry
      queryClient.invalidateQueries({ queryKey: ['write_history'] })
    },
    onError: (error) => {
      console.error('Failed to create write history entry:', error)
    }
  })
}

export function useUpdateWriteHistory() {
  const historyApi = new WriteHistoryApi()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: { id: number, updates: Partial<WritingRevisionHistory> }) => {
      return await historyApi.update(data.id, data.updates)
    },
    onSuccess: (updatedHistory) => {
      // Update individual entry cache
      queryClient.setQueryData(['write_history_entry', updatedHistory.id], updatedHistory)
      
      // Update in list caches
      queryClient.setQueryData(['write_history'], (old: any) => {
        if (!old?.result) return old
        return {
          ...old,
          result: old.result.map((entry: WritingRevisionHistory) => 
            entry.id === updatedHistory.id ? updatedHistory : entry
          )
        }
      })
    },
    onError: (error) => {
      console.error('Failed to update write history entry:', error)
    }
  })
}

export function useDeleteWriteHistory() {
  const historyApi = new WriteHistoryApi()
  const queryClient = useQueryClient()
  const authStore = useAuthStore()

  return useMutation({
    mutationFn: async (historyId: number) => {
      return await historyApi.delete(historyId)
    },
    // Optimistic update
    onMutate: async (historyId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['write_history'] })
      
      // Snapshot previous value
      const previousHistory = queryClient.getQueryData(['write_history'])
      
      // Optimistically remove from cache
      queryClient.setQueryData(['write_history'], (old: any) => {
        if (!old?.result) return old
        return {
          ...old,
          result: old.result.filter((entry: WritingRevisionHistory) => entry.id !== historyId)
        }
      })
      
      // Remove individual entry cache
      queryClient.removeQueries({ queryKey: ['write_history_entry', historyId] })
      
      return { previousHistory }
    },
    onError: (err, historyId, context) => {
      // Rollback on error
      if (context?.previousHistory) {
        queryClient.setQueryData(['write_history'], context.previousHistory)
      }
    },
    onSettled: () => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: ['write_history'] })
    }
  })
}