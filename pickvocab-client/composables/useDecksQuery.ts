import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'
import { type Ref } from 'vue'

export function useDecksQuery(page: Ref<number>, isDemoPage: boolean = false) {
  const store = useAppStore()
  const authStore = useAuthStore()

  const queryKey = computed(() => [
    'all_decks', 
    { 
      page: page.value,
      isDemoPage,
      owner: isDemoPage ? undefined : authStore.currentUser?.id 
    }
  ])

  return useQuery({
    queryKey,
    queryFn: async () => {
      const params = isDemoPage 
        ? { page: page.value, is_demo: true }
        : { page: page.value, owner: authStore.currentUser?.id }
      
      return await store.listDecksPaginated(params)
    },
    staleTime: Infinity, // Never automatically stale
    enabled: computed(() => !isDemoPage || authStore.currentUser?.id !== undefined)
  })
}

export function useInvalidateDecksQuery() {
  const queryClient = useQueryClient()
  
  return {
    invalidateAllDecks: () => queryClient.invalidateQueries({ queryKey: ['all_decks'] }),
    invalidateDecks: (isDemoPage: boolean) => {
      const key = ['all_decks', { isDemoPage }]
      return queryClient.invalidateQueries({ queryKey: key })
    },
    refetchAllDecks: () => queryClient.refetchQueries({ queryKey: ['all_decks'] }),
    refetchDecks: (isDemoPage: boolean) => {
      const key = ['all_decks', { isDemoPage }]
      return queryClient.refetchQueries({ queryKey: key })
    }
  }
}