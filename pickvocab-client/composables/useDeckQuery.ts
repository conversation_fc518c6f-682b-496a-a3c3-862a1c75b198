import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'

export function useDeckQuery(deckId: Ref<number>, page: Ref<number>) {
  const store = useAppStore()
  const authStore = useAuthStore()

  const queryKey = computed(() => [
    'deck', 
    deckId.value,
    page.value
  ])

  return useQuery({
    queryKey,
    queryFn: async () => {
      return await store.getDeck(deckId.value, page.value)
    },
    staleTime: Infinity, // Never automatically stale
    enabled: computed(() => deckId.value !== undefined && deckId.value > 0)
  })
}

export function useInvalidateDeckQuery() {
  const queryClient = useQueryClient()
  
  return {
    invalidateAllDecks: () => queryClient.invalidateQueries({ queryKey: ['deck'] }),
    invalidateDeck: (deckId: number) => {
      const key = ['deck', deckId]
      return queryClient.invalidateQueries({ queryKey: key })
    },
    invalidateDeckPage: (deckId: number, page: number) => {
      const key = ['deck', deckId, page]
      return queryClient.invalidateQueries({ queryKey: key })
    },
    refetchDeck: (deckId: number) => {
      const key = ['deck', deckId]
      return queryClient.refetchQueries({ queryKey: key })
    },
    refetchDeckPage: (deckId: number, page: number) => {
      const key = ['deck', deckId, page]
      return queryClient.refetchQueries({ queryKey: key })
    }
  }
}

/**
 * Hook for managing deck queries with automatic individual card cache population
 * @param deckId - Reactive deck ID
 * @param page - Reactive page number
 * @param populateCardCache - Whether to automatically populate individual card cache
 */
export function useDeckQueryWithCardCachePopulation(
  deckId: Ref<number>, 
  page: Ref<number>,
  populateCardCache: boolean = true
) {
  const queryClient = useQueryClient()
  const deckQuery = useDeckQuery(deckId, page)

  // Automatically populate individual card cache when deck data loads
  if (populateCardCache) {
    watch(() => deckQuery.data.value, (newData) => {
      if (newData?.cards) {
        newData.cards.forEach(card => {
          const cardId = typeof card.id === 'string' ? parseInt(card.id) : card.id
          queryClient.setQueryData(['card', cardId], card)
        })
      }
    }, { immediate: true })
  }

  return deckQuery
}

/**
 * Utility function to populate individual card cache from deck data
 * Useful for components that want manual control over cache population
 */
export function usePopulateDeckCardCache() {
  const queryClient = useQueryClient()

  return {
    populateFromDeck: (deck: Deck) => {
      if (deck.cards) {
        deck.cards.forEach(card => {
          const cardId = typeof card.id === 'string' ? parseInt(card.id) : card.id
          queryClient.setQueryData(['card', cardId], card)
        })
      }
    }
  }
}