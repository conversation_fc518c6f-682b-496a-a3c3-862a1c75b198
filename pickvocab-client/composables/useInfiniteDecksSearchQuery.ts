import { useInfiniteQuery, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'

interface PaginatedDecksResponse {
  results: Deck[]
  count: number
  next: string | null
  previous: string | null
  page: number
}

export function useInfiniteDecksSearchQuery(searchText: Ref<string>) {
  const store = useAppStore()
  const authStore = useAuthStore()

  return useInfiniteQuery({
    queryKey: computed(() => ['decks_search', searchText.value || '__empty__']),
    queryFn: async ({ pageParam = 1 }) => {
      if (!searchText.value.trim()) {
        // For empty search, use listDecksPaginated to get user's notebooks
        return await store.listDecksPaginated({ page: pageParam })
      }
      
      return await store.searchDecksPaginated(searchText.value, pageParam)
    },
    getNextPageParam: (lastPage) => {
      return lastPage.next ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes for search results
    enabled: computed(() => authStore.currentUser?.id !== undefined),
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  })
}

export function useInfiniteDecksSearchQueryWithAbort(searchText: Ref<string>) {
  const store = useAppStore()
  const authStore = useAuthStore()
  const queryClient = useQueryClient()
  
  // Track current abort controller
  let currentController: AbortController | undefined

  const query = useInfiniteQuery({
    queryKey: computed(() => ['decks_search', searchText.value || '__empty__']),
    queryFn: async ({ pageParam = 1 }) => {
      // Cancel previous request
      if (currentController) {
        currentController.abort()
      }
      
      // Create new abort controller
      currentController = new AbortController()
      
      if (!searchText.value.trim()) {
        // For empty search, use listDecksPaginated to get user's notebooks
        try {
          return await store.listDecksPaginated({ page: pageParam })
        } catch (error) {
          if (error instanceof Error && error.name === 'AbortError') {
            return {
              results: [],
              count: 0,
              next: null,
              previous: null,
              page: pageParam
            } as PaginatedDecksResponse
          }
          throw error
        }
      }
      
      try {
        return await store.searchDecksPaginated(
          searchText.value, 
          pageParam, 
          currentController.signal
        )
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          // Return empty results for aborted requests
          return {
            results: [],
            count: 0,
            next: null,
            previous: null,
            page: pageParam
          } as PaginatedDecksResponse
        }
        throw error
      }
    },
    getNextPageParam: (lastPage) => {
      return lastPage.next ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000,
    enabled: computed(() => authStore.currentUser?.id !== undefined),
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  })

  // Watch for search text changes and invalidate query
  watch(searchText, (newSearchText, oldSearchText) => {
    if (newSearchText !== oldSearchText) {
      // Cancel current request
      if (currentController) {
        currentController.abort()
      }
      
      // Invalidate and refetch with new search term
      queryClient.invalidateQueries({ 
        queryKey: ['decks_search', oldSearchText] 
      })
    }
  })

  return {
    ...query,
    // Helper to get flattened results
    allDecks: computed(() => {
      return query.data.value?.pages.flatMap(page => page.results) || []
    }),
    // Helper to get total count
    totalCount: computed(() => {
      return query.data.value?.pages[0]?.count || 0
    }),
    // Helper to cancel current request
    cancelCurrentRequest: () => {
      if (currentController) {
        currentController.abort()
        currentController = undefined
      }
    }
  }
}

export function useInvalidateDecksSearchQuery() {
  const queryClient = useQueryClient()
  
  return {
    invalidateSearch: (searchText?: string) => {
      if (searchText) {
        return queryClient.invalidateQueries({ 
          queryKey: ['decks_search', searchText] 
        })
      } else {
        return queryClient.invalidateQueries({ 
          queryKey: ['decks_search'] 
        })
      }
    },
    removeSearch: (searchText: string) => {
      return queryClient.removeQueries({ 
        queryKey: ['decks_search', searchText] 
      })
    },
    clearAllSearches: () => {
      return queryClient.removeQueries({ 
        queryKey: ['decks_search'] 
      })
    }
  }
}