import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'

export function useCardsQuery(page: Ref<number>, isDemoPage: boolean = false) {
  const store = useAppStore()
  const authStore = useAuthStore()

  const queryKey = computed(() => [
    'all_cards', 
    { 
      page: page.value, 
      isDemoPage,
      owner: isDemoPage ? undefined : authStore.currentUser?.id 
    }
  ])

  return useQuery({
    queryKey,
    queryFn: async () => {
      const params = isDemoPage 
        ? { page: page.value, decks__is_demo: true }
        : { page: page.value, owner: authStore.currentUser?.id }
      
      return await store.listGenericCards(params)
    },
    staleTime: Infinity, // Never automatically stale
    enabled: computed(() => !isDemoPage || authStore.currentUser?.id !== undefined)
  })
}

export function useInvalidateCardsQuery() {
  const queryClient = useQueryClient()
  
  return {
    invalidateAllCards: () => queryClient.invalidateQueries({ queryKey: ['all_cards'] }),
    invalidateCardsPage: (page: number, isDemoPage: boolean) => {
      const key = ['all_cards', { page, isDemoPage }]
      return queryClient.invalidateQueries({ queryKey: key })
    },
    refetchAllCards: () => queryClient.refetchQueries({ queryKey: ['all_cards'] }),
    refetchCardsPage: (page: number, isDemoPage: boolean) => {
      const key = ['all_cards', { page, isDemoPage }]
      return queryClient.refetchQueries({ queryKey: key })
    }
  }
}

/**
 * Hook for managing card list queries with automatic individual card cache population
 * @param page - Reactive page number
 * @param isDemoPage - Whether this is for demo cards
 * @param populateIndividualCache - Whether to automatically populate individual card cache
 */
export function useCardsQueryWithCachePopulation(
  page: Ref<number>, 
  isDemoPage: boolean = false,
  populateIndividualCache: boolean = true
) {
  const queryClient = useQueryClient()
  const cardsQuery = useCardsQuery(page, isDemoPage)

  // Automatically populate individual card cache when list data loads
  if (populateIndividualCache) {
    watch(() => cardsQuery.data.value, (newData) => {
      if (newData?.result) {
        newData.result.forEach(card => {
          const cardId = typeof card.id === 'string' ? parseInt(card.id) : card.id
          queryClient.setQueryData(['card', cardId], card)
        })
      }
    }, { immediate: true })
  }

  return cardsQuery
}

/**
 * Utility function to populate individual card cache from list data
 * Useful for components that want manual control over cache population
 */
export function usePopulateCardCache() {
  const queryClient = useQueryClient()

  return {
    populateFromCardList: (cards: Card[]) => {
      cards.forEach(card => {
        const cardId = typeof card.id === 'string' ? parseInt(card.id) : card.id
        queryClient.setQueryData(['card', cardId], card)
      })
    }
  }
}