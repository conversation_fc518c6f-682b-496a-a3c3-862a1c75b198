<script setup lang="ts">
import ReviewCardBackView from '~/components/app/reviews/ReviewCardBackView.vue';
import ReviewCardFrontView from '~/components/app/reviews/ReviewCardFrontView.vue';
import MultipleChoiceQuestionView from '~/components/app/reviews/MultipleChoiceQuestionView.vue';
import TypeInQuestionView from '~/components/app/reviews/TypeInQuestionView.vue';
import CreativeWritingView from '~/components/app/reviews/CreativeWritingView.vue';
import DangerAlert from '~/components/app/utils/DangerAlert.vue';
import ReviewSummaryView from '~/components/app/reviews/ReviewSummaryView.vue';
import type { LocalSessionReviewItem, MCQItem, TypeInQuestionItem } from '~/components/app/reviews/reviewTypes';
import type { CreativeWritingItem } from '~/components/app/reviews/CreativeWritingTypes';
import type { Card } from '~/utils/card';
import type { Review } from '~/utils/review';
import type { ReviewSessionSummary } from '~/components/app/reviews/reviewSummaryTypes';
import { calculateSessionSummary } from '~/utils/reviewAnalytics';

useSeoMeta({
  title: 'Review | Pickvocab'
});

definePageMeta({
  layout: 'app',
});

enum CardState {
  Front,
  Back
}

const store = useAppStore();

const route = useRoute();
const router = useRouter();
const review: Ref<Review | undefined> = ref();
const currentCardIdx: Ref<number> = ref(0);
const currentCardState: Ref<CardState> = ref(CardState.Front);
const isLoading = ref(false);

// Review completion state
const isReviewCompleted = ref(false);
const sessionSummary = ref<ReviewSessionSummary | null>(null);

// Get local session review items from composable
const { localSessionReviewItems, hasLocalSessionReviewItems, clearLocalSessionReviewItems } = useReviewSession();

// Computed property to determine current item type
const currentItem = computed(() => {
  if (localSessionReviewItems.value && currentCardIdx.value < localSessionReviewItems.value.length) {
    return localSessionReviewItems.value[currentCardIdx.value];
  }
  return null;
});

// Computed property to determine if we're using local session items
const usingLocalSession = computed(() => {
  return hasLocalSessionReviewItems.value;
});

// Computed property for total items count
const totalItems = computed(() => {
  if (usingLocalSession.value) {
    return localSessionReviewItems.value.length;
  }
  return review.value?.cards.length || 0;
});

// Computed property to get the original card for MCQ items
const originalCardForMCQ = computed(() => {
  if (currentItem.value && currentItem.value.type === 'mcq') {
    const mcqData = currentItem.value.data as MCQItem;
    if (mcqData.originalCardId && review.value) {
      // Find the original card in the review - ensure consistent string comparison
      const reviewCard = review.value.cards.find(rc => String(rc.card.id) === String(mcqData.originalCardId));
      return reviewCard?.card;
    }
  }
  return null;
});

// Computed property to get the original card for TypeIn items
const originalCardForTypeIn = computed(() => {
  if (currentItem.value && currentItem.value.type === 'type-in') {
    const typeInData = currentItem.value.data as TypeInQuestionItem;
    if (typeInData.originalCardId && review.value) {
      const reviewCard = review.value.cards.find(rc => String(rc.card.id) === String(typeInData.originalCardId));
      return reviewCard?.card;
    }
  }
  return null;
});

// Computed property to get the original card for Creative Writing items
const originalCardForCreativeWriting = computed(() => {
  if (currentItem.value && currentItem.value.type === 'creative-writing') {
    const creativeWritingData = currentItem.value.data as CreativeWritingItem;
    if (creativeWritingData.originalCardId && review.value) {
      const reviewCard = review.value.cards.find(rc => String(rc.card.id) === String(creativeWritingData.originalCardId));
      return reviewCard?.card;
    }
  }
  return null;
});

watch(() => route.params.id, async (value) => {
  if (Array.isArray(value)) throw new Error('Unexpected');
  
  isLoading.value = true;
  try {
    // Always fetch the review for scoring purposes, even with local session items
    review.value = await store.getReview(Number(value));
  } finally {
    isLoading.value = false;
  }
}, { immediate: true });

function showDefinition() {
  currentCardState.value = CardState.Back;
}

function updateScore(delta: number) {
  if (usingLocalSession.value) {
    // For local session items, we need to handle scoring differently
    const currentItemData = currentItem.value;
    if (currentItemData && review.value) {
      if (currentItemData.type === 'basic') {
        // For basic cards in local session, find and update the score
        const card = currentItemData.data as Card;
        const reviewCard = review.value.cards.find(rc => String(rc.card.id) === String(card.id));
        if (reviewCard) {
          reviewCard.deltaScore = delta;
        }
      } else if (currentItemData.type === 'mcq') {
        // For MCQ items, find the original card and update its score
        const mcqData = currentItemData.data as MCQItem;
        if (mcqData.originalCardId) {
          const reviewCard = review.value.cards.find(rc => String(rc.card.id) === String(mcqData.originalCardId));
          if (reviewCard) {
            reviewCard.deltaScore = delta;
          }
        }
      } else if (currentItemData.type === 'type-in') {
       const typeInData = currentItemData.data as TypeInQuestionItem;
       if (typeInData.originalCardId) {
         const reviewCard = review.value.cards.find(rc => String(rc.card.id) === String(typeInData.originalCardId));
         if (reviewCard) {
           reviewCard.deltaScore = delta;
         }
       }
      } else if (currentItemData.type === 'creative-writing') {
        // For Creative Writing items, find the original card and update its score
        const creativeWritingData = currentItemData.data as CreativeWritingItem;
        if (creativeWritingData.originalCardId) {
          const reviewCard = review.value.cards.find(rc => String(rc.card.id) === String(creativeWritingData.originalCardId));
          if (reviewCard) {
            reviewCard.deltaScore = delta;
          }
        }
      }
    }
  } else {
    // Regular review flow
    review.value!.cards[currentCardIdx.value].deltaScore = delta;
  }

  currentCardIdx.value += 1;
  currentCardState.value = CardState.Front;

  if (currentCardIdx.value >= totalItems.value) {
    // Calculate session summary
    if (review.value) {
      sessionSummary.value = calculateSessionSummary(
        review.value,
        localSessionReviewItems.value as LocalSessionReviewItem[] | undefined
      );
    }
    
    // Show summary view instead of alert
    isReviewCompleted.value = true;
    
    // Clear local session items when review is complete
    clearLocalSessionReviewItems();
    
    // Only update backend scores if we have a review object with scored cards
    if (review.value && review.value.cards.some(card => card.deltaScore !== undefined)) {
      store.updateScoreReview(review.value!);
    }
  }
}

// Action handlers for summary view
function handleContinueLearning() {
  router.push({ path: '/app' });
}

function handleReviewAgain() {
  // Reset state and restart review
  currentCardIdx.value = 0;
  currentCardState.value = CardState.Front;
  isReviewCompleted.value = false;
  sessionSummary.value = null;
}

</script>

<template>
  <NuxtLayout name="app">
    <!-- Review Summary View -->
    <ReviewSummaryView 
      v-if="isReviewCompleted && sessionSummary"
      :session-summary="sessionSummary"
      @continue-learning="handleContinueLearning"
      @review-again="handleReviewAgain"
    />
    
    <!-- Review Flow (only show when not completed) -->
    <template v-else>
      <!-- Local Session Review Items (AI + Basic mixed) -->
    <template v-if="usingLocalSession && currentCardIdx < totalItems">
      <template v-if="currentItem">
        <!-- MCQ Item -->
        <MultipleChoiceQuestionView 
          v-if="currentItem.type === 'mcq'"
          :mcq-data="currentItem.data as MCQItem"
          :original-card="originalCardForMCQ || undefined"
          @answer="updateScore"
        />

       <!-- Type-in Item -->
       <TypeInQuestionView
         v-else-if="currentItem.type === 'type-in'"
         :key="(currentItem.data as TypeInQuestionItem).id"
         :question-data="currentItem.data as TypeInQuestionItem"
         :original-card="originalCardForTypeIn || undefined"
         @answer="updateScore"
       />

        <!-- Creative Writing Item -->
        <CreativeWritingView
          v-else-if="currentItem.type === 'creative-writing'"
          :key="(currentItem.data as CreativeWritingItem).id"
          :writing-data="currentItem.data as CreativeWritingItem"
          :original-card="originalCardForCreativeWriting || undefined"
          @answer="updateScore"
        />
        
        <!-- Basic Card Item -->
        <template v-else-if="currentItem.type === 'basic'">
          <ReviewCardFrontView
            v-if="currentCardState === CardState.Front"
            :card="currentItem.data as Card"
            @show-definition="showDefinition()"
          />
          <ReviewCardBackView 
            v-else
            :card="currentItem.data as Card" 
            @answer="updateScore"
          />
        </template>
      </template>
    </template>
    
    <!-- Regular Review Flow (Backend cards only) -->
    <template v-else-if="review && review.cards.length > 0">
      <template v-if="currentCardIdx < review.cards.length">
        <ReviewCardFrontView 
          v-if="currentCardState === CardState.Front" 
          :card="review.cards[currentCardIdx].card"
          @show-definition="showDefinition()"
        />
        <ReviewCardBackView 
          v-else
          :card="review.cards[currentCardIdx].card" 
          @answer="updateScore"
        />
      </template>
    </template>
    
      <!-- Error/Loading States -->
      <div v-else-if="!isLoading && !usingLocalSession" class="sm:ml-64 pt-14 pl-10 pr-10">
        <DangerAlert
          class="mt-10"
          :label="'Error'"
          :message="'No cards available for review. Please create some cards first.'"
        />
      </div>
    </template>

  </NuxtLayout>
</template>

<style scoped></style>
