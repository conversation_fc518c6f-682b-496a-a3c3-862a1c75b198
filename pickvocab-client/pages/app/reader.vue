<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { Button } from '@/components/ui/button';
import RecentBooksDisplay from '@/components/app/epubReader/RecentBooksDisplay.vue';
import { useRecentBooks } from '~/components/app/epubReader/useRecentBooks';
import { PlusCircleIcon } from 'lucide-vue-next'; // XIcon removed
// Assume useBookReaderStore will be created in a subsequent task
import { useBookReaderStore } from '~/stores/bookReaderStore';

definePageMeta({
  layout: 'app',
});

useSeoMeta({
  title: 'Reading Hub | Pickvocab'
});

const router = useRouter();
const bookReaderStore = useBookReaderStore(); // Will be uncommented when store is available

const { getRecentBookById, addOrUpdateRecentBook, removeRecentBook } = useRecentBooks();

// Storage management for fallback browsers
const showStorageInfo = ref(false);
const storageUsed = ref(0);
const storageLimit = ref(500 * 1024 * 1024); // 500MB
const supportsFileSystemAccess = 'showOpenFilePicker' in window;

// Load storage info for fallback browsers
onMounted(async () => {
  if (localStorage.getItem(onboardingKey) !== 'true') {
    showReaderOnboarding.value = true;
  }
  
  if (!supportsFileSystemAccess) {
    try {
      // Give the database time to initialize properly
      await nextTick();
      const { getTotalStorageUsed, STORAGE_LIMITS } = await import('~/components/app/epubReader/recentBooksDB');
      
      // Wait a bit more for database migrations to complete
      setTimeout(async () => {
        try {
          storageUsed.value = await getTotalStorageUsed();
          storageLimit.value = STORAGE_LIMITS.MAX_TOTAL_SIZE;
        } catch (error) {
          console.warn('Could not load storage info after delay:', error);
          // Set defaults if storage info fails
          storageUsed.value = 0;
          storageLimit.value = STORAGE_LIMITS.MAX_TOTAL_SIZE;
        }
      }, 100);
    } catch (error) {
      console.warn('Could not load storage info:', error);
    }
  }
});

const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return `${bytes}B`;
  if (bytes < 1024 * 1024) return `${Math.round(bytes / 1024)}KB`;
  return `${Math.round(bytes / 1024 / 1024)}MB`;
};

const storagePercentage = computed(() => {
  return Math.round((storageUsed.value / storageLimit.value) * 100);
});

const showReaderOnboarding = ref(false);
const onboardingKey = 'pickvocab_readerPageOnboardingDismissed';

const dismissReaderOnboarding = () => {
  localStorage.setItem(onboardingKey, 'true');
  showReaderOnboarding.value = false;
};

async function openFileWithPicker(): Promise<FileSystemFileHandle | File | null> {
  if ('showOpenFilePicker' in window) {
    // Use File System Access API (Chrome, Edge)
    try {
      const [handle] = await window.showOpenFilePicker({
        types: [{ description: 'EPUB Files', accept: { 'application/epub+zip': ['.epub'] } }],
        multiple: false,
      });
      return handle;
    } catch (err) {
      if ((err as DOMException).name === 'AbortError') {
        console.log('User cancelled file picker.');
      } else {
        console.error('Error picking file:', err);
      }
      return null;
    }
  } else {
    // Fallback for Firefox, Safari, and other browsers
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.epub,application/epub+zip';
      input.style.display = 'none';
      
      input.onchange = (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        document.body.removeChild(input);
        resolve(file || null);
      };
      
      input.oncancel = () => {
        document.body.removeChild(input);
        resolve(null);
      };
      
      document.body.appendChild(input);
      input.click();
    });
  }
}

const openFileAndProcess = async () => {
  const result = await openFileWithPicker();
  if (result) {
    try {
      let file: File;
      let handle: FileSystemFileHandle | null = null;
      
      if (result instanceof File) {
        // Fallback mode - direct File object
        file = result;
        handle = null;
      } else {
        // File System Access API - FileSystemFileHandle
        file = await result.getFile();
        handle = result;
      }
      
      const arrayBuffer = await file.arrayBuffer();
      bookReaderStore.setCurrentBookToRead({ data: arrayBuffer, name: file.name, handle: handle });
      
      console.log('EPUB file loaded via picker, ready to pass to store:', file.name);
      
      // Update storage info for fallback browsers
      if (!supportsFileSystemAccess && file.size) {
        storageUsed.value += file.size;
      }
      
      router.push('/app/read-book');
    } catch (error) {
      console.error('Error processing file from picker:', error);
      // TODO: Show error to user
    }
  }
};

async function handleRecentBookSelect(bookId: string) {
  const entry = await getRecentBookById(bookId);

  if (!entry) {
    console.error('Recent book entry not found for ID:', bookId);
    alert('Could not find recent book entry. It might have been removed.');
    return;
  }

  // Check if this entry supports File System Access API
  if (entry.supportsFileSystemAccess && entry.fileHandle) {
    // Use File System Access API for supported browsers
    async function getFileFromHandle(handle: FileSystemFileHandle, entryId: string): Promise<File | null> {
      try {
        if (await handle.queryPermission({ mode: 'read' }) !== 'granted') {
          if (await handle.requestPermission({ mode: 'read' }) !== 'granted') {
            console.warn('Read permission denied for file handle:', handle.name);
            alert(`Permission to read the file "${handle.name}" was denied. Please grant permission to open it again, or it will be removed from recent books.`);
            await removeRecentBook(entryId);
            return null;
          }
        }
        return await handle.getFile();
      } catch (error) {
        console.error('Error accessing file from handle:', handle.name, error);
        alert(`Could not access the file "${handle.name}". It might have been moved or deleted. It will be removed from recent books.`);
        await removeRecentBook(entryId);
        return null;
      }
    }

    const file = await getFileFromHandle(entry.fileHandle, entry.id);

    if (file) {
      try {
        const arrayBuffer = await file.arrayBuffer();
        bookReaderStore.setCurrentBookToRead({ data: arrayBuffer, name: file.name, handle: entry.fileHandle });
        console.log(`Successfully prepared to re-open book via store: ${entry.filename}`);
        router.push('/app/read-book');
        await addOrUpdateRecentBook({ ...entry, lastOpened: Date.now() });
      } catch (readError) {
        console.error('Error reading file for re-opening:', readError);
        alert('An error occurred while trying to read the book file.');
      }
    } else {
      console.warn(`Failed to get file for ${entry.filename}. It might have been removed from recent list.`);
    }
  } else {
    // Fallback browsers - check if we have stored file data
    if (entry.fileData) {
      try {
        // Use stored file data - no need for user to reselect!
        bookReaderStore.setCurrentBookToRead({ 
          data: entry.fileData, 
          name: entry.filename, 
          handle: null 
        });
        console.log(`Successfully opened book from stored data: ${entry.filename}`);
        router.push('/app/read-book');
        await addOrUpdateRecentBook({ ...entry, lastOpened: Date.now() });
      } catch (readError) {
        console.error('Error reading stored file data:', readError);
        alert('An error occurred while trying to read the stored book data. Please try selecting the file again.');
        // Fall back to file picker if stored data fails
        promptForFileReselection(entry);
      }
    } else {
      // No stored data - prompt user to reselect the file
      promptForFileReselection(entry);
    }
  }
}

async function promptForFileReselection(entry: any) {
  alert(`Please select "${entry.filename}" to continue reading.`);
  const result = await openFileWithPicker();
  if (result && result instanceof File && result.name === entry.filename) {
    try {
      const arrayBuffer = await result.arrayBuffer();
      bookReaderStore.setCurrentBookToRead({ data: arrayBuffer, name: result.name, handle: null });
      console.log(`Successfully re-opened book via fallback picker: ${entry.filename}`);
      router.push('/app/read-book');
      await addOrUpdateRecentBook({ ...entry, lastOpened: Date.now() });
    } catch (readError) {
      console.error('Error reading re-selected file:', readError);
      alert('An error occurred while trying to read the book file.');
    }
  } else if (result) {
    alert('The selected file does not match the expected book. Please select the correct file or remove this entry from recent books.');
  }
}
</script>

<template>
  <NuxtLayout name="app">
    <div class="sm:ml-64 mt-14 pt-10 pl-10 pr-10 xl:pr-48 flex flex-col h-full bg-background text-foreground">
      <main class="flex-grow w-full bg-card">
        <div class="flex flex-col items-center justify-start p-8 h-full overflow-y-auto">
          <div class="w-full max-w-4xl">
            <!-- Onboarding Message -->
            <Transition name="fade">
              <div
                v-if="showReaderOnboarding"
                class="mb-6 p-4 bg-blue-500 text-white rounded-lg shadow-lg flex items-center justify-between"
                role="alert"
              >
                <p class="text-sm">
                  Welcome to your Reading Hub! Click 'Open New Book' to upload an EPUB, or select a recent book to continue reading. While reading, select any word or phrase to look it up
                </p>
                <button
                  @click="dismissReaderOnboarding"
                  class="ml-4 px-3 py-1 bg-blue-700 hover:bg-blue-800 rounded text-sm font-semibold whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-blue-300"
                  aria-label="Dismiss onboarding message"
                >
                  Got it!
                </button>
              </div>
            </Transition>

            <!-- Storage Info for Fallback Browsers -->
            <div v-if="!supportsFileSystemAccess && storageUsed > 0" class="mb-6 p-4 bg-slate-50 border border-slate-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-medium text-slate-700">Book Storage</h3>
                <button 
                  @click="showStorageInfo = !showStorageInfo"
                  class="text-xs text-slate-500 hover:text-slate-700"
                >
                  {{ showStorageInfo ? 'Hide' : 'Details' }}
                </button>
              </div>
              
              <div class="w-full bg-slate-200 rounded-full h-2 mb-2">
                <div 
                  class="h-2 rounded-full transition-all duration-300"
                  :class="storagePercentage > 80 ? 'bg-red-500' : storagePercentage > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                  :style="{ width: `${storagePercentage}%` }"
                ></div>
              </div>
              
              <div class="flex justify-between text-xs text-slate-600">
                <span>{{ formatFileSize(storageUsed) }} used</span>
                <span>{{ formatFileSize(storageLimit) }} limit</span>
              </div>
              
              <div v-if="showStorageInfo" class="mt-3 text-xs text-slate-600 border-t border-slate-200 pt-3">
                <p class="mb-2">Your browser doesn't support persistent file access, so books are stored locally for instant access.</p>
                <p v-if="storagePercentage > 80" class="text-red-600 font-medium">
                  ⚠ Storage almost full. Older books may be automatically cleaned up.
                </p>
              </div>
            </div>

            <!-- "Open New Book" Button -->
            <div class="flex mb-8">
              <Button @click="openFileAndProcess" size="lg" variant="outline" class="mx-auto sm:mx-0 px-6">
                <PlusCircleIcon class="mr-2 h-5 w-5" />
                Open New Book (.epub)
              </Button>
            </div>
            <!-- Recent Books Display -->
            <RecentBooksDisplay @select-book="handleRecentBookSelect" class="w-full max-w-4xl" />
          </div>
        </div>
      </main>
    </div>
  </NuxtLayout>
</template>

<style scoped>
/* Animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Ensure other styles are not conflicting */
</style>